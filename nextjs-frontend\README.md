# DADP Frontend - Next.js + TypeScript

This is the Next.js + TypeScript frontend for the Data Annotation & Delivery Platform (DADP), converted from the original HTML templates while preserving the existing CSS styles and functionality.

## 🚀 Features

- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling (preserving original design)
- **React Query** for server state management
- **Authentication** with JWT tokens
- **Role-based access control** (Admin, Auditor, Annotator)
- **Responsive design** maintained from original templates
- **Component-based architecture**

## 📁 Project Structure

```
nextjs-frontend/
├── app/                          # Next.js App Router
│   ├── (auth)/                   # Authentication pages
│   │   └── login/
│   ├── (dashboard)/              # Protected dashboard pages
│   │   ├── admin/
│   │   ├── auditor/
│   │   └── annotator/
│   ├── globals.css               # Global styles (converted from style.css)
│   ├── layout.tsx                # Root layout
│   ├── page.tsx                  # Landing page
│   └── providers.tsx             # App providers
├── components/                   # Reusable components
│   ├── layout/
│   ├── navigation/
│   └── ui/
├── lib/                          # Utilities and configurations
│   ├── api-client.ts             # API client with axios
│   ├── auth-context.tsx          # Authentication context
│   └── utils.ts                  # Utility functions
├── types/                        # TypeScript type definitions
│   └── index.ts
└── public/                       # Static assets
    └── img/                      # Images from static/img
```

## 🎨 Design System

The frontend maintains the original design system with:

- **Color Palette**: Primary (#4a6fa5), Secondary (#6d8cc7), Accent (#ffa500)
- **Typography**: Poppins font family
- **Components**: Cards, buttons, forms, alerts, navigation
- **Responsive Design**: Mobile-first approach
- **Icons**: Bootstrap Icons and Lucide React

## 🔧 Setup Instructions

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Backend API running on `http://localhost:5000`

### Installation

1. **Navigate to the frontend directory:**
   ```bash
   cd nextjs-frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Create environment file:**
   ```bash
   cp .env.example .env.local
   ```

4. **Configure environment variables:**
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:5000
   NEXTAUTH_SECRET=your-secret-key
   NEXTAUTH_URL=http://localhost:3000
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. **Open your browser:**
   Navigate to `http://localhost:3000`

## 🔐 Authentication

The frontend uses JWT-based authentication with the following flow:

1. User logs in with username/password
2. Backend returns JWT token
3. Token is stored in localStorage
4. Token is included in API requests via axios interceptors
5. Protected routes check authentication status

### User Roles

- **Admin**: Full access to dashboard, user management, data connectors
- **Auditor**: Access to audit tasks and review functionality  
- **Annotator**: Access to annotation tasks and tools

## 🌐 API Integration

The frontend communicates with the backend API using:

- **Axios** for HTTP requests
- **React Query** for caching and state management
- **TypeScript interfaces** for type safety
- **Error handling** with toast notifications

### API Client Structure

```typescript
// Authentication
api.auth.login(credentials)
api.auth.logout()
api.auth.me()

// User Management
api.users.list()
api.users.create(userData)
api.users.update(id, userData)

// File Management
api.files.browse(path)
api.files.upload(formData)

// Annotations
api.annotations.list()
api.annotations.get(id)
api.annotations.save(id, data)

// Audits
api.audits.list()
api.audits.approve(id, feedback)
api.audits.reject(id, feedback)
```

## 📱 Pages Overview

### Public Pages
- **Landing Page** (`/`) - Marketing page with features and contact info
- **Login Page** (`/login`) - User authentication

### Admin Pages
- **Dashboard** (`/admin/dashboard`) - Data connectors and settings
- **User Management** (`/admin/users`) - Manage platform users

### Auditor Pages  
- **Dashboard** (`/auditor/dashboard`) - Audit overview and pending tasks
- **Available Tasks** (`/auditor/tasks`) - List of tasks to review
- **History** (`/auditor/history`) - Audit history

### Annotator Pages
- **Dashboard** (`/annotator/dashboard`) - Task overview and progress
- **Annotation Tool** (`/annotator/annotate/[id]`) - Image annotation interface
- **Task Review** (`/annotator/review/[id]`) - Review completed annotations

## 🎯 Key Components

### Layout Components
- `DashboardLayout` - Protected layout with navigation and role checking
- `Navbar` - Responsive navigation with role-based menu items
- `Footer` - Simple footer component

### UI Components
- `Alert` - Flash messages and notifications
- `Button` - Reusable button component with variants
- `Modal` - Modal dialogs for forms and confirmations

### Feature Components
- Data connectors (NAS, Google Drive)
- File browser and management
- Annotation tools and canvas
- User management forms

## 🔄 State Management

- **Authentication State**: React Context for user session
- **Server State**: React Query for API data caching
- **Local State**: React hooks (useState, useReducer)
- **Form State**: React Hook Form for complex forms

## 🚀 Deployment

### Build for Production

```bash
npm run build
npm start
```

### Environment Variables for Production

```env
NEXT_PUBLIC_API_URL=https://your-api-domain.com
NEXTAUTH_SECRET=your-production-secret
NEXTAUTH_URL=https://your-frontend-domain.com
```

## 🧪 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler

### Code Style

- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **Tailwind CSS** for styling

## 📝 Migration Notes

### From HTML Templates

The conversion from HTML templates to Next.js components involved:

1. **Template Structure**: Converted Jinja2 templates to React components
2. **Styling**: Migrated CSS to Tailwind classes while preserving design
3. **JavaScript**: Converted jQuery code to React hooks and modern JS
4. **Routing**: Implemented Next.js App Router for navigation
5. **State Management**: Added React Query and Context for state
6. **Type Safety**: Added comprehensive TypeScript types

### Preserved Features

- ✅ All original styling and design
- ✅ Responsive layout and mobile support
- ✅ Role-based navigation and access control
- ✅ Flash messages and notifications
- ✅ Form validation and error handling
- ✅ File management and browsing
- ✅ Data connector functionality

### Enhanced Features

- 🚀 Modern React architecture
- 🚀 TypeScript type safety
- 🚀 Improved performance with Next.js
- 🚀 Better developer experience
- 🚀 Component reusability
- 🚀 Automated testing capabilities

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for new features
3. Update documentation for significant changes
4. Test across different user roles
5. Ensure responsive design is maintained

## 📞 Support

For questions or issues with the frontend:

1. Check the browser console for errors
2. Verify API connectivity
3. Ensure proper environment variables
4. Review authentication flow
5. Contact the development team

---

**Note**: This frontend is designed to work with the existing DADP backend API. Ensure the backend is running and accessible before starting the frontend development server.
