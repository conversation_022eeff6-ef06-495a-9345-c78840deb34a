'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface Dataset {
  id: string;
  name: string;
  folder_path: string;
  total_batches: number;
  completed_batches: number;
  progress: number;
}

interface BatchInfo {
  batch_id: string;
  status: 'pending' | 'in_progress' | 'completed';
  auditor: string;
  completion_date?: string;
}

export default function AuditorTrackingPage() {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<string>('');
  const [batches, setBatches] = useState<BatchInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [merging, setMerging] = useState(false);
  const [storageDestination, setStorageDestination] = useState('current');

  useEffect(() => {
    fetchDatasets();
  }, []);

  useEffect(() => {
    if (selectedDataset) {
      fetchBatches(selectedDataset);
    }
  }, [selectedDataset]);

  const fetchDatasets = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockDatasets: Dataset[] = [
        {
          id: 'dataset1',
          name: 'Medical Documents Dataset',
          folder_path: '/data/medical_docs',
          total_batches: 10,
          completed_batches: 8,
          progress: 80
        },
        {
          id: 'dataset2',
          name: 'Legal Documents Dataset',
          folder_path: '/data/legal_docs',
          total_batches: 15,
          completed_batches: 15,
          progress: 100
        },
        {
          id: 'dataset3',
          name: 'Financial Reports Dataset',
          folder_path: '/data/financial_reports',
          total_batches: 5,
          completed_batches: 3,
          progress: 60
        }
      ];
      
      setDatasets(mockDatasets);
    } catch (error) {
      toast.error('Failed to fetch datasets');
    } finally {
      setLoading(false);
    }
  };

  const fetchBatches = async (datasetId: string) => {
    try {
      // Mock batch data - replace with actual API call
      const mockBatches: BatchInfo[] = [
        { batch_id: 'batch_001', status: 'completed', auditor: 'john_doe', completion_date: '2024-01-15' },
        { batch_id: 'batch_002', status: 'completed', auditor: 'jane_smith', completion_date: '2024-01-16' },
        { batch_id: 'batch_003', status: 'in_progress', auditor: 'bob_wilson' },
        { batch_id: 'batch_004', status: 'pending', auditor: '' },
        { batch_id: 'batch_005', status: 'pending', auditor: '' }
      ];
      
      setBatches(mockBatches);
    } catch (error) {
      toast.error('Failed to fetch batch information');
    }
  };

  const mergeDataset = async () => {
    if (!selectedDataset) {
      toast.error('Please select a dataset');
      return;
    }

    const dataset = datasets.find(d => d.id === selectedDataset);
    if (!dataset || dataset.completed_batches !== dataset.total_batches) {
      toast.error('All batches must be completed before merging');
      return;
    }

    setMerging(true);
    try {
      // Mock merge operation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast.success(`Dataset merged successfully to ${storageDestination}`);
    } catch (error) {
      toast.error('Failed to merge dataset');
    } finally {
      setMerging(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress === 100) return 'bg-green-500';
    if (progress >= 70) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const selectedDatasetInfo = datasets.find(d => d.id === selectedDataset);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Loading auditor tracking data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fas fa-chart-line mr-3 text-primary-600"></i>
            Auditor Tracking
          </h1>
          <p className="text-gray-600">
            Monitor dataset completion progress and manage batch auditing
          </p>
        </div>

        {/* Dataset Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Select Dataset</h2>
          </div>
          <div className="p-6">
            <select
              value={selectedDataset}
              onChange={(e) => setSelectedDataset(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="">Choose a dataset...</option>
              {datasets.map((dataset) => (
                <option key={dataset.id} value={dataset.id}>
                  {dataset.name} ({dataset.completed_batches}/{dataset.total_batches} completed)
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Dataset Overview */}
        {selectedDatasetInfo && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i className="fas fa-database text-blue-600"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Batches</p>
                  <p className="text-2xl font-bold text-gray-900">{selectedDatasetInfo.total_batches}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <i className="fas fa-check-circle text-green-600"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-gray-900">{selectedDatasetInfo.completed_batches}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <i className="fas fa-percentage text-yellow-600"></i>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Progress</p>
                  <p className="text-2xl font-bold text-gray-900">{selectedDatasetInfo.progress}%</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        {selectedDatasetInfo && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Dataset Progress</h3>
            </div>
            <div className="p-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  {selectedDatasetInfo.name}
                </span>
                <span className="text-sm text-gray-500">
                  {selectedDatasetInfo.completed_batches} of {selectedDatasetInfo.total_batches} batches
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${getProgressColor(selectedDatasetInfo.progress)}`}
                  style={{ width: `${selectedDatasetInfo.progress}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Batch Details */}
        {batches.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Batch Details</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Batch ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Auditor
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Completion Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {batches.map((batch) => (
                    <tr key={batch.batch_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {batch.batch_id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(batch.status)}`}>
                          {batch.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {batch.auditor || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {batch.completion_date || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Merge Actions */}
        {selectedDatasetInfo && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Dataset Actions</h3>
            </div>
            <div className="p-6">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Storage Destination
                  </label>
                  <select
                    value={storageDestination}
                    onChange={(e) => setStorageDestination(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="current">
                      <i className="fas fa-hdd mr-2"></i>Current Storage
                    </option>
                    <option value="google-drive">
                      <i className="fab fa-google-drive mr-2"></i>Google Drive
                    </option>
                    <option value="dropbox" disabled>
                      <i className="fab fa-dropbox mr-2"></i>Dropbox (Coming Soon)
                    </option>
                    <option value="database" disabled>
                      <i className="fas fa-database mr-2"></i>Database (Coming Soon)
                    </option>
                  </select>
                </div>
                <div className="flex-shrink-0">
                  <button
                    onClick={mergeDataset}
                    disabled={
                      !selectedDatasetInfo ||
                      selectedDatasetInfo.completed_batches !== selectedDatasetInfo.total_batches ||
                      selectedDatasetInfo.total_batches === 0 ||
                      merging
                    }
                    className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {merging ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Merging...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-file-code mr-2"></i>
                        Merge JSON Files
                      </>
                    )}
                  </button>
                </div>
              </div>
              
              {selectedDatasetInfo.completed_batches !== selectedDatasetInfo.total_batches && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex">
                    <i className="fas fa-exclamation-triangle text-yellow-400 mr-2 mt-0.5"></i>
                    <p className="text-sm text-yellow-700">
                      All batches must be completed before merging the dataset.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
