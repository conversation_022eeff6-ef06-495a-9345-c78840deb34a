'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  HardDrive, 
  FolderOpen, 
  Settings, 
  Database,
  Plug,
  XCircle,
  CheckCircle,
  AlertCircle,
  Folder
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ConnectionStatus {
  nas_connected: boolean;
  google_drive_connected: boolean;
  auditor_image_folder: string;
}

export default function AdminDashboard() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    nas_connected: false,
    google_drive_connected: false,
    auditor_image_folder: '',
  });
  const [loading, setLoading] = useState(true);
  const [showNasModal, setShowNasModal] = useState(false);
  const [showGoogleDriveModal, setShowGoogleDriveModal] = useState(false);
  const [nasForm, setNasForm] = useState({
    nas_type: 'ftp',
    nas_url: '',
    nas_username: '',
    nas_password: '',
  });
  const [googleDriveForm, setGoogleDriveForm] = useState({
    client_id: '',
    client_secret: '',
    folder_id: '',
  });
  const [auditorFolder, setAuditorFolder] = useState('');

  useEffect(() => {
    fetchConnectionStatus();
  }, []);

  const fetchConnectionStatus = async () => {
    try {
      const [nasResponse, driveResponse] = await Promise.all([
        api.connectors.nas.status(),
        api.connectors.googleDrive.status(),
      ]);

      setConnectionStatus({
        nas_connected: nasResponse.data.data?.connected || false,
        google_drive_connected: driveResponse.data.data?.connected || false,
        auditor_image_folder: nasResponse.data.data?.auditor_folder || '',
      });
      setAuditorFolder(nasResponse.data.data?.auditor_folder || '');
    } catch (error) {
      console.error('Failed to fetch connection status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNasConnect = async () => {
    try {
      const response = await api.connectors.nas.connect(nasForm);
      if (response.data.success) {
        toast.success('NAS connected successfully!');
        setShowNasModal(false);
        fetchConnectionStatus();
      } else {
        toast.error(response.data.message || 'Failed to connect to NAS');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to connect to NAS');
    }
  };

  const handleNasDisconnect = async () => {
    try {
      const response = await api.connectors.nas.disconnect();
      if (response.data.success) {
        toast.success('NAS disconnected successfully!');
        fetchConnectionStatus();
      } else {
        toast.error(response.data.message || 'Failed to disconnect NAS');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to disconnect NAS');
    }
  };

  const handleGoogleDriveConnect = async () => {
    try {
      const response = await api.connectors.googleDrive.configure(googleDriveForm);
      if (response.data.success) {
        toast.success('Google Drive configured successfully!');
        setShowGoogleDriveModal(false);
        fetchConnectionStatus();
      } else {
        toast.error(response.data.message || 'Failed to configure Google Drive');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to configure Google Drive');
    }
  };

  const handleGoogleDriveReset = async () => {
    try {
      const response = await api.connectors.googleDrive.reset();
      if (response.data.success) {
        toast.success('Google Drive connection reset successfully!');
        fetchConnectionStatus();
      } else {
        toast.error(response.data.message || 'Failed to reset Google Drive connection');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to reset Google Drive connection');
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="admin" title="Admin Dashboard">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title="Admin Dashboard">
      <div className="container space-y-6">
        {/* Data Connectors Section */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-xl font-semibold flex items-center">
              <HardDrive className="w-5 h-5 mr-2" />
              Data Connectors
            </h2>
          </div>
          <div className="card-body">
            <p className="text-gray-600 mb-6">
              Connect to your data sources to manage datasets and annotation tasks.
            </p>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Google Drive Connector */}
              <div className="card">
                <div className="card-body text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Database className="w-8 h-8 text-primary-500" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Google Drive Database</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Connect to manage structured annotation data and user information.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-2 mb-4">
                    <button
                      onClick={() => setShowGoogleDriveModal(true)}
                      className="btn btn-primary flex-1"
                    >
                      <Plug className="w-4 h-4 mr-2" />
                      Connect to Google Drive
                    </button>
                    <button
                      onClick={handleGoogleDriveReset}
                      className="btn btn-outline flex-1"
                      disabled={!connectionStatus.google_drive_connected}
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Reset Connection
                    </button>
                  </div>
                  
                  <div className={`text-sm flex items-center justify-center ${
                    connectionStatus.google_drive_connected ? 'text-success-600' : 'text-gray-500'
                  }`}>
                    {connectionStatus.google_drive_connected ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Connected
                      </>
                    ) : (
                      <>
                        <XCircle className="w-4 h-4 mr-1" />
                        Not connected
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* NAS Storage Connector */}
              <div className="card">
                <div className="card-body text-center">
                  <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FolderOpen className="w-8 h-8 text-success-500" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">NAS Storage</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Connect to access and manage image datasets stored on Network Attached Storage.
                  </p>
                  
                  <div className="flex flex-col gap-2 mb-4">
                    <button
                      onClick={() => setShowNasModal(true)}
                      className="btn btn-success"
                    >
                      <Plug className="w-4 h-4 mr-2" />
                      Connect to NAS
                    </button>
                    
                    {connectionStatus.nas_connected && (
                      <button
                        onClick={handleNasDisconnect}
                        className="btn btn-outline"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Disconnect
                      </button>
                    )}
                  </div>
                  
                  <div className={`text-sm flex items-center justify-center ${
                    connectionStatus.nas_connected ? 'text-success-600' : 'text-gray-500'
                  }`}>
                    {connectionStatus.nas_connected ? (
                      <>
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Connected
                      </>
                    ) : (
                      <>
                        <XCircle className="w-4 h-4 mr-1" />
                        Not connected
                      </>
                    )}
                  </div>

                  {connectionStatus.nas_connected && (
                    <div className="mt-4">
                      <button className="btn btn-outline w-full">
                        <Folder className="w-4 h-4 mr-2" />
                        Click here to select annotation dataset
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Auditor Settings Section */}
        <div className="card">
          <div className="card-header flex items-center justify-between">
            <h2 className="text-xl font-semibold flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Auditor Settings
            </h2>
            <span className="px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
              <CheckCircle className="w-4 h-4 inline mr-1" />
              Settings Active
            </span>
          </div>
          <div className="card-body">
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-4">
                Select the folder where auditors will access images for review tasks.
              </h3>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <div className="flex">
                    <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-md">
                      <Folder className="w-4 h-4" />
                    </span>
                    <input
                      type="text"
                      value={auditorFolder}
                      onChange={(e) => setAuditorFolder(e.target.value)}
                      className="form-input rounded-l-none"
                      placeholder="Select auditor image folder"
                      readOnly
                    />
                  </div>
                </div>
                <button
                  className="btn btn-outline"
                  disabled={!connectionStatus.nas_connected}
                >
                  <FolderOpen className="w-4 h-4 mr-2" />
                  Browse
                </button>
              </div>
              
              <div className="mt-2 text-sm text-gray-600 flex items-center">
                {connectionStatus.nas_connected ? (
                  <>
                    <AlertCircle className="w-4 h-4 mr-1" />
                    Click Browse to select a folder from connected NAS
                  </>
                ) : (
                  <>
                    <AlertCircle className="w-4 h-4 mr-1 text-warning-500" />
                    Connect to NAS first to browse folders
                  </>
                )}
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                className="btn btn-primary"
                disabled={!auditorFolder || auditorFolder === connectionStatus.auditor_image_folder}
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* NAS Connection Modal */}
      {showNasModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold">Connect to NAS</h3>
              <button
                onClick={() => setShowNasModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="form-label">NAS Type</label>
                <select
                  value={nasForm.nas_type}
                  onChange={(e) => setNasForm(prev => ({ ...prev, nas_type: e.target.value }))}
                  className="form-select"
                >
                  <option value="ftp">FTP</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">NAS URL</label>
                <input
                  type="text"
                  value={nasForm.nas_url}
                  onChange={(e) => setNasForm(prev => ({ ...prev, nas_url: e.target.value }))}
                  className="form-input"
                  placeholder="Example: ************:6001"
                />
                <p className="text-sm text-gray-600 mt-1">IP address or hostname with port (if needed)</p>
              </div>
              
              <div>
                <label className="form-label">Username</label>
                <input
                  type="text"
                  value={nasForm.nas_username}
                  onChange={(e) => setNasForm(prev => ({ ...prev, nas_username: e.target.value }))}
                  className="form-input"
                />
              </div>
              
              <div>
                <label className="form-label">Password</label>
                <input
                  type="password"
                  value={nasForm.nas_password}
                  onChange={(e) => setNasForm(prev => ({ ...prev, nas_password: e.target.value }))}
                  className="form-input"
                />
              </div>
              
              <div className="alert alert-info">
                <AlertCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  These credentials will be used for the current session only.
                </span>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 p-6 border-t">
              <button
                onClick={() => setShowNasModal(false)}
                className="btn btn-outline"
              >
                Close
              </button>
              <button
                onClick={handleNasConnect}
                className="btn btn-primary"
              >
                <Plug className="w-4 h-4 mr-2" />
                Connect
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Google Drive Connection Modal */}
      {showGoogleDriveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="flex items-center justify-between p-6 border-b">
              <h3 className="text-lg font-semibold">Connect to Google Drive</h3>
              <button
                onClick={() => setShowGoogleDriveModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="form-label">Client ID</label>
                <input
                  type="text"
                  value={googleDriveForm.client_id}
                  onChange={(e) => setGoogleDriveForm(prev => ({ ...prev, client_id: e.target.value }))}
                  className="form-input"
                />
              </div>
              
              <div>
                <label className="form-label">Client Secret</label>
                <input
                  type="text"
                  value={googleDriveForm.client_secret}
                  onChange={(e) => setGoogleDriveForm(prev => ({ ...prev, client_secret: e.target.value }))}
                  className="form-input"
                />
              </div>
              
              <div>
                <label className="form-label">Root Folder ID (optional)</label>
                <input
                  type="text"
                  value={googleDriveForm.folder_id}
                  onChange={(e) => setGoogleDriveForm(prev => ({ ...prev, folder_id: e.target.value }))}
                  className="form-input"
                  placeholder="Google Drive Folder ID"
                />
                <p className="text-sm text-gray-600 mt-1">Leave empty to access all folders</p>
              </div>
              
              <div className="alert alert-info">
                <AlertCircle className="w-4 h-4 mr-2" />
                <span className="text-sm">
                  You'll need to authorize this application in a new browser window after connecting.
                </span>
              </div>
            </div>
            
            <div className="flex justify-end gap-3 p-6 border-t">
              <button
                onClick={() => setShowGoogleDriveModal(false)}
                className="btn btn-outline"
              >
                Close
              </button>
              <button
                onClick={handleGoogleDriveConnect}
                className="btn btn-primary"
              >
                <Plug className="w-4 h-4 mr-2" />
                Configure & Connect
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
