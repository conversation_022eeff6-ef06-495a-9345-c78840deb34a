'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function GoogleAuthErrorPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const error = searchParams.get('error') || 'Unknown authentication error occurred';

  useEffect(() => {
    // Notify opener window (if any) that authentication failed
    if (window.opener && !window.opener.closed) {
      window.opener.postMessage('google-auth-error', '*');
    }
  }, []);

  const handleReturnToDashboard = () => {
    router.push('/admin/dashboard');
  };

  const handleCloseWindow = () => {
    if (window.opener) {
      window.close();
    } else {
      router.push('/admin/dashboard');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
          {/* Header */}
          <div className="bg-red-600 px-6 py-4">
            <div className="flex items-center">
              <i className="fas fa-exclamation-triangle text-white text-xl mr-3"></i>
              <h1 className="text-xl font-semibold text-white">Authentication Error</h1>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-8 text-center">
            {/* Error Icon */}
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-red-100 mb-6">
              <i className="fas fa-times-circle text-red-600 text-4xl"></i>
            </div>

            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Google Drive Connection Failed
            </h2>

            {/* Error Message */}
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <i className="fas fa-exclamation-circle text-red-400 mr-2 mt-0.5"></i>
                <p className="text-sm text-red-700 text-left">
                  {error}
                </p>
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-600 mb-8 leading-relaxed">
              There was a problem connecting to Google Drive. This could be due to:
            </p>

            {/* Error Reasons */}
            <div className="text-left bg-gray-50 rounded-lg p-4 mb-8">
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-start">
                  <i className="fas fa-circle text-gray-400 text-xs mt-2 mr-2"></i>
                  <span>Permission denied or access revoked</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-circle text-gray-400 text-xs mt-2 mr-2"></i>
                  <span>Network connectivity issues</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-circle text-gray-400 text-xs mt-2 mr-2"></i>
                  <span>Google API service temporarily unavailable</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-circle text-gray-400 text-xs mt-2 mr-2"></i>
                  <span>Invalid or expired authentication credentials</span>
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleReturnToDashboard}
                className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors"
              >
                <i className="fas fa-arrow-left mr-2"></i>
                Return to Dashboard
              </button>
              <button
                onClick={handleCloseWindow}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400 transition-colors"
              >
                <i className="fas fa-times mr-2"></i>
                Close Window
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-center text-sm text-gray-500">
              <i className="fas fa-info-circle mr-2"></i>
              <span>Please try again or contact your administrator if the problem persists</span>
            </div>
          </div>
        </div>

        {/* Additional Help */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Need Help?</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p>• Check your internet connection</p>
            <p>• Ensure you have the necessary Google Drive permissions</p>
            <p>• Try refreshing the page and attempting the connection again</p>
            <p>• Contact your system administrator if issues persist</p>
          </div>
        </div>
      </div>
    </div>
  );
}
