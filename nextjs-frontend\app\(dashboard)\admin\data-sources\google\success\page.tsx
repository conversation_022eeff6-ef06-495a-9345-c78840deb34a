'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function GoogleAuthSuccessPage() {
  const router = useRouter();

  useEffect(() => {
    // Notify opener window (if any) that authentication is complete
    if (window.opener && !window.opener.closed) {
      window.opener.postMessage('google-auth-success', '*');
    }

    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      handleReturnToDashboard();
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleReturnToDashboard = () => {
    if (window.opener) {
      window.close();
    } else {
      router.push('/admin/dashboard');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
          {/* Header */}
          <div className="bg-green-600 px-6 py-4">
            <div className="flex items-center">
              <i className="fas fa-check-circle text-white text-xl mr-3"></i>
              <h1 className="text-xl font-semibold text-white">Authentication Successful</h1>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-8 text-center">
            {/* Success Icon */}
            <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-green-100 mb-6">
              <i className="fab fa-google text-green-600 text-4xl"></i>
            </div>

            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Google Drive Connected Successfully
            </h2>

            {/* Success Message */}
            <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
              <div className="flex">
                <i className="fas fa-check-circle text-green-400 mr-2 mt-0.5"></i>
                <p className="text-sm text-green-700 text-left">
                  Your Google Drive account has been successfully connected to the application.
                </p>
              </div>
            </div>

            {/* Description */}
            <p className="text-gray-600 mb-8 leading-relaxed">
              You can now access Google Drive features and sync your data seamlessly. 
              You can close this window and return to the dashboard.
            </p>

            {/* Features Available */}
            <div className="text-left bg-gray-50 rounded-lg p-4 mb-8">
              <h3 className="text-sm font-medium text-gray-900 mb-3">Available Features:</h3>
              <ul className="text-sm text-gray-700 space-y-2">
                <li className="flex items-start">
                  <i className="fas fa-check text-green-500 text-xs mt-2 mr-2"></i>
                  <span>Upload files to Google Drive</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-green-500 text-xs mt-2 mr-2"></i>
                  <span>Access and download files from Drive</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-green-500 text-xs mt-2 mr-2"></i>
                  <span>Sync annotation data automatically</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-green-500 text-xs mt-2 mr-2"></i>
                  <span>Backup and restore functionality</span>
                </li>
              </ul>
            </div>

            {/* Action Button */}
            <button
              onClick={handleReturnToDashboard}
              className="w-full bg-primary-600 text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Return to Dashboard
            </button>

            {/* Auto-redirect notice */}
            <p className="text-xs text-gray-500 mt-4">
              This window will close automatically in a few seconds...
            </p>
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-center text-sm text-gray-500">
              <i className="fas fa-shield-alt mr-2"></i>
              <span>Your connection is secure and encrypted</span>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Next Steps</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p>• Configure your Google Drive storage settings</p>
            <p>• Set up automatic backup schedules</p>
            <p>• Explore data synchronization options</p>
            <p>• Review privacy and sharing settings</p>
          </div>
        </div>
      </div>
    </div>
  );
}
