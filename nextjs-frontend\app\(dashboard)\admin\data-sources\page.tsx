'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  Database,
  Server,
  Shield,
  Check,
  ArrowRight,
  ExternalLink,
  Wifi,
  WifiOff,
  Clock
} from 'lucide-react';
import Link from 'next/link';

interface DataSource {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: 'connected' | 'disconnected' | 'coming-soon';
  features: string[];
  connectUrl?: string;
  configUrl?: string;
}

export default function DataSourcesPage() {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDataSources();
  }, []);

  const fetchDataSources = async () => {
    try {
      // Mock data sources for now
      const mockSources: DataSource[] = [
        {
          id: 'telegram',
          name: 'Telegram',
          description: 'Connect to Telegram channels and download media content for processing.',
          icon: 'telegram',
          status: 'connected',
          features: [
            'Access channel messages',
            'Download media files',
            'Filter by date and type',
            'Batch processing'
          ],
          connectUrl: '/admin/data-sources/telegram/connect',
          configUrl: '/admin/data-sources/telegram/channels'
        },
        {
          id: 'google-drive',
          name: 'Google Drive',
          description: 'Import files and documents from Google Drive for analysis.',
          icon: 'google-drive',
          status: 'disconnected',
          features: [
            'Document import',
            'Media collection',
            'Folder monitoring',
            'Auto-synchronization'
          ],
          connectUrl: '/admin/data-sources/google/connect'
        },
        {
          id: 'nas',
          name: 'NAS',
          description: 'Connect to network storage devices for secure data import and processing.',
          icon: 'server',
          status: 'disconnected',
          features: [
            'Bulk file import',
            'Scheduled syncing',
            'Secure connections',
            'Automated backup'
          ]
        },
        {
          id: 'database',
          name: 'Database',
          description: 'Connect to external databases and import data for processing.',
          icon: 'database',
          status: 'coming-soon',
          features: [
            'SQL databases',
            'NoSQL databases',
            'Custom Database',
            'Scheduled imports'
          ]
        },
        {
          id: 'twitter',
          name: 'Twitter',
          description: 'Track hashtags and download media from Twitter for analysis.',
          icon: 'twitter',
          status: 'coming-soon',
          features: [
            'Track hashtags',
            'Monitor accounts',
            'Download media',
            'Sentiment analysis'
          ]
        },
        {
          id: 'youtube',
          name: 'YouTube',
          description: 'Access YouTube videos and channels for content analysis.',
          icon: 'youtube',
          status: 'coming-soon',
          features: [
            'Download videos',
            'Channel analysis',
            'Comment extraction',
            'Metadata processing'
          ]
        },
        {
          id: 'linkedin',
          name: 'LinkedIn',
          description: 'Monitor LinkedIn posts and profiles for professional insights.',
          icon: 'linkedin',
          status: 'coming-soon',
          features: [
            'Profile tracking',
            'Post analysis',
            'Media collection',
            'Engagement metrics'
          ]
        },
        {
          id: 'instagram',
          name: 'Instagram',
          description: 'Track Instagram posts and stories for visual content analysis.',
          icon: 'instagram',
          status: 'coming-soon',
          features: [
            'Post tracking',
            'Story collection',
            'Hashtag monitoring',
            'Image analysis'
          ]
        }
      ];

      setDataSources(mockSources);
    } catch (error) {
      console.error('Failed to fetch data sources:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-success-500" />;
      case 'disconnected':
        return <WifiOff className="w-4 h-4 text-gray-500" />;
      case 'coming-soon':
        return <Clock className="w-4 h-4 text-warning-500" />;
      default:
        return <WifiOff className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-success-100 text-success-700';
      case 'disconnected':
        return 'bg-gray-100 text-gray-700';
      case 'coming-soon':
        return 'bg-warning-100 text-warning-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getSourceIcon = (iconName: string) => {
    switch (iconName) {
      case 'database':
        return <Database className="w-8 h-8" />;
      case 'server':
        return <Server className="w-8 h-8" />;
      default:
        return <Database className="w-8 h-8" />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="admin" title="Data Sources">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title="Data Sources">
      <div className="container space-y-8">
        {/* Background Effects */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-purple-500/5 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-cyan-500/5 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        {/* Header */}
        <div className="text-center relative">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Data Sources</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Connect and manage various data sources for your annotation and processing workflows
          </p>
        </div>

        {/* Data Sources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {dataSources.map((source) => (
            <div
              key={source.id}
              className="bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 overflow-hidden group"
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center group-hover:bg-primary-200 transition-colors">
                    {getSourceIcon(source.icon)}
                  </div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(source.status)}`}>
                    {getStatusIcon(source.status)}
                    <span className="ml-1 capitalize">{source.status.replace('-', ' ')}</span>
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{source.name}</h3>
                <p className="text-gray-600 text-sm">{source.description}</p>
              </div>

              {/* Features */}
              <div className="p-6">
                <ul className="space-y-2">
                  {source.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm text-gray-700">
                      <Check className="w-4 h-4 text-success-500 mr-2 flex-shrink-0" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Footer */}
              <div className="p-6 pt-0">
                {source.status === 'connected' && source.configUrl ? (
                  <Link
                    href={source.configUrl}
                    className="btn btn-primary w-full group"
                  >
                    Configure
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                ) : source.status === 'disconnected' && source.connectUrl ? (
                  <Link
                    href={source.connectUrl}
                    className="btn btn-outline w-full group"
                  >
                    Connect
                    <ExternalLink className="w-4 h-4 ml-2 group-hover:scale-110 transition-transform" />
                  </Link>
                ) : (
                  <button
                    disabled
                    className="btn btn-outline w-full opacity-50 cursor-not-allowed"
                  >
                    Coming Soon
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Compliance Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
          <div className="flex items-start space-x-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Our Commitment to Ethics
              </h3>
              <p className="text-gray-700 leading-relaxed">
                ProcessVenue adheres to the highest standards of data privacy and ethical practices. 
                We're committed to compliant and transparent data handling, respecting all privacy 
                regulations, and never engaging in unauthorized data collection. Your trust is our 
                priority—we safeguard it through ethical operations and strict adherence to our{' '}
                <a href="#" className="text-blue-600 hover:text-blue-700 font-medium underline">
                  Terms & Conditions
                </a>.
              </p>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mr-4">
                <Wifi className="w-6 h-6 text-success-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {dataSources.filter(s => s.status === 'connected').length}
                </div>
                <div className="text-sm text-gray-600">Connected Sources</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                <WifiOff className="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {dataSources.filter(s => s.status === 'disconnected').length}
                </div>
                <div className="text-sm text-gray-600">Available Sources</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-warning-100 rounded-lg flex items-center justify-center mr-4">
                <Clock className="w-6 h-6 text-warning-600" />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {dataSources.filter(s => s.status === 'coming-soon').length}
                </div>
                <div className="text-sm text-gray-600">Coming Soon</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
