'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface TelegramChannel {
  id: string;
  title: string;
  username?: string;
  participant_count: number;
  type: 'channel' | 'group' | 'supergroup';
  description?: string;
  is_verified: boolean;
  is_scam: boolean;
}

export default function TelegramChannelsPage() {
  const [channels, setChannels] = useState<TelegramChannel[]>([]);
  const [loading, setLoading] = useState(true);
  const [disconnecting, setDisconnecting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChannels, setSelectedChannels] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchChannels();
  }, []);

  const fetchChannels = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockChannels: TelegramChannel[] = [
        {
          id: 'channel1',
          title: 'Tech News Daily',
          username: 'technewsdaily',
          participant_count: 15420,
          type: 'channel',
          description: 'Latest technology news and updates',
          is_verified: true,
          is_scam: false
        },
        {
          id: 'channel2',
          title: 'AI Research Group',
          username: 'airesearchgroup',
          participant_count: 8750,
          type: 'supergroup',
          description: 'Discussions about artificial intelligence research',
          is_verified: false,
          is_scam: false
        },
        {
          id: 'channel3',
          title: 'Data Science Community',
          username: 'datasciencecommunity',
          participant_count: 23100,
          type: 'channel',
          description: 'Data science tutorials, tips, and discussions',
          is_verified: true,
          is_scam: false
        },
        {
          id: 'channel4',
          title: 'Machine Learning Papers',
          username: 'mlpapers',
          participant_count: 12300,
          type: 'channel',
          description: 'Latest machine learning research papers and reviews',
          is_verified: false,
          is_scam: false
        },
        {
          id: 'channel5',
          title: 'Programming Tips',
          participant_count: 5600,
          type: 'group',
          description: 'Programming tips and tricks for developers',
          is_verified: false,
          is_scam: false
        }
      ];
      
      setChannels(mockChannels);
    } catch (error) {
      toast.error('Failed to fetch channels');
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setDisconnecting(true);
    try {
      // Mock disconnection - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success('Disconnected from Telegram');
      // Redirect to connection page
      window.location.href = '/admin/data-sources/telegram/connect';
    } catch (error) {
      toast.error('Failed to disconnect');
    } finally {
      setDisconnecting(false);
    }
  };

  const toggleChannelSelection = (channelId: string) => {
    setSelectedChannels(prev => {
      const newSet = new Set(prev);
      if (newSet.has(channelId)) {
        newSet.delete(channelId);
      } else {
        newSet.add(channelId);
      }
      return newSet;
    });
  };

  const selectAllChannels = () => {
    setSelectedChannels(new Set(filteredChannels.map(c => c.id)));
  };

  const deselectAllChannels = () => {
    setSelectedChannels(new Set());
  };

  const downloadSelectedChannels = async () => {
    if (selectedChannels.size === 0) {
      toast.error('Please select at least one channel');
      return;
    }

    try {
      // Mock download - replace with actual API call
      toast.success(`Starting download for ${selectedChannels.size} channel(s)`);
      // Redirect to images page
      window.location.href = '/admin/data-sources/telegram/images';
    } catch (error) {
      toast.error('Failed to start download');
    }
  };

  const filteredChannels = channels.filter(channel =>
    channel.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (channel.username && channel.username.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (channel.description && channel.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getChannelIcon = (type: string) => {
    switch (type) {
      case 'channel':
        return 'fas fa-bullhorn';
      case 'supergroup':
        return 'fas fa-users';
      case 'group':
        return 'fas fa-user-friends';
      default:
        return 'fas fa-comments';
    }
  };

  const formatParticipantCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Loading Telegram channels...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                <i className="fab fa-telegram mr-3 text-blue-500"></i>
                Telegram Channels
                <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {channels.length}
                </span>
              </h1>
              <p className="text-gray-600">
                Select channels to download media content for processing
              </p>
            </div>
            <button
              onClick={handleDisconnect}
              disabled={disconnecting}
              className="px-4 py-2 bg-red-600 text-white rounded-md font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {disconnecting ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Disconnecting...
                </>
              ) : (
                <>
                  <i className="fas fa-unlink mr-2"></i>
                  Disconnect
                </>
              )}
            </button>
          </div>
        </div>

        {/* Search and Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search channels..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Selection Actions */}
              <div className="flex space-x-2">
                <button
                  onClick={selectAllChannels}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-check-square mr-2"></i>
                  Select All
                </button>
                <button
                  onClick={deselectAllChannels}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-square mr-2"></i>
                  Deselect All
                </button>
                <button
                  onClick={downloadSelectedChannels}
                  disabled={selectedChannels.size === 0}
                  className="px-4 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <i className="fas fa-download mr-2"></i>
                  Download Selected ({selectedChannels.size})
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Channels Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredChannels.map((channel) => (
            <div
              key={channel.id}
              className={`bg-white rounded-lg shadow-sm border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
                selectedChannels.has(channel.id)
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => toggleChannelSelection(channel.id)}
            >
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${
                      selectedChannels.has(channel.id) ? 'bg-primary-100' : 'bg-gray-100'
                    }`}>
                      <i className={`${getChannelIcon(channel.type)} ${
                        selectedChannels.has(channel.id) ? 'text-primary-600' : 'text-gray-600'
                      }`}></i>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h3 className="text-sm font-medium text-gray-900 truncate">
                          {channel.title}
                        </h3>
                        {channel.is_verified && (
                          <i className="fas fa-check-circle text-blue-500 ml-1 text-xs"></i>
                        )}
                      </div>
                      {channel.username && (
                        <p className="text-xs text-gray-500">@{channel.username}</p>
                      )}
                    </div>
                  </div>
                  
                  {/* Selection Checkbox */}
                  <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                    selectedChannels.has(channel.id)
                      ? 'bg-primary-600 border-primary-600'
                      : 'border-gray-300'
                  }`}>
                    {selectedChannels.has(channel.id) && (
                      <i className="fas fa-check text-white text-xs"></i>
                    )}
                  </div>
                </div>

                {/* Description */}
                {channel.description && (
                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {channel.description}
                  </p>
                )}

                {/* Stats */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-500">
                    <i className="fas fa-users mr-1"></i>
                    <span>{formatParticipantCount(channel.participant_count)}</span>
                  </div>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      channel.type === 'channel' ? 'bg-blue-100 text-blue-800' :
                      channel.type === 'supergroup' ? 'bg-green-100 text-green-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {channel.type}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredChannels.length === 0 && (
          <div className="text-center py-12">
            <i className="fas fa-search text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No channels found</h3>
            <p className="text-gray-600">
              {searchTerm ? 'Try adjusting your search terms' : 'No channels available'}
            </p>
          </div>
        )}

        {/* Status Message */}
        {selectedChannels.size > 0 && (
          <div className="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <i className="fas fa-info-circle text-primary-600 mr-2"></i>
              <span className="text-sm text-gray-700">
                {selectedChannels.size} channel{selectedChannels.size !== 1 ? 's' : ''} selected
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
