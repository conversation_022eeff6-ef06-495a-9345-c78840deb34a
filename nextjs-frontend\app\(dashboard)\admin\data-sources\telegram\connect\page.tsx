'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface TelegramSession {
  status: 'disconnected' | 'connecting' | 'verification_needed' | 'password_needed' | 'connected';
  phone?: string;
}

export default function TelegramConnectPage() {
  const [session, setSession] = useState<TelegramSession>({ status: 'disconnected' });
  const [formData, setFormData] = useState({
    api_id: '',
    api_hash: '',
    phone: '',
    verification_code: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [checkingSession, setCheckingSession] = useState(true);

  useEffect(() => {
    checkExistingSession();
  }, []);

  const checkExistingSession = async () => {
    try {
      setCheckingSession(true);
      // Mock session check - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock response - replace with actual session status
      setSession({ status: 'disconnected' });
    } catch (error) {
      console.error('Failed to check session:', error);
    } finally {
      setCheckingSession(false);
    }
  };

  const handleConnect = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.api_id || !formData.api_hash || !formData.phone) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      // Mock connection - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setSession({ status: 'verification_needed', phone: formData.phone });
      toast.success('Verification code sent to your phone');
    } catch (error) {
      toast.error('Failed to connect to Telegram');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.verification_code) {
      toast.error('Please enter the verification code');
      return;
    }

    setLoading(true);
    try {
      // Mock verification - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Simulate password requirement for some accounts
      const needsPassword = Math.random() > 0.7;
      
      if (needsPassword) {
        setSession(prev => ({ ...prev, status: 'password_needed' }));
        toast.info('Two-factor authentication required');
      } else {
        setSession(prev => ({ ...prev, status: 'connected' }));
        toast.success('Successfully connected to Telegram');
      }
    } catch (error) {
      toast.error('Invalid verification code');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.password) {
      toast.error('Please enter your password');
      return;
    }

    setLoading(true);
    try {
      // Mock password verification - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSession(prev => ({ ...prev, status: 'connected' }));
      toast.success('Successfully connected to Telegram');
    } catch (error) {
      toast.error('Invalid password');
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    setLoading(true);
    try {
      // Mock disconnection - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSession({ status: 'disconnected' });
      setFormData({
        api_id: '',
        api_hash: '',
        phone: '',
        verification_code: '',
        password: ''
      });
      toast.success('Disconnected from Telegram');
    } catch (error) {
      toast.error('Failed to disconnect');
    } finally {
      setLoading(false);
    }
  };

  if (checkingSession) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Checking for existing session...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-green-100 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className="relative max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fab fa-telegram mr-3 text-blue-500"></i>
            Telegram Connection
          </h1>
          <p className="text-gray-600">
            Connect to Telegram to access channels and download media content
          </p>
        </div>

        {/* Connection Status */}
        {session.status !== 'disconnected' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  session.status === 'connected' ? 'bg-green-500' :
                  session.status === 'connecting' ? 'bg-yellow-500' :
                  'bg-blue-500'
                }`}></div>
                <span className="text-sm font-medium text-gray-900">
                  {session.status === 'connected' ? 'Connected' :
                   session.status === 'verification_needed' ? 'Verification Required' :
                   session.status === 'password_needed' ? 'Password Required' :
                   'Connecting...'}
                </span>
              </div>
              {session.phone && (
                <span className="text-sm text-gray-500">{session.phone}</span>
              )}
            </div>
          </div>
        )}

        {/* Connection Form */}
        {session.status === 'disconnected' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                <i className="fab fa-telegram mr-2"></i>
                Connect to Telegram
              </h2>
            </div>
            <div className="p-6">
              <form onSubmit={handleConnect} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API ID
                  </label>
                  <input
                    type="text"
                    value={formData.api_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, api_id: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter your Telegram API ID"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Get your API ID from <a href="https://my.telegram.org" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">my.telegram.org</a>
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Hash
                  </label>
                  <input
                    type="text"
                    value={formData.api_hash}
                    onChange={(e) => setFormData(prev => ({ ...prev, api_hash: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter your Telegram API Hash"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Get your API Hash from <a href="https://my.telegram.org" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">my.telegram.org</a>
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="+1234567890"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Enter your phone number with country code
                  </p>
                </div>

                <div className="flex justify-between">
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Connecting...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-plug mr-2"></i>
                        Connect
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Verification Code Form */}
        {session.status === 'verification_needed' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                <i className="fas fa-mobile-alt mr-2"></i>
                Enter Verification Code
              </h2>
            </div>
            <div className="p-6">
              <form onSubmit={handleVerifyCode} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Verification Code
                  </label>
                  <input
                    type="text"
                    value={formData.verification_code}
                    onChange={(e) => setFormData(prev => ({ ...prev, verification_code: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-center text-lg tracking-widest"
                    placeholder="12345"
                    maxLength={5}
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Enter the 5-digit code sent to {session.phone}
                  </p>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => setSession({ status: 'disconnected' })}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Verifying...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-check mr-2"></i>
                        Verify Code
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Password Form */}
        {session.status === 'password_needed' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                <i className="fas fa-lock mr-2"></i>
                Two-Factor Authentication
              </h2>
            </div>
            <div className="p-6">
              <form onSubmit={handleVerifyPassword} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter your Telegram password"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Enter your two-factor authentication password
                  </p>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => setSession({ status: 'verification_needed', phone: session.phone })}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? (
                      <>
                        <i className="fas fa-spinner fa-spin mr-2"></i>
                        Verifying...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-unlock-alt mr-2"></i>
                        Verify Password
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Connected State */}
        {session.status === 'connected' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                <i className="fas fa-check-circle text-green-500 mr-2"></i>
                Successfully Connected
              </h2>
            </div>
            <div className="p-6 text-center">
              <div className="mb-6">
                <i className="fab fa-telegram text-6xl text-blue-500 mb-4"></i>
                <p className="text-gray-600">
                  Your Telegram account is now connected. You can access channels and download media content.
                </p>
              </div>

              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => window.location.href = '/admin/data-sources/telegram/channels'}
                  className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 transition-colors"
                >
                  <i className="fas fa-list mr-2"></i>
                  View Channels
                </button>
                <button
                  onClick={handleDisconnect}
                  disabled={loading}
                  className="px-6 py-2 bg-red-600 text-white rounded-md font-medium hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Disconnecting...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-unlink mr-2"></i>
                      Disconnect
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <i className="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">Getting Started</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Create a Telegram application at <a href="https://my.telegram.org" target="_blank" rel="noopener noreferrer" className="underline">my.telegram.org</a></li>
                <li>• Get your API ID and API Hash from the application settings</li>
                <li>• Use the phone number associated with your Telegram account</li>
                <li>• Keep your credentials secure and do not share them</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
