'use client';

import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface TelegramImage {
  id: string;
  file_name: string;
  file_size: number;
  date: string;
  channel: string;
  thumbnail_url?: string;
  selected: boolean;
}

interface UploadResult {
  folder_name: string;
  folder_link: string;
  uploaded_files: string[];
  date_info: string;
}

export default function TelegramImagesPage() {
  const [images, setImages] = useState<TelegramImage[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [selectedCount, setSelectedCount] = useState(0);
  const [storageDestination, setStorageDestination] = useState('google-drive');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });
  const [channelFilter, setChannelFilter] = useState('');
  const cropperRef = useRef<any>(null);

  useEffect(() => {
    fetchImages();
  }, []);

  useEffect(() => {
    const count = images.filter(img => img.selected).length;
    setSelectedCount(count);
  }, [images]);

  const fetchImages = async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API call
      const mockImages: TelegramImage[] = [
        {
          id: 'img1',
          file_name: 'document_001.jpg',
          file_size: 2048576,
          date: '2024-01-15',
          channel: 'Tech News Daily',
          thumbnail_url: '/api/placeholder/150/150',
          selected: false
        },
        {
          id: 'img2',
          file_name: 'chart_analysis.png',
          file_size: 1536000,
          date: '2024-01-14',
          channel: 'Data Science Community',
          thumbnail_url: '/api/placeholder/150/150',
          selected: false
        },
        {
          id: 'img3',
          file_name: 'research_paper.pdf',
          file_size: 3072000,
          date: '2024-01-13',
          channel: 'AI Research Group',
          thumbnail_url: '/api/placeholder/150/150',
          selected: false
        },
        {
          id: 'img4',
          file_name: 'infographic.jpg',
          file_size: 1024000,
          date: '2024-01-12',
          channel: 'Machine Learning Papers',
          thumbnail_url: '/api/placeholder/150/150',
          selected: false
        },
        {
          id: 'img5',
          file_name: 'code_snippet.png',
          file_size: 512000,
          date: '2024-01-11',
          channel: 'Programming Tips',
          thumbnail_url: '/api/placeholder/150/150',
          selected: false
        }
      ];
      
      setImages(mockImages);
    } catch (error) {
      toast.error('Failed to fetch images');
    } finally {
      setLoading(false);
    }
  };

  const toggleImageSelection = (imageId: string) => {
    setImages(prev => prev.map(img => 
      img.id === imageId ? { ...img, selected: !img.selected } : img
    ));
  };

  const selectAllImages = () => {
    setImages(prev => prev.map(img => ({ ...img, selected: true })));
  };

  const deselectAllImages = () => {
    setImages(prev => prev.map(img => ({ ...img, selected: false })));
  };

  const uploadSelectedImages = async () => {
    const selectedImages = images.filter(img => img.selected);
    
    if (selectedImages.length === 0) {
      toast.error('Please select at least one image');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // Mock upload process with progress
      for (let i = 0; i <= 100; i += 10) {
        setUploadProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Mock upload result
      const result: UploadResult = {
        folder_name: `telegram_images_${new Date().toISOString().split('T')[0]}`,
        folder_link: 'https://drive.google.com/drive/folders/mock-folder-id',
        uploaded_files: selectedImages.map(img => img.file_name),
        date_info: `${selectedImages.length} files uploaded on ${new Date().toLocaleDateString()}`
      };

      setUploadResult(result);
      toast.success(`Successfully uploaded ${selectedImages.length} files`);
      
      // Reset selection
      deselectAllImages();
    } catch (error) {
      toast.error('Failed to upload images');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getUniqueChannels = () => {
    return [...new Set(images.map(img => img.channel))];
  };

  const filteredImages = images.filter(img => {
    const matchesChannel = !channelFilter || img.channel === channelFilter;
    const matchesDateRange = (!dateRange.start || img.date >= dateRange.start) &&
                            (!dateRange.end || img.date <= dateRange.end);
    return matchesChannel && matchesDateRange;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Loading Telegram images...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fas fa-images mr-3 text-primary-600"></i>
            Telegram Images
          </h1>
          <p className="text-gray-600">
            Select and upload images from Telegram channels to your storage destination
          </p>
        </div>

        {/* Filters and Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              {/* Channel Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Channel
                </label>
                <select
                  value={channelFilter}
                  onChange={(e) => setChannelFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">All Channels</option>
                  {getUniqueChannels().map(channel => (
                    <option key={channel} value={channel}>{channel}</option>
                  ))}
                </select>
              </div>

              {/* Date Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Storage Destination */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Storage
                </label>
                <select
                  value={storageDestination}
                  onChange={(e) => setStorageDestination(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="google-drive">
                    <i className="fab fa-google-drive mr-2"></i>Google Drive
                  </option>
                  <option value="dropbox" disabled>
                    <i className="fab fa-dropbox mr-2"></i>Dropbox (Coming Soon)
                  </option>
                  <option value="azure" disabled>
                    <i className="fas fa-server mr-2"></i>Azure (Coming Soon)
                  </option>
                  <option value="nas" disabled>
                    <i className="fas fa-database mr-2"></i>NAS (Coming Soon)
                  </option>
                </select>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <button
                onClick={selectAllImages}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                <i className="fas fa-check-square mr-2"></i>
                Select All
              </button>
              <button
                onClick={deselectAllImages}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                <i className="fas fa-square mr-2"></i>
                Deselect All
              </button>
              <button
                onClick={uploadSelectedImages}
                disabled={selectedCount === 0 || uploading}
                className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {uploading ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Uploading... ({uploadProgress}%)
                  </>
                ) : (
                  <>
                    <i className="fas fa-upload mr-2"></i>
                    Upload Selected ({selectedCount})
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Upload Progress */}
        {uploading && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="p-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Upload Progress</span>
                <span className="text-sm text-gray-500">{uploadProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* Upload Results */}
        {uploadResult && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Upload Results</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Folder Information */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    <i className="fas fa-folder mr-2"></i>
                    Folder Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium">Folder name:</span>
                      <span className="ml-2 text-gray-600">{uploadResult.folder_name}</span>
                    </div>
                    <div>
                      <span className="font-medium">Date info:</span>
                      <span className="ml-2 text-gray-600">{uploadResult.date_info}</span>
                    </div>
                    <div>
                      <span className="font-medium">Folder link:</span>
                      <a
                        href={uploadResult.folder_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="ml-2 text-primary-600 hover:text-primary-700 underline"
                      >
                        Open in {storageDestination === 'google-drive' ? 'Google Drive' : 'Storage'}
                      </a>
                    </div>
                  </div>
                </div>

                {/* Uploaded Files */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">
                    <i className="fas fa-file-image mr-2"></i>
                    Uploaded Files
                  </h4>
                  <div className="max-h-32 overflow-y-auto">
                    <ul className="text-sm text-gray-600 space-y-1">
                      {uploadResult.uploaded_files.map((fileName, index) => (
                        <li key={index} className="flex items-center">
                          <i className="fas fa-check text-green-500 mr-2 text-xs"></i>
                          {fileName}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Images Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
          {filteredImages.map((image) => (
            <div
              key={image.id}
              className={`relative bg-white rounded-lg shadow-sm border-2 transition-all duration-200 cursor-pointer hover:shadow-md ${
                image.selected
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => toggleImageSelection(image.id)}
            >
              {/* Selection Checkbox */}
              <div className="absolute top-2 right-2 z-10">
                <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                  image.selected
                    ? 'bg-primary-600 border-primary-600'
                    : 'bg-white border-gray-300'
                }`}>
                  {image.selected && (
                    <i className="fas fa-check text-white text-xs"></i>
                  )}
                </div>
              </div>

              {/* Image Thumbnail */}
              <div className="aspect-square bg-gray-100 rounded-t-lg overflow-hidden">
                {image.thumbnail_url ? (
                  <img
                    src={image.thumbnail_url}
                    alt={image.file_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <i className="fas fa-file text-gray-400 text-2xl"></i>
                  </div>
                )}
              </div>

              {/* Image Info */}
              <div className="p-3">
                <h3 className="text-xs font-medium text-gray-900 truncate mb-1">
                  {image.file_name}
                </h3>
                <div className="text-xs text-gray-500 space-y-1">
                  <div className="truncate">{image.channel}</div>
                  <div className="flex justify-between">
                    <span>{formatFileSize(image.file_size)}</span>
                    <span>{image.date}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredImages.length === 0 && (
          <div className="text-center py-12">
            <i className="fas fa-images text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
            <p className="text-gray-600">
              Try adjusting your filters or check if images are available from the selected channels
            </p>
          </div>
        )}

        {/* Selection Summary */}
        {selectedCount > 0 && (
          <div className="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <i className="fas fa-info-circle text-primary-600 mr-2"></i>
              <span className="text-sm text-gray-700">
                {selectedCount} image{selectedCount !== 1 ? 's' : ''} selected
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
