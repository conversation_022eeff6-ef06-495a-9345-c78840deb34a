'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  ArrowLeft, 
  RefreshCw, 
  Folder, 
  ZoomIn, 
  ChevronLeft, 
  ChevronRight,
  Info,
  X
} from 'lucide-react';
import Image from 'next/image';

interface ImageFile {
  name: string;
  path: string;
  size?: number;
  modified?: string;
}

interface BrowserData {
  images: ImageFile[];
  current_folder: string;
  parent_folder?: string;
  available_folders: string[];
  page: number;
  total_pages: number;
  total_images: number;
  per_page: number;
}

export default function OCRBrowserPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [data, setData] = useState<BrowserData>({
    images: [],
    current_folder: '',
    available_folders: [],
    page: 1,
    total_pages: 1,
    total_images: 0,
    per_page: 20,
  });
  const [loading, setLoading] = useState(true);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [showFolderDropdown, setShowFolderDropdown] = useState(false);

  const currentFolder = searchParams.get('folder') || '';
  const currentPage = parseInt(searchParams.get('page') || '1');

  useEffect(() => {
    fetchBrowserData();
  }, [currentFolder, currentPage]);

  const fetchBrowserData = async () => {
    setLoading(true);
    try {
      const response = await api.files.browse(currentFolder);
      if (response.data.success) {
        setData(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch browser data:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToFolder = (folder: string) => {
    const params = new URLSearchParams();
    if (folder) params.set('folder', folder);
    router.push(`/admin/ocr-directory/browser?${params.toString()}`);
  };

  const navigateToPage = (page: number) => {
    const params = new URLSearchParams();
    if (currentFolder) params.set('folder', currentFolder);
    params.set('page', page.toString());
    router.push(`/admin/ocr-directory/browser?${params.toString()}`);
  };

  const openImageModal = (index: number) => {
    setSelectedImageIndex(index);
    setShowImageModal(true);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev' && selectedImageIndex > 0) {
      setSelectedImageIndex(selectedImageIndex - 1);
    } else if (direction === 'next' && selectedImageIndex < data.images.length - 1) {
      setSelectedImageIndex(selectedImageIndex + 1);
    }
  };

  const getFolderName = (folder: string) => {
    if (!folder) return 'Root Directory';
    const parts = folder.split('/');
    return parts[parts.length - 1] || parts[parts.length - 2] || folder;
  };

  const renderPagination = () => {
    if (data.total_pages <= 1) return null;

    const pages = [];
    const maxVisiblePages = 7;
    
    // Always show first page
    pages.push(1);
    
    // Calculate range around current page
    const start = Math.max(2, currentPage - 2);
    const end = Math.min(data.total_pages - 1, currentPage + 2);
    
    // Add ellipsis if needed
    if (start > 2) {
      pages.push('...');
    }
    
    // Add pages around current page
    for (let i = start; i <= end; i++) {
      if (i !== 1 && i !== data.total_pages) {
        pages.push(i);
      }
    }
    
    // Add ellipsis if needed
    if (end < data.total_pages - 1) {
      pages.push('...');
    }
    
    // Always show last page if more than 1 page
    if (data.total_pages > 1) {
      pages.push(data.total_pages);
    }

    return (
      <div className="flex flex-col items-center space-y-4 mt-8">
        <nav className="flex items-center space-x-2">
          <button
            onClick={() => navigateToPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="btn btn-outline btn-sm disabled:opacity-50"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>

          {pages.map((page, index) => (
            <div key={index}>
              {page === '...' ? (
                <span className="px-3 py-2 text-gray-500">...</span>
              ) : (
                <button
                  onClick={() => navigateToPage(page as number)}
                  className={`btn btn-sm ${
                    page === currentPage ? 'btn-primary' : 'btn-outline'
                  }`}
                >
                  {page}
                </button>
              )}
            </div>
          ))}

          <button
            onClick={() => navigateToPage(currentPage + 1)}
            disabled={currentPage === data.total_pages}
            className="btn btn-outline btn-sm disabled:opacity-50"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </nav>

        <div className="text-sm text-gray-600">
          Showing page {currentPage} of {data.total_pages} ({data.total_images} total images)
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="admin" title="OCR Directory Browser">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title="OCR Directory Browser">
      <div className="container space-y-6">
        {/* Navigation Bar */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            {data.parent_folder !== undefined && (
              <button
                onClick={() => navigateToFolder(data.parent_folder || '')}
                className="btn btn-outline btn-sm"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </button>
            )}
            
            <button
              onClick={fetchBrowserData}
              className="btn btn-outline btn-sm"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>

          {data.available_folders.length > 0 && (
            <div className="relative">
              <button
                onClick={() => setShowFolderDropdown(!showFolderDropdown)}
                className="btn btn-primary btn-sm"
              >
                <Folder className="w-4 h-4 mr-2" />
                Available Folders
              </button>
              
              {showFolderDropdown && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="py-2">
                    {data.available_folders.map((folder) => (
                      <button
                        key={folder}
                        onClick={() => {
                          navigateToFolder(folder);
                          setShowFolderDropdown(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <Folder className="w-4 h-4 inline mr-2" />
                        {folder}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Current Folder Title */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center justify-center">
            <Folder className="w-6 h-6 mr-2" />
            {getFolderName(currentFolder)}
          </h2>
        </div>

        {/* Images Grid */}
        <div className="card">
          <div className="card-body">
            {data.images.length === 0 ? (
              <div className="text-center py-16">
                <Info className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No images found</h3>
                <p className="text-gray-600">This folder doesn't contain any images.</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                {data.images.map((image, index) => (
                  <div
                    key={image.path}
                    onClick={() => openImageModal(index)}
                    className="group relative bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-all duration-200"
                  >
                    <div className="aspect-square relative">
                      <Image
                        src={`/api/images/${image.path}`}
                        alt={image.name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 16vw"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <div className="bg-white rounded-full p-2">
                            <ZoomIn className="w-5 h-5 text-gray-700" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-2">
                      <p className="text-xs text-gray-700 truncate" title={image.name}>
                        {image.name}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Pagination */}
        {renderPagination()}
      </div>

      {/* Image Modal */}
      {showImageModal && data.images[selectedImageIndex] && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold truncate">
                {data.images[selectedImageIndex].name}
              </h3>
              <button
                onClick={() => setShowImageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="relative">
              {/* Navigation Buttons */}
              {selectedImageIndex > 0 && (
                <button
                  onClick={() => navigateImage('prev')}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-all"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
              )}
              
              {selectedImageIndex < data.images.length - 1 && (
                <button
                  onClick={() => navigateImage('next')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-all"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              )}

              {/* Image */}
              <div className="flex items-center justify-center p-4" style={{ maxHeight: 'calc(90vh - 120px)' }}>
                <Image
                  src={`/api/images/${data.images[selectedImageIndex].path}`}
                  alt={data.images[selectedImageIndex].name}
                  width={800}
                  height={600}
                  className="max-w-full max-h-full object-contain"
                />
              </div>
            </div>

            <div className="p-4 border-t bg-gray-50">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Image {selectedImageIndex + 1} of {data.images.length}
                </span>
                <span>
                  {data.images[selectedImageIndex].name}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Click outside to close dropdown */}
      {showFolderDropdown && (
        <div
          className="fixed inset-0 z-5"
          onClick={() => setShowFolderDropdown(false)}
        />
      )}
    </DashboardLayout>
  );
}
