'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface Dataset {
  id: string;
  name: string;
  folder_path: string;
}

export default function EditInstructionsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [mode, setMode] = useState(searchParams.get('mode') || 'dataset');
  const [selectedDataset, setSelectedDataset] = useState(searchParams.get('dataset') || '');
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [datasetInstructions, setDatasetInstructions] = useState('');
  const [supervisionInstructions, setSupervisionInstructions] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchDatasets();
  }, []);

  useEffect(() => {
    if (selectedDataset) {
      fetchInstructions(selectedDataset);
    }
  }, [selectedDataset]);

  const fetchDatasets = async () => {
    try {
      // Mock data - replace with actual API call
      const mockDatasets: Dataset[] = [
        {
          id: 'dataset1',
          name: 'Medical Documents Dataset',
          folder_path: '/data/medical_docs'
        },
        {
          id: 'dataset2',
          name: 'Legal Documents Dataset',
          folder_path: '/data/legal_docs'
        },
        {
          id: 'dataset3',
          name: 'Financial Reports Dataset',
          folder_path: '/data/financial_reports'
        }
      ];
      
      setDatasets(mockDatasets);
    } catch (error) {
      toast.error('Failed to fetch datasets');
    } finally {
      setLoading(false);
    }
  };

  const fetchInstructions = async (datasetId: string) => {
    try {
      // Mock instructions - replace with actual API call
      const mockInstructions = {
        dataset: `Instructions for ${datasets.find(d => d.id === datasetId)?.name || 'selected dataset'}:

1. Carefully review each document image
2. Identify and annotate all text regions
3. Ensure proper bounding box placement
4. Verify text transcription accuracy
5. Mark any unclear or damaged text areas
6. Follow the annotation guidelines strictly
7. Double-check your work before submission

Special considerations:
- Pay attention to handwritten text
- Note any special formatting or layouts
- Report any technical issues immediately`,
        
        supervision: `Supervision Instructions:

1. Review annotator work quality
2. Provide constructive feedback
3. Ensure consistency across batches
4. Monitor annotation guidelines compliance
5. Address quality issues promptly
6. Maintain annotation standards
7. Report systematic issues to admin

Quality metrics to monitor:
- Accuracy of text transcription
- Proper bounding box placement
- Consistency in annotation style
- Adherence to guidelines`
      };
      
      setDatasetInstructions(mockInstructions.dataset);
      setSupervisionInstructions(mockInstructions.supervision);
    } catch (error) {
      toast.error('Failed to fetch instructions');
    }
  };

  const handleModeChange = (newMode: string) => {
    setMode(newMode);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('mode', newMode);
    if (selectedDataset) {
      url.searchParams.set('dataset', selectedDataset);
    }
    window.history.pushState({}, '', url.toString());
  };

  const handleDatasetChange = (datasetId: string) => {
    setSelectedDataset(datasetId);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('mode', mode);
    url.searchParams.set('dataset', datasetId);
    window.history.pushState({}, '', url.toString());
  };

  const saveInstructions = async () => {
    if (!selectedDataset) {
      toast.error('Please select a dataset');
      return;
    }

    setSaving(true);
    try {
      // Mock save operation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Instructions saved successfully');
    } catch (error) {
      toast.error('Failed to save instructions');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Loading instructions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fas fa-edit mr-3 text-primary-600"></i>
            Edit Dataset Instructions
          </h1>
          <p className="text-gray-600">
            Configure instructions for annotators and supervisors
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Instruction Configuration</h2>
          </div>
          
          <div className="p-6 space-y-6">
            {/* Mode Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instruction Type
              </label>
              <select
                value={mode}
                onChange={(e) => handleModeChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="dataset">Dataset Instructions</option>
                <option value="supervision">Supervision Instructions</option>
              </select>
              <p className="mt-1 text-sm text-gray-500">
                {mode === 'dataset' 
                  ? 'Instructions shown to annotators when working with datasets'
                  : 'Instructions for supervisors managing annotation quality'
                }
              </p>
            </div>

            {/* Dataset Selection */}
            {mode === 'dataset' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Dataset
                </label>
                <select
                  value={selectedDataset}
                  onChange={(e) => handleDatasetChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Choose a dataset...</option>
                  {datasets.map((dataset) => (
                    <option key={dataset.id} value={dataset.id}>
                      {dataset.name}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  Select the dataset to configure instructions for
                </p>
              </div>
            )}

            {/* Dataset Instructions */}
            {mode === 'dataset' && selectedDataset && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dataset Instructions
                </label>
                <textarea
                  value={datasetInstructions}
                  onChange={(e) => setDatasetInstructions(e.target.value)}
                  rows={12}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter dataset-specific instructions..."
                />
                <p className="mt-1 text-sm text-gray-500">
                  These instructions will be shown to annotators when working with this dataset.
                </p>
              </div>
            )}

            {/* Supervision Instructions */}
            {mode === 'supervision' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Supervision Instructions
                </label>
                <textarea
                  value={supervisionInstructions}
                  onChange={(e) => setSupervisionInstructions(e.target.value)}
                  rows={12}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter supervision instructions..."
                />
                <p className="mt-1 text-sm text-gray-500">
                  These instructions will be shown to supervisors for quality control.
                </p>
              </div>
            )}

            {/* Save Button */}
            {((mode === 'dataset' && selectedDataset) || mode === 'supervision') && (
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => router.back()}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={saveInstructions}
                  disabled={saving}
                  className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {saving ? (
                    <>
                      <i className="fas fa-spinner fa-spin mr-2"></i>
                      Saving...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-save mr-2"></i>
                      Save Instructions
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Preview Section */}
        {((mode === 'dataset' && selectedDataset && datasetInstructions) || 
          (mode === 'supervision' && supervisionInstructions)) && (
          <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Preview</h3>
            </div>
            <div className="p-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  {mode === 'dataset' ? 'Annotator View' : 'Supervisor View'}
                </h4>
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                    {mode === 'dataset' ? datasetInstructions : supervisionInstructions}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex">
            <i className="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-blue-800 mb-1">Instruction Guidelines</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Be clear and specific in your instructions</li>
                <li>• Include examples where helpful</li>
                <li>• Use numbered lists for step-by-step processes</li>
                <li>• Highlight important points or warnings</li>
                <li>• Keep instructions concise but comprehensive</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
