'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface Dataset {
  id: string;
  name: string;
  folder_path: string;
  total_batches: number;
  completed_batches: number;
  status: 'active' | 'completed' | 'pending';
  created_date: string;
}

interface FolderPath {
  manual_folder: string;
  verification_image_folder: string;
  verification_label_folder: string;
}

export default function AdminOCRDirectoryPage() {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<string>('');
  const [folderPaths, setFolderPaths] = useState<FolderPath>({
    manual_folder: '',
    verification_image_folder: '',
    verification_label_folder: ''
  });
  const [loading, setLoading] = useState(true);
  const [showNasBrowser, setShowNasBrowser] = useState(false);
  const [currentPathType, setCurrentPathType] = useState<keyof FolderPath>('manual_folder');

  useEffect(() => {
    fetchDatasets();
    fetchFolderPaths();
  }, []);

  const fetchDatasets = async () => {
    try {
      // Mock data - replace with actual API call
      const mockDatasets: Dataset[] = [
        {
          id: 'dataset1',
          name: 'Medical Documents OCR',
          folder_path: '/data/medical_ocr',
          total_batches: 25,
          completed_batches: 20,
          status: 'active',
          created_date: '2024-01-15'
        },
        {
          id: 'dataset2',
          name: 'Legal Documents OCR',
          folder_path: '/data/legal_ocr',
          total_batches: 30,
          completed_batches: 30,
          status: 'completed',
          created_date: '2024-01-10'
        },
        {
          id: 'dataset3',
          name: 'Financial Reports OCR',
          folder_path: '/data/financial_ocr',
          total_batches: 15,
          completed_batches: 0,
          status: 'pending',
          created_date: '2024-01-20'
        }
      ];
      
      setDatasets(mockDatasets);
    } catch (error) {
      toast.error('Failed to fetch datasets');
    } finally {
      setLoading(false);
    }
  };

  const fetchFolderPaths = async () => {
    try {
      // Mock folder paths - replace with actual API call
      const mockPaths: FolderPath = {
        manual_folder: '/nas/manual_annotation',
        verification_image_folder: '/nas/verification/images',
        verification_label_folder: '/nas/verification/labels'
      };
      
      setFolderPaths(mockPaths);
    } catch (error) {
      toast.error('Failed to fetch folder paths');
    }
  };

  const openNasBrowser = (pathType: keyof FolderPath) => {
    setCurrentPathType(pathType);
    setShowNasBrowser(true);
  };

  const updateFolderPath = (pathType: keyof FolderPath, newPath: string) => {
    setFolderPaths(prev => ({
      ...prev,
      [pathType]: newPath
    }));
  };

  const saveFolderPaths = async () => {
    try {
      // Mock save operation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Folder paths updated successfully');
    } catch (error) {
      toast.error('Failed to update folder paths');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressPercentage = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
          <p className="text-gray-600">Loading OCR directory data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fas fa-folder-open mr-3 text-primary-600"></i>
            OCR Directory Management
          </h1>
          <p className="text-gray-600">
            Manage OCR datasets and configure folder paths for annotation workflows
          </p>
        </div>

        {/* Folder Configuration */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Folder Configuration</h2>
          </div>
          <div className="p-6 space-y-6">
            {/* Manual Annotation Folder */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Manual Annotation Folder
              </label>
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={folderPaths.manual_folder}
                  onChange={(e) => updateFolderPath('manual_folder', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="/path/to/manual/annotation/folder"
                />
                <button
                  onClick={() => openNasBrowser('manual_folder')}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-folder mr-2"></i>
                  Browse
                </button>
              </div>
            </div>

            {/* Verification Image Folder */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Verification Image Folder
              </label>
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={folderPaths.verification_image_folder}
                  onChange={(e) => updateFolderPath('verification_image_folder', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="/path/to/verification/images"
                />
                <button
                  onClick={() => openNasBrowser('verification_image_folder')}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-folder mr-2"></i>
                  Browse
                </button>
              </div>
            </div>

            {/* Verification Label Folder */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Verification Label Folder
              </label>
              <div className="flex space-x-3">
                <input
                  type="text"
                  value={folderPaths.verification_label_folder}
                  onChange={(e) => updateFolderPath('verification_label_folder', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="/path/to/verification/labels"
                />
                <button
                  onClick={() => openNasBrowser('verification_label_folder')}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-folder mr-2"></i>
                  Browse
                </button>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                onClick={saveFolderPaths}
                className="px-6 py-2 bg-primary-600 text-white rounded-md font-medium hover:bg-primary-700 transition-colors"
              >
                <i className="fas fa-save mr-2"></i>
                Save Configuration
              </button>
            </div>
          </div>
        </div>

        {/* Dataset Overview */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">OCR Datasets</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dataset Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Batches
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {datasets.map((dataset) => {
                  const progress = getProgressPercentage(dataset.completed_batches, dataset.total_batches);
                  return (
                    <tr key={dataset.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <i className="fas fa-database text-gray-400 mr-3"></i>
                          <div>
                            <div className="text-sm font-medium text-gray-900">{dataset.name}</div>
                            <div className="text-sm text-gray-500">{dataset.folder_path}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(dataset.status)}`}>
                          {dataset.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                            <div
                              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {dataset.completed_batches}/{dataset.total_batches}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {dataset.created_date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => setSelectedDataset(dataset.id)}
                            className="text-primary-600 hover:text-primary-900 transition-colors"
                          >
                            <i className="fas fa-eye mr-1"></i>
                            View
                          </button>
                          <button className="text-gray-600 hover:text-gray-900 transition-colors">
                            <i className="fas fa-images mr-1"></i>
                            Browse
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Dataset Details */}
        {selectedDataset && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Dataset Details</h3>
              <button
                onClick={() => setSelectedDataset('')}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="p-6">
              {(() => {
                const dataset = datasets.find(d => d.id === selectedDataset);
                if (!dataset) return null;

                return (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Dataset Information</h4>
                      <div className="space-y-2 text-sm">
                        <div><span className="font-medium">Name:</span> {dataset.name}</div>
                        <div><span className="font-medium">Path:</span> {dataset.folder_path}</div>
                        <div><span className="font-medium">Status:</span> 
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(dataset.status)}`}>
                            {dataset.status}
                          </span>
                        </div>
                        <div><span className="font-medium">Created:</span> {dataset.created_date}</div>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Progress Summary</h4>
                      <div className="space-y-2 text-sm">
                        <div><span className="font-medium">Total Batches:</span> {dataset.total_batches}</div>
                        <div><span className="font-medium">Completed:</span> {dataset.completed_batches}</div>
                        <div><span className="font-medium">Remaining:</span> {dataset.total_batches - dataset.completed_batches}</div>
                        <div><span className="font-medium">Progress:</span> {getProgressPercentage(dataset.completed_batches, dataset.total_batches)}%</div>
                      </div>
                    </div>
                  </div>
                );
              })()}

              {(() => {
                const dataset = datasets.find(d => d.id === selectedDataset);
                if (!dataset || dataset.total_batches > 0) return null;

                return (
                  <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex">
                      <i className="fas fa-exclamation-circle text-yellow-400 mr-2 mt-0.5"></i>
                      <p className="text-sm text-yellow-700">
                        This dataset has no batches available.
                      </p>
                    </div>
                    <div className="mt-3 flex justify-end">
                      <button className="px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
                        <i className="fas fa-images mr-2"></i>
                        Browse Images
                      </button>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        )}

        {/* NAS Browser Modal - Placeholder */}
        {showNasBrowser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">Browse NAS Directory</h3>
                <button
                  onClick={() => setShowNasBrowser(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="p-6">
                <p className="text-gray-600 mb-4">
                  Select a folder for: <span className="font-medium">{currentPathType.replace('_', ' ')}</span>
                </p>
                <div className="bg-gray-50 rounded-lg p-4 text-center">
                  <i className="fas fa-folder text-4xl text-gray-400 mb-2"></i>
                  <p className="text-gray-600">NAS Browser functionality will be implemented here</p>
                </div>
                <div className="flex justify-end mt-6 space-x-3">
                  <button
                    onClick={() => setShowNasBrowser(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      // Mock folder selection
                      updateFolderPath(currentPathType, `/nas/selected/${currentPathType}`);
                      setShowNasBrowser(false);
                      toast.success('Folder selected successfully');
                    }}
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
                  >
                    Select Folder
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
