'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { api } from '@/lib/api-client';

interface Source {
  id: string;
  type: 'file' | 'url' | 'text';
  name: string;
  content?: string;
  url?: string;
  file?: File;
}

export default function AdminSyntheticUploadPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'file' | 'url' | 'text'>('file');
  const [sources, setSources] = useState<Source[]>([]);
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedContent, setExtractedContent] = useState<string>('');
  const [showPreview, setShowPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = useCallback((files: FileList) => {
    const newSources: Source[] = [];
    
    for (let i = 0; i < files.length && sources.length + newSources.length < 5; i++) {
      const file = files[i];
      if (file.type === 'application/pdf' || file.type === 'text/plain' || file.name.endsWith('.md')) {
        newSources.push({
          id: Date.now() + i + '',
          type: 'file',
          name: file.name,
          file
        });
      } else {
        toast.error(`Unsupported file type: ${file.name}`);
      }
    }

    if (newSources.length > 0) {
      setSources(prev => [...prev, ...newSources]);
      toast.success(`Added ${newSources.length} file(s)`);
    }
  }, [sources.length]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileUpload(e.target.files);
    }
  };

  const addUrlSource = () => {
    if (!urlInput.trim()) {
      toast.error('Please enter a valid URL');
      return;
    }

    if (sources.length >= 5) {
      toast.error('Maximum 5 sources allowed');
      return;
    }

    const newSource: Source = {
      id: Date.now() + '',
      type: 'url',
      name: urlInput,
      url: urlInput
    };

    setSources(prev => [...prev, newSource]);
    setUrlInput('');
    toast.success('URL added successfully');
  };

  const addTextSource = () => {
    if (!textInput.trim()) {
      toast.error('Please enter some text');
      return;
    }

    if (sources.length >= 5) {
      toast.error('Maximum 5 sources allowed');
      return;
    }

    const newSource: Source = {
      id: Date.now() + '',
      type: 'text',
      name: `Text Content ${sources.filter(s => s.type === 'text').length + 1}`,
      content: textInput
    };

    setSources(prev => [...prev, newSource]);
    setTextInput('');
    toast.success('Text content added successfully');
  };

  const removeSource = (id: string) => {
    setSources(prev => prev.filter(s => s.id !== id));
  };

  const processSources = async () => {
    if (sources.length === 0) {
      toast.error('Please add at least one source');
      return;
    }

    setIsProcessing(true);
    try {
      // Mock processing - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setExtractedContent(`
        # Extracted Content Preview
        
        Successfully processed ${sources.length} source(s):
        ${sources.map(s => `- ${s.name} (${s.type})`).join('\n')}
        
        This is a preview of the extracted content that will be used for synthetic dataset generation.
      `);
      
      setShowPreview(true);
      toast.success('Sources processed successfully');
    } catch (error) {
      toast.error('Failed to process sources');
    } finally {
      setIsProcessing(false);
    }
  };

  const generateDataset = async () => {
    try {
      // Mock dataset generation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Dataset generation started');
      router.push('/synthetic/dataset');
    } catch (error) {
      toast.error('Failed to generate dataset');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            <i className="fas fa-upload mr-3 text-primary-600"></i>
            Admin Synthetic Upload
          </h1>
          <p className="text-gray-600">
            Upload documents, add URLs, or paste text to create synthetic datasets
          </p>
        </div>

        {/* Upload Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex border-b border-gray-200">
            {[
              { key: 'file', icon: 'fas fa-file-upload', label: 'Upload Files' },
              { key: 'url', icon: 'fas fa-link', label: 'Add URL' },
              { key: 'text', icon: 'fas fa-keyboard', label: 'Paste Text' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.key
                    ? 'border-primary-500 text-primary-600 bg-primary-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={`${tab.icon} mr-2`}></i>
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* File Upload Panel */}
            {activeTab === 'file' && (
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors cursor-pointer"
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                onClick={() => fileInputRef.current?.click()}
              >
                <i className="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Documents</h3>
                <p className="text-gray-600 mb-2">
                  Drag and drop or <span className="text-primary-600 font-medium">browse files</span>
                </p>
                <p className="text-sm text-gray-500">Supported formats: PDF, TXT, and Markdown</p>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.txt,.md"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>
            )}

            {/* URL Panel */}
            {activeTab === 'url' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Website URL
                  </label>
                  <div className="flex space-x-3">
                    <input
                      type="url"
                      value={urlInput}
                      onChange={(e) => setUrlInput(e.target.value)}
                      placeholder="https://example.com/article"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    />
                    <button
                      onClick={addUrlSource}
                      disabled={sources.length >= 5}
                      className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <i className="fas fa-plus mr-2"></i>
                      Add URL
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Text Panel */}
            {activeTab === 'text' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Text Content
                  </label>
                  <textarea
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Paste or type your text here"
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <div className="flex justify-end mt-3">
                    <button
                      onClick={addTextSource}
                      disabled={sources.length >= 5}
                      className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <i className="fas fa-plus mr-2"></i>
                      Add as Source
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Sources List */}
        {sources.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Added Sources</h3>
              <span className="text-sm text-gray-500">
                {sources.length}/5 sources
              </span>
            </div>
            <div className="p-6 space-y-3">
              {sources.map((source) => (
                <div
                  key={source.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <i className={`fas ${
                      source.type === 'file' ? 'fa-file' :
                      source.type === 'url' ? 'fa-link' : 'fa-text-width'
                    } text-gray-400`}></i>
                    <span className="text-sm font-medium text-gray-900">{source.name}</span>
                    <span className="text-xs text-gray-500 capitalize">{source.type}</span>
                  </div>
                  <button
                    onClick={() => removeSource(source.id)}
                    className="text-red-500 hover:text-red-700 transition-colors"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Process Button */}
        <div className="flex justify-center mb-6">
          <button
            onClick={processSources}
            disabled={sources.length === 0 || isProcessing}
            className="px-8 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <i className="fas fa-spinner fa-spin mr-2"></i>
                Processing Sources...
              </>
            ) : (
              <>
                <i className="fas fa-cog mr-2"></i>
                Process Sources
              </>
            )}
          </button>
        </div>

        {/* Preview Section */}
        {showPreview && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Content Preview</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <div className="p-6">
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <pre className="whitespace-pre-wrap text-sm text-gray-700">
                  {extractedContent}
                </pre>
              </div>
              <div className="flex justify-end">
                <button
                  onClick={generateDataset}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
                >
                  <i className="fas fa-database mr-2"></i>
                  Generate Synthetic Dataset
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
