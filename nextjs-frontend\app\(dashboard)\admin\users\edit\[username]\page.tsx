'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  UserCog, 
  ArrowLeft, 
  Save, 
  Info, 
  TrendingUp,
  User,
  Mail,
  Shield,
  Tag,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';
import { formatDateTime } from '@/lib/utils';

interface User {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  role: 'admin' | 'auditor' | 'annotator';
  is_active: boolean;
  last_login?: string;
  annotation_mode?: string;
  created_at: string;
}

export default function EditUserPage() {
  const params = useParams();
  const router = useRouter();
  const username = params.username as string;

  const [user, setUser] = useState<User | null>(null);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    role: 'annotator',
    annotation_mode: 'manual',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchUser();
  }, [username]);

  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        email: user.email || '',
        role: user.role,
        annotation_mode: user.annotation_mode || 'manual',
      });
    }
  }, [user]);

  const fetchUser = async () => {
    try {
      const response = await api.users.get(username);
      if (response.data.success) {
        setUser(response.data.data);
      } else {
        toast.error('User not found');
        router.push('/admin/users');
      }
    } catch (error) {
      console.error('Failed to fetch user:', error);
      toast.error('Failed to load user');
      router.push('/admin/users');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const updateData = {
        full_name: formData.full_name || undefined,
        email: formData.email || undefined,
        role: formData.role,
        annotation_mode: formData.role === 'annotator' ? formData.annotation_mode : undefined,
      };

      const response = await api.users.update(username, updateData);

      if (response.data.success) {
        toast.success('User updated successfully!');
        router.push('/admin/users');
      } else {
        toast.error(response.data.message || 'Failed to update user');
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to update user';
      toast.error(message);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4" />;
      case 'auditor':
        return <TrendingUp className="w-4 h-4" />;
      case 'annotator':
        return <User className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-error-100 text-error-700';
      case 'auditor':
        return 'bg-info-100 text-info-700';
      case 'annotator':
        return 'bg-primary-100 text-primary-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="admin" title="Edit User">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!user) {
    return (
      <DashboardLayout requiredRole="admin" title="Edit User">
        <div className="container">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">User not found</h2>
            <Link href="/admin/users" className="btn btn-primary">
              Back to Users
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title={`Edit User: ${user.username}`}>
      <div className="container max-w-6xl">
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold flex items-center">
                <UserCog className="w-5 h-5 mr-2" />
                Edit User: {user.username}
              </h2>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                user.is_active ? 'bg-success-100 text-success-700' : 'bg-gray-100 text-gray-700'
              }`}>
                {user.is_active ? (
                  <>
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Active
                  </>
                ) : (
                  <>
                    <XCircle className="w-4 h-4 mr-1" />
                    Suspended
                  </>
                )}
              </span>
            </div>
          </div>

          <div className="card-body">
            {/* User Information Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* User Information */}
              <div className="card">
                <div className="card-header">
                  <h5 className="text-lg font-semibold flex items-center">
                    <Info className="w-5 h-5 mr-2" />
                    User Information
                  </h5>
                </div>
                <div className="card-body space-y-3">
                  <div className="flex items-center">
                    <div className="w-24 font-medium text-gray-700">Username:</div>
                    <div className="text-gray-900">{user.username}</div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-24 font-medium text-gray-700">Role:</div>
                    <div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                        {getRoleIcon(user.role)}
                        <span className="ml-1 capitalize">{user.role}</span>
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-24 font-medium text-gray-700">Last Login:</div>
                    <div className="text-gray-900">
                      {user.last_login ? formatDateTime(user.last_login) : 'Never'}
                    </div>
                  </div>
                </div>
              </div>

              {/* User Activity */}
              <div className="card">
                <div className="card-header">
                  <h5 className="text-lg font-semibold flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    User Activity
                  </h5>
                </div>
                <div className="card-body space-y-3">
                  <div className="flex items-center">
                    <div className="w-24 font-medium text-gray-700">Status:</div>
                    <div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        user.is_active ? 'bg-success-100 text-success-700' : 'bg-gray-100 text-gray-700'
                      }`}>
                        {user.is_active ? (
                          <>
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <XCircle className="w-3 h-3 mr-1" />
                            Suspended
                          </>
                        )}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-24 font-medium text-gray-700">Account:</div>
                    <div className="text-gray-900">Custom User</div>
                  </div>
                  {user.role === 'annotator' && user.annotation_mode && (
                    <div className="flex items-center">
                      <div className="w-24 font-medium text-gray-700">Mode:</div>
                      <div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-info-100 text-info-700">
                          <Tag className="w-3 h-3 mr-1" />
                          {user.annotation_mode.charAt(0).toUpperCase() + user.annotation_mode.slice(1)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Edit Form */}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="border-t pt-6">
                <h5 className="text-lg font-semibold mb-6 flex items-center">
                  <UserCog className="w-5 h-5 mr-2" />
                  Edit User Details
                </h5>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Full Name */}
                  <div className="form-group">
                    <label htmlFor="full_name" className="form-label">
                      Full Name
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="full_name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleChange}
                        className="form-input pl-10"
                        placeholder="Enter full name"
                        disabled={saving}
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div className="form-group">
                    <label htmlFor="email" className="form-label">
                      Email
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail className="h-4 w-4 text-gray-400" />
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="form-input pl-10"
                        placeholder="Enter email address"
                        disabled={saving}
                      />
                    </div>
                  </div>

                  {/* Role */}
                  <div className="form-group">
                    <label htmlFor="role" className="form-label">
                      Role
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Shield className="h-4 w-4 text-gray-400" />
                      </div>
                      <select
                        id="role"
                        name="role"
                        value={formData.role}
                        onChange={handleChange}
                        className="form-select pl-10"
                        disabled={saving}
                      >
                        <option value="annotator">Annotator</option>
                        <option value="auditor">Auditor</option>
                        <option value="admin">Administrator</option>
                      </select>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Select the user's role in the system
                    </p>
                  </div>

                  {/* Annotation Mode (only for annotators) */}
                  {formData.role === 'annotator' && (
                    <div className="form-group">
                      <label htmlFor="annotation_mode" className="form-label">
                        Annotation Mode
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Tag className="h-4 w-4 text-gray-400" />
                        </div>
                        <select
                          id="annotation_mode"
                          name="annotation_mode"
                          value={formData.annotation_mode}
                          onChange={handleChange}
                          className="form-select pl-10"
                          disabled={saving}
                        >
                          <option value="manual">Manual Labelling</option>
                          <option value="verification">Label Verification</option>
                          <option value="supervision">Supervision</option>
                        </select>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Select the annotation mode for this user
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex justify-between pt-6 border-t">
                <Link href="/admin/users" className="btn btn-outline">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Users
                </Link>
                <button
                  type="submit"
                  disabled={saving}
                  className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </div>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
