'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  AlertTriangle, 
  ArrowLeft, 
  Database, 
  Trash2,
  X,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

export default function FlushDatabasePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const requiredConfirmation = 'FLUSH DATABASE';

  const tablesToFlush = [
    'Manual Processing Records (image_processed_manual)',
    'Verification Records (image_processed_verification)',
    'Audit History (audit_history)',
    'Admin Instructions (admin_instructions)',
    'Dataset (dataset)',
    'Supervision (Supervision)',
    'SQLite Sequence (sqlite_sequence)',
  ];

  const handleFlushDatabase = async () => {
    if (confirmationText !== requiredConfirmation) {
      toast.error(`Please type "${requiredConfirmation}" to confirm`);
      return;
    }

    setIsLoading(true);

    try {
      const response = await api.admin.flushDatabase();
      
      if (response.data.success) {
        setShowSuccessModal(true);
        setConfirmationText('');
      } else {
        toast.error(response.data.message || 'Failed to flush database');
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to flush database';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    router.push('/admin/users');
  };

  return (
    <DashboardLayout requiredRole="admin" title="Flush Database">
      <div className="container max-w-4xl">
        <div className="card border-error-200">
          <div className="card-header bg-error-500 text-white">
            <h2 className="text-xl font-semibold flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              ⚠️ Flush Database and Cache
            </h2>
          </div>

          <div className="card-body">
            {/* Warning Alert */}
            <div className="alert alert-warning mb-6">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-warning-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h5 className="font-semibold text-warning-800 mb-2">
                    Warning: This action cannot be undone!
                  </h5>
                  <p className="text-warning-700 mb-3">
                    You are about to delete all records from the following tables:
                  </p>
                  <ul className="list-disc list-inside text-warning-700 space-y-1 mb-3">
                    {tablesToFlush.map((table, index) => (
                      <li key={index}>{table}</li>
                    ))}
                  </ul>
                  <p className="text-warning-700 mb-3">
                    Additionally, the Redis cache database will be completely flushed.
                  </p>
                  <hr className="border-warning-300 my-3" />
                  <p className="text-warning-800 font-medium mb-0">
                    All data in these tables and the cache will be permanently removed. This action cannot be reversed.
                  </p>
                </div>
              </div>
            </div>

            {/* Confirmation Section */}
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Database className="w-5 h-5 mr-2" />
                Confirmation Required
              </h3>
              
              <p className="text-gray-700 mb-4">
                To proceed with flushing the database and cache, please type{' '}
                <code className="bg-gray-200 px-2 py-1 rounded text-error-600 font-mono font-semibold">
                  {requiredConfirmation}
                </code>{' '}
                in the field below:
              </p>

              <div className="form-group">
                <label htmlFor="confirmation" className="form-label">
                  Confirmation Text <span className="text-error-500">*</span>
                </label>
                <input
                  type="text"
                  id="confirmation"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  className={`form-input font-mono ${
                    confirmationText && confirmationText !== requiredConfirmation 
                      ? 'border-error-500' 
                      : confirmationText === requiredConfirmation 
                        ? 'border-success-500' 
                        : ''
                  }`}
                  placeholder={`Type "${requiredConfirmation}" to confirm`}
                  disabled={isLoading}
                />
                {confirmationText && confirmationText !== requiredConfirmation && (
                  <p className="text-sm text-error-600 mt-1">
                    Text does not match. Please type exactly: {requiredConfirmation}
                  </p>
                )}
                {confirmationText === requiredConfirmation && (
                  <p className="text-sm text-success-600 mt-1 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Confirmation text matches
                  </p>
                )}
              </div>
            </div>

            {/* Impact Summary */}
            <div className="bg-error-50 border border-error-200 rounded-lg p-4 mb-6">
              <h4 className="font-semibold text-error-800 mb-2">Impact Summary:</h4>
              <ul className="text-error-700 text-sm space-y-1">
                <li>• All annotation and verification data will be lost</li>
                <li>• All audit history will be permanently deleted</li>
                <li>• All admin instructions and configurations will be reset</li>
                <li>• All dataset information will be removed</li>
                <li>• Cache will be completely cleared</li>
                <li>• Users will need to restart their annotation work</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t">
              <Link href="/admin/users" className="btn btn-outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Cancel
              </Link>
              
              <button
                onClick={handleFlushDatabase}
                disabled={isLoading || confirmationText !== requiredConfirmation}
                className="btn btn-danger disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Flushing Database...
                  </div>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4 mr-2" />
                    Confirm Database and Cache Flush
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="bg-success-500 text-white p-6 rounded-t-lg">
              <h3 className="text-lg font-bold flex items-center">
                <CheckCircle className="w-5 h-5 mr-2" />
                Database Flushed Successfully
              </h3>
            </div>
            
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-success-100 rounded-full flex items-center justify-center mr-4">
                  <Database className="w-6 h-6 text-success-500" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900">Operation Completed</h4>
                  <p className="text-gray-600 text-sm">
                    All database tables and cache have been successfully flushed.
                  </p>
                </div>
              </div>
              
              <div className="alert alert-info">
                <AlertTriangle className="w-5 h-5 mr-2" />
                The system has been reset to a clean state. All previous data has been permanently removed.
              </div>
            </div>
            
            <div className="flex justify-end p-6 border-t">
              <button
                onClick={handleSuccessModalClose}
                className="btn btn-primary"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Continue to User Management
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
