'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { useAuth } from '@/lib/auth-context';
import { 
  Shield, 
  UserCheck, 
  Eye, 
  Search, 
  Filter, 
  UserPlus,
  Database,
  Edit,
  Pause,
  Play,
  X,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';
import { formatDateTime } from '@/lib/utils';
import toast from 'react-hot-toast';

interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  role: 'admin' | 'auditor' | 'annotator';
  is_active: boolean;
  last_login?: string;
  annotation_mode?: string;
  verification_mode?: boolean;
  created_at: string;
}

export default function ManageUsersPage() {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'suspended'>('all');
  const [showSuspendModal, setShowSuspendModal] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, statusFilter]);

  const fetchUsers = async () => {
    try {
      const response = await api.users.list();
      if (response.data.success) {
        setUsers(response.data.data.items || []);
      } else {
        toast.error('Failed to fetch users');
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.role.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user =>
        statusFilter === 'active' ? user.is_active : !user.is_active
      );
    }

    setFilteredUsers(filtered);
  };

  const handleSuspendUser = async (username: string) => {
    try {
      const response = await api.users.update(username, { is_active: false });
      if (response.data.success) {
        toast.success('User suspended successfully');
        fetchUsers();
        setShowSuspendModal(null);
      } else {
        toast.error('Failed to suspend user');
      }
    } catch (error) {
      console.error('Failed to suspend user:', error);
      toast.error('Failed to suspend user');
    }
  };

  const handleReactivateUser = async (username: string) => {
    try {
      const response = await api.users.update(username, { is_active: true });
      if (response.data.success) {
        toast.success('User reactivated successfully');
        fetchUsers();
      } else {
        toast.error('Failed to reactivate user');
      }
    } catch (error) {
      console.error('Failed to reactivate user:', error);
      toast.error('Failed to reactivate user');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4" />;
      case 'auditor':
        return <Eye className="w-4 h-4" />;
      case 'annotator':
        return <UserCheck className="w-4 h-4" />;
      default:
        return <UserCheck className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-error-100 text-error-700';
      case 'auditor':
        return 'bg-warning-100 text-warning-700';
      case 'annotator':
        return 'bg-primary-100 text-primary-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getModeDisplay = (user: User) => {
    if (user.role !== 'annotator') return '-';
    
    if (user.annotation_mode === 'supervision') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning-100 text-warning-700">
          <Eye className="w-3 h-3 mr-1" />
          Supervision
        </span>
      );
    } else if (user.verification_mode || user.annotation_mode === 'verification') {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-info-100 text-info-700">
          <UserCheck className="w-3 h-3 mr-1" />
          Verification
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
          <Edit className="w-3 h-3 mr-1" />
          Manual
        </span>
      );
    }
  };

  const getUserCounts = () => {
    return {
      admin: users.filter(u => u.role === 'admin').length,
      annotator: users.filter(u => u.role === 'annotator').length,
      auditor: users.filter(u => u.role === 'auditor').length,
    };
  };

  const counts = getUserCounts();

  if (loading) {
    return (
      <DashboardLayout requiredRole="admin" title="User Management">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title="User Management">
      <div className="container space-y-6">
        {/* Role Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card cursor-pointer hover:shadow-medium transition-shadow" onClick={() => setStatusFilter('all')}>
            <div className="card-body text-center">
              <div className="w-16 h-16 bg-error-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-error-500" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Administrators</h3>
              <div className="text-3xl font-bold text-error-600 mb-2">{counts.admin}</div>
              <p className="text-gray-600 text-sm">System administrators</p>
            </div>
          </div>

          <div className="card cursor-pointer hover:shadow-medium transition-shadow" onClick={() => setStatusFilter('all')}>
            <div className="card-body text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserCheck className="w-8 h-8 text-primary-500" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Annotators</h3>
              <div className="text-3xl font-bold text-primary-600 mb-2">{counts.annotator}</div>
              <p className="text-gray-600 text-sm">Data annotation/verification</p>
            </div>
          </div>

          <div className="card cursor-pointer hover:shadow-medium transition-shadow" onClick={() => setStatusFilter('all')}>
            <div className="card-body text-center">
              <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Eye className="w-8 h-8 text-warning-500" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Auditors</h3>
              <div className="text-3xl font-bold text-warning-600 mb-2">{counts.auditor}</div>
              <p className="text-gray-600 text-sm">Quality control users</p>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="card">
          <div className="card-header">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <h2 className="text-xl font-semibold flex items-center">
                  <Database className="w-5 h-5 mr-2" />
                  Registered Users
                </h2>
                <span className="ml-3 px-3 py-1 bg-primary-100 text-primary-700 rounded-full text-sm font-medium">
                  {users.length} Total
                </span>
              </div>
              <div className="flex space-x-2">
                <Link href="/admin/users/flush-db" className="btn btn-outline btn-sm">
                  <Database className="w-4 h-4 mr-1" />
                  Flush DB
                </Link>
                <Link href="/admin/users/register" className="btn btn-primary btn-sm">
                  <UserPlus className="w-4 h-4 mr-1" />
                  Add New User
                </Link>
              </div>
            </div>
          </div>

          <div className="card-body">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="form-input pl-10"
                    placeholder="Search by username, name, email, role..."
                  />
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm('')}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setStatusFilter('all')}
                  className={`btn btn-sm ${statusFilter === 'all' ? 'btn-primary' : 'btn-outline'}`}
                >
                  <Filter className="w-4 h-4 mr-1" />
                  All Status
                </button>
                <button
                  onClick={() => setStatusFilter('active')}
                  className={`btn btn-sm ${statusFilter === 'active' ? 'btn-success' : 'btn-outline'}`}
                >
                  <Play className="w-4 h-4 mr-1" />
                  Active
                </button>
                <button
                  onClick={() => setStatusFilter('suspended')}
                  className={`btn btn-sm ${statusFilter === 'suspended' ? 'btn-danger' : 'btn-outline'}`}
                >
                  <Pause className="w-4 h-4 mr-1" />
                  Suspended
                </button>
              </div>
            </div>

            {/* Users Table */}
            {filteredUsers.length === 0 ? (
              <div className="text-center py-12">
                {users.length === 0 ? (
                  <>
                    <UserPlus className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No registered users found</h3>
                    <Link href="/admin/users/register" className="btn btn-primary">
                      <UserPlus className="w-4 h-4 mr-2" />
                      Add First User
                    </Link>
                  </>
                ) : (
                  <>
                    <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">No users match your search criteria.</p>
                  </>
                )}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Username
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Full Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mode
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Login
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{user.username}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900">{user.full_name || '-'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                            {getRoleIcon(user.role)}
                            <span className="ml-1 capitalize">{user.role}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-gray-900">{user.email || '-'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getModeDisplay(user)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {user.last_login ? (
                            <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                              {formatDateTime(user.last_login)}
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-gray-200 text-gray-600 rounded text-xs">
                              Never
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            user.is_active ? 'bg-success-100 text-success-700' : 'bg-error-100 text-error-700'
                          }`}>
                            {user.is_active ? 'Active' : 'Suspended'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <Link
                              href={`/admin/users/edit/${user.username}`}
                              className="text-primary-600 hover:text-primary-900"
                              title="Edit User"
                            >
                              <Edit className="w-4 h-4" />
                            </Link>
                            
                            {user.username !== currentUser?.username && (
                              user.is_active ? (
                                <button
                                  onClick={() => setShowSuspendModal(user.username)}
                                  className="text-error-600 hover:text-error-900"
                                  title="Suspend User"
                                >
                                  <Pause className="w-4 h-4" />
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleReactivateUser(user.username)}
                                  className="text-success-600 hover:text-success-900"
                                  title="Reactivate User"
                                >
                                  <Play className="w-4 h-4" />
                                </button>
                              )
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Suspend Confirmation Modal */}
      {showSuspendModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="bg-gradient-to-r from-error-500 to-error-600 text-white p-6 rounded-t-lg">
              <h3 className="text-lg font-bold flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Confirm Suspension
              </h3>
            </div>
            
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-error-100 rounded-full flex items-center justify-center mr-4">
                  <UserPlus className="w-6 h-6 text-error-500" />
                </div>
                <div>
                  <h4 className="font-semibold">
                    Suspend User: <span className="text-error-600">{showSuspendModal}</span>
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {users.find(u => u.username === showSuspendModal)?.full_name || 'No name provided'}
                  </p>
                </div>
              </div>
              
              <div className="alert alert-warning">
                <AlertTriangle className="w-5 h-5 mr-2" />
                The user will no longer be able to log in, but their account information will be preserved.
              </div>
            </div>
            
            <div className="flex justify-between p-6 border-t">
              <button
                onClick={() => setShowSuspendModal(null)}
                className="btn btn-outline"
              >
                <X className="w-4 h-4 mr-2" />
                Cancel
              </button>
              <button
                onClick={() => handleSuspendUser(showSuspendModal)}
                className="btn btn-danger"
              >
                <Pause className="w-4 h-4 mr-2" />
                Suspend User
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
