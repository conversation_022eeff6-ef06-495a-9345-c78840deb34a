'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { UserPlus, Eye, EyeOff, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

export default function RegisterUserPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    username: '',
    full_name: '',
    password: '',
    confirm_password: '',
    email: '',
    role: 'annotator',
    annotation_mode: 'manual',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Username validation
    if (!formData.username) {
      newErrors.username = 'Username is required';
    } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
      newErrors.username = 'Username can only contain letters, numbers, and underscores';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Confirm password validation
    if (!formData.confirm_password) {
      newErrors.confirm_password = 'Please confirm your password';
    } else if (formData.password !== formData.confirm_password) {
      newErrors.confirm_password = 'Passwords do not match';
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const userData = {
        username: formData.username,
        full_name: formData.full_name || undefined,
        password: formData.password,
        email: formData.email || undefined,
        role: formData.role,
        annotation_mode: formData.role === 'annotator' ? formData.annotation_mode : undefined,
      };

      const response = await api.users.create(userData);

      if (response.data.success) {
        toast.success('User registered successfully!');
        router.push('/admin/users');
      } else {
        toast.error(response.data.message || 'Failed to register user');
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to register user';
      toast.error(message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  return (
    <DashboardLayout requiredRole="admin" title="Register New User">
      <div className="container max-w-4xl">
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold flex items-center">
                <UserPlus className="w-5 h-5 mr-2" />
                Register New User
              </h2>
              <Link href="/admin/users" className="btn btn-outline">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Users
              </Link>
            </div>
          </div>

          <div className="card-body">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Username */}
                <div className="form-group">
                  <label htmlFor="username" className="form-label">
                    Username <span className="text-error-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    className={`form-input ${errors.username ? 'border-error-500' : ''}`}
                    placeholder="Enter username"
                    required
                    disabled={isLoading}
                  />
                  <p className="text-sm text-gray-600 mt-1">
                    Letters, numbers, & underscores only.
                  </p>
                  {errors.username && (
                    <p className="text-sm text-error-600 mt-1">{errors.username}</p>
                  )}
                </div>

                {/* Full Name */}
                <div className="form-group">
                  <label htmlFor="full_name" className="form-label">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="full_name"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter full name"
                    disabled={isLoading}
                  />
                </div>

                {/* Password */}
                <div className="form-group">
                  <label htmlFor="password" className="form-label">
                    Password <span className="text-error-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      className={`form-input pr-10 ${errors.password ? 'border-error-500' : ''}`}
                      placeholder="Enter password"
                      required
                      minLength={6}
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Minimum 6 characters.
                  </p>
                  {errors.password && (
                    <p className="text-sm text-error-600 mt-1">{errors.password}</p>
                  )}
                </div>

                {/* Confirm Password */}
                <div className="form-group">
                  <label htmlFor="confirm_password" className="form-label">
                    Confirm Password <span className="text-error-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      id="confirm_password"
                      name="confirm_password"
                      value={formData.confirm_password}
                      onChange={handleChange}
                      className={`form-input pr-10 ${errors.confirm_password ? 'border-error-500' : ''}`}
                      placeholder="Confirm password"
                      required
                      disabled={isLoading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      disabled={isLoading}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                  {errors.confirm_password && (
                    <p className="text-sm text-error-600 mt-1">{errors.confirm_password}</p>
                  )}
                </div>

                {/* Email */}
                <div className="form-group">
                  <label htmlFor="email" className="form-label">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`form-input ${errors.email ? 'border-error-500' : ''}`}
                    placeholder="Enter email address"
                    disabled={isLoading}
                  />
                  {errors.email && (
                    <p className="text-sm text-error-600 mt-1">{errors.email}</p>
                  )}
                </div>

                {/* Role */}
                <div className="form-group">
                  <label htmlFor="role" className="form-label">
                    Assign Role <span className="text-error-500">*</span>
                  </label>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    className="form-select"
                    required
                    disabled={isLoading}
                  >
                    <option value="annotator">Annotator</option>
                    <option value="auditor">Auditor</option>
                    <option value="admin">Administrator</option>
                  </select>
                </div>
              </div>

              {/* Annotator Settings */}
              {formData.role === 'annotator' && (
                <div className="border-l-4 border-primary-500 pl-4 bg-primary-50 p-4 rounded-r-lg">
                  <h3 className="text-lg font-semibold text-primary-700 mb-4 flex items-center">
                    <UserPlus className="w-5 h-5 mr-2" />
                    Annotator Settings
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="form-group">
                      <label htmlFor="annotation_mode" className="form-label">
                        Default Annotation Mode
                      </label>
                      <select
                        id="annotation_mode"
                        name="annotation_mode"
                        value={formData.annotation_mode}
                        onChange={handleChange}
                        className="form-select"
                        disabled={isLoading}
                      >
                        <option value="manual">Manual Labelling</option>
                        <option value="verification">Label Verification</option>
                        <option value="supervision">Supervision</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Link href="/admin/users" className="btn btn-outline">
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Registering...
                    </div>
                  ) : (
                    <>
                      <UserPlus className="w-4 h-4 mr-2" />
                      Register User
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
