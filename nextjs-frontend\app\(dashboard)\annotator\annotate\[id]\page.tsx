'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { useAuth } from '@/lib/auth-context';
import { 
  ArrowLeft, 
  ArrowRight, 
  Save, 
  Keyboard, 
  ListTodo,
  CheckCircle,
  Edit,
  X,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RefreshCw,
  Crop,
  Move
} from 'lucide-react';
import toast from 'react-hot-toast';
import Image from 'next/image';

interface AnnotationImage {
  id: string;
  path: string;
  filename: string;
  label?: string;
  completed: boolean;
}

interface AnnotationTask {
  id: string;
  images: AnnotationImage[];
  instructions?: string;
  verification_mode: boolean;
  session_id: string;
  batch_name: string;
}

export default function AnnotatePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const taskId = params.id as string;

  const [task, setTask] = useState<AnnotationTask | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentLabel, setCurrentLabel] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [cropMode, setCropMode] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchTask();
  }, [taskId]);

  useEffect(() => {
    // Keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return;

      switch (e.key) {
        case 'Enter':
          if (!isEditMode) {
            handleSaveLabel();
          }
          break;
        case 'ArrowLeft':
          e.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleNext();
          break;
        case 's':
          if (e.ctrlKey) {
            e.preventDefault();
            handleSaveAll();
          }
          break;
        case 'c':
          if (e.ctrlKey) {
            e.preventDefault();
            toggleCropMode();
          }
          break;
        case 'r':
        case 'F5':
          e.preventDefault();
          refreshImage();
          break;
        case '+':
        case '=':
          e.preventDefault();
          zoomIn();
          break;
        case '-':
          e.preventDefault();
          zoomOut();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentImageIndex, currentLabel, isEditMode]);

  const fetchTask = async () => {
    try {
      const response = await api.annotations.get(taskId);
      if (response.data.success) {
        setTask(response.data.data);
        if (response.data.data.images.length > 0) {
          setCurrentLabel(response.data.data.images[0].label || '');
        }
      } else {
        toast.error('Failed to load annotation task');
        router.push('/annotator/dashboard');
      }
    } catch (error) {
      console.error('Failed to fetch task:', error);
      toast.error('Failed to load annotation task');
      router.push('/annotator/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveLabel = async () => {
    if (!task || !currentLabel.trim()) {
      toast.error('Please enter a label');
      return;
    }

    setSaving(true);
    try {
      const updatedImages = [...task.images];
      updatedImages[currentImageIndex] = {
        ...updatedImages[currentImageIndex],
        label: currentLabel.trim(),
        completed: true,
      };

      const response = await api.annotations.save(taskId, {
        image_index: currentImageIndex,
        label: currentLabel.trim(),
      });

      if (response.data.success) {
        setTask(prev => prev ? { ...prev, images: updatedImages } : null);
        toast.success('Label saved successfully');
        
        // Auto-advance to next image if not the last one
        if (currentImageIndex < task.images.length - 1) {
          handleNext();
        }
      } else {
        toast.error('Failed to save label');
      }
    } catch (error) {
      console.error('Failed to save label:', error);
      toast.error('Failed to save label');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveAll = async () => {
    if (!task) return;

    const uncompletedImages = task.images.filter(img => !img.completed);
    if (uncompletedImages.length > 0) {
      toast.error(`Please complete labeling all images. ${uncompletedImages.length} images remaining.`);
      return;
    }

    setSaving(true);
    try {
      const response = await api.annotations.submit(taskId);
      if (response.data.success) {
        toast.success('All labels saved successfully!');
        router.push('/annotator/dashboard');
      } else {
        toast.error('Failed to save all labels');
      }
    } catch (error) {
      console.error('Failed to save all labels:', error);
      toast.error('Failed to save all labels');
    } finally {
      setSaving(false);
    }
  };

  const handlePrevious = () => {
    if (currentImageIndex > 0) {
      const newIndex = currentImageIndex - 1;
      setCurrentImageIndex(newIndex);
      setCurrentLabel(task?.images[newIndex].label || '');
      setIsEditMode(false);
    }
  };

  const handleNext = () => {
    if (task && currentImageIndex < task.images.length - 1) {
      const newIndex = currentImageIndex + 1;
      setCurrentImageIndex(newIndex);
      setCurrentLabel(task.images[newIndex].label || '');
      setIsEditMode(false);
    }
  };

  const zoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 300));
  };

  const zoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 25));
  };

  const resetZoom = () => {
    setZoomLevel(100);
  };

  const toggleCropMode = () => {
    setCropMode(!cropMode);
  };

  const refreshImage = () => {
    if (imageRef.current) {
      const src = imageRef.current.src;
      imageRef.current.src = '';
      imageRef.current.src = src + '?t=' + Date.now();
    }
  };

  const getCompletedCount = () => {
    return task?.images.filter(img => img.completed).length || 0;
  };

  const getProgressPercentage = () => {
    if (!task || task.images.length === 0) return 0;
    return Math.round((getCompletedCount() / task.images.length) * 100);
  };

  const isAllCompleted = () => {
    return task ? getCompletedCount() === task.images.length : false;
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="annotator">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!task) {
    return (
      <DashboardLayout requiredRole="annotator">
        <div className="container">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Task not found</h2>
            <button
              onClick={() => router.push('/annotator/dashboard')}
              className="btn btn-primary"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const currentImage = task.images[currentImageIndex];

  return (
    <DashboardLayout requiredRole="annotator">
      <div className="container-fluid py-4">
        {/* Header */}
        <div className="flex justify-center items-center mb-4">
          <h1 className="text-2xl font-bold">Image Annotation</h1>
          {user?.role === 'admin' && (
            <button
              onClick={() => router.back()}
              className="btn btn-outline ml-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Browser
            </button>
          )}
        </div>

        <div className="grid grid-cols-12 gap-4 h-[calc(100vh-200px)]">
          {/* Left Sidebar - Guidelines */}
          <div className="col-span-12 lg:col-span-3">
            <div className="card h-full">
              <div className="card-header">
                <h5 className="flex items-center">
                  <ListTodo className="w-5 h-5 mr-2" />
                  Task Guidelines
                </h5>
              </div>
              <div className="card-body">
                {task.instructions ? (
                  <div className="whitespace-pre-wrap text-sm">
                    {task.instructions}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">
                    No specific instructions have been provided for this dataset.
                  </p>
                )}
                
                <div className="mt-6">
                  <h6 className="font-semibold text-primary-600 mb-3">Best Practices</h6>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                      Be consistent with your labeling approach
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                      Take breaks to avoid fatigue
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                      Use keyboard shortcuts for efficiency
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="w-4 h-4 text-success-500 mr-2 mt-0.5 flex-shrink-0" />
                      Refer to guidelines if unsure
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Main Annotation Area */}
          <div className="col-span-12 lg:col-span-6">
            <div className="space-y-4">
              {/* Image Display */}
              <div className="relative bg-gray-100 rounded-lg overflow-hidden" style={{ height: '60vh' }}>
                {/* Status Indicators */}
                {cropMode && (
                  <div className="absolute top-4 left-4 z-10 bg-warning-100 text-warning-700 px-3 py-1 rounded-full text-sm">
                    <Crop className="w-4 h-4 inline mr-1" />
                    Crop Mode (Press Enter to confirm, Esc to cancel)
                  </div>
                )}
                
                <div className="absolute top-4 right-4 z-10 bg-info-100 text-info-700 px-3 py-1 rounded-full text-sm">
                  <ZoomIn className="w-4 h-4 inline mr-1" />
                  {zoomLevel}%
                </div>

                {currentImage.completed && (
                  <div className="absolute bottom-4 right-4 z-10 bg-success-100 text-success-700 px-3 py-1 rounded-full text-sm">
                    <CheckCircle className="w-4 h-4 inline mr-1" />
                    Labeled
                  </div>
                )}

                {/* Image Container */}
                <div 
                  ref={containerRef}
                  className="w-full h-full flex items-center justify-center overflow-hidden cursor-move"
                >
                  <img
                    ref={imageRef}
                    src={`/api/images/${currentImage.path}`}
                    alt={currentImage.filename}
                    className="max-w-full max-h-full object-contain transition-transform duration-200"
                    style={{ transform: `scale(${zoomLevel / 100})` }}
                    draggable={false}
                  />
                </div>

                {/* Zoom Controls */}
                <div className="absolute bottom-4 left-4 z-10 flex items-center space-x-2 bg-white rounded-lg shadow-md p-2">
                  <button
                    onClick={zoomOut}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="Zoom Out (-)"
                  >
                    <ZoomOut className="w-4 h-4" />
                  </button>
                  <span className="text-sm font-medium min-w-[3rem] text-center">
                    {zoomLevel}%
                  </span>
                  <button
                    onClick={zoomIn}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="Zoom In (+)"
                  >
                    <ZoomIn className="w-4 h-4" />
                  </button>
                  <button
                    onClick={resetZoom}
                    className="p-1 hover:bg-gray-100 rounded"
                    title="Reset Zoom"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>

                {/* Refresh Button */}
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <button
                    onClick={refreshImage}
                    className="btn btn-outline btn-sm"
                    title="Refresh Image (F5)"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>

                {/* Progress Bar */}
                <div className="absolute bottom-0 left-0 right-0 h-1.5 bg-gray-200">
                  <div 
                    className="h-full bg-primary-500 transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  />
                </div>
              </div>

              {/* Progress Counter */}
              <div className="text-center text-sm text-gray-600">
                <span className="font-medium">{getCompletedCount()}</span> of{' '}
                <span className="font-medium">{task.images.length}</span> images labeled
                <span className="ml-2">({getProgressPercentage()}%)</span>
              </div>

              {/* Label Input */}
              <div className="card">
                <div className="card-body">
                  {task.verification_mode ? (
                    <div>
                      <div className="bg-info-50 border border-info-200 rounded-lg p-3 mb-4">
                        <div className="flex items-center text-info-700">
                          <CheckCircle className="w-5 h-5 mr-2" />
                          Label Verification Mode
                        </div>
                      </div>

                      {!isEditMode ? (
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="font-medium text-gray-900">
                            {currentLabel || 'No label'}
                          </div>
                          <div className="flex space-x-2">
                            <button
                              onClick={handleSaveLabel}
                              className="btn btn-success btn-sm"
                              disabled={saving}
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Verify
                            </button>
                            <button
                              onClick={() => setIsEditMode(true)}
                              className="btn btn-outline btn-sm"
                            >
                              <Edit className="w-4 h-4 mr-1" />
                              Edit
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <label className="form-label">Edit Label</label>
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              value={currentLabel}
                              onChange={(e) => setCurrentLabel(e.target.value)}
                              className="form-input flex-1"
                              placeholder="Enter corrected label"
                              autoFocus
                            />
                            <button
                              onClick={() => {
                                handleSaveLabel();
                                setIsEditMode(false);
                              }}
                              className="btn btn-primary"
                              disabled={saving}
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Save
                            </button>
                            <button
                              onClick={() => {
                                setCurrentLabel(currentImage.label || '');
                                setIsEditMode(false);
                              }}
                              className="btn btn-outline"
                            >
                              <X className="w-4 h-4 mr-1" />
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <label className="form-label">
                        Label <span className="text-error-500">*</span>
                      </label>
                      <div className="flex space-x-2">
                        <input
                          type="text"
                          value={currentLabel}
                          onChange={(e) => setCurrentLabel(e.target.value)}
                          className="form-input flex-1"
                          placeholder="Enter label for this image"
                          required
                        />
                        <button
                          onClick={handleSaveLabel}
                          className="btn btn-primary"
                          disabled={saving || !currentLabel.trim()}
                        >
                          <Save className="w-4 h-4 mr-1" />
                          Save
                        </button>
                      </div>
                      <p className="text-sm text-gray-600">
                        Label is required before proceeding to the next image
                      </p>
                    </div>
                  )}

                  {/* Navigation */}
                  <div className="flex items-center justify-between mt-6">
                    <button
                      onClick={handlePrevious}
                      disabled={currentImageIndex === 0}
                      className="btn btn-outline disabled:opacity-50"
                    >
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Previous
                    </button>

                    <span className="text-sm font-medium">
                      Image {currentImageIndex + 1} of {task.images.length}
                    </span>

                    <button
                      onClick={handleNext}
                      disabled={currentImageIndex === task.images.length - 1}
                      className="btn btn-outline disabled:opacity-50"
                    >
                      Next
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>

                  {/* Save All */}
                  <div className="mt-6 text-center">
                    {isAllCompleted() && (
                      <div className="alert alert-success mb-4">
                        <CheckCircle className="w-5 h-5 mr-2" />
                        All images labeled! Ready to save.
                      </div>
                    )}
                    
                    <div className="flex justify-center space-x-4">
                      <button
                        onClick={handleSaveAll}
                        disabled={!isAllCompleted() || saving}
                        className="btn btn-success disabled:opacity-50"
                        title={!isAllCompleted() ? 'Complete labeling all images to enable this button' : ''}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        Save All Labels
                      </button>
                      
                      <button
                        onClick={() => router.push('/annotator/dashboard')}
                        disabled={!isAllCompleted()}
                        className="btn btn-primary disabled:opacity-50"
                        title={!isAllCompleted() ? 'Save all labels first to enable this button' : ''}
                      >
                        <ArrowRight className="w-4 h-4 mr-2" />
                        Get Next Set of Images
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Keyboard Shortcuts */}
          <div className="col-span-12 lg:col-span-3">
            <div className="card h-full">
              <div className="card-header">
                <h5 className="flex items-center">
                  <Keyboard className="w-5 h-5 mr-2" />
                  Keyboard Shortcuts
                </h5>
              </div>
              <div className="card-body p-0">
                <div className="divide-y divide-gray-200">
                  {[
                    { action: 'Save label', key: '↵ Enter' },
                    { action: 'Previous image', key: '← Left' },
                    { action: 'Next image', key: '→ Right' },
                    { action: 'Save all labels', key: 'Ctrl+S' },
                    { action: 'Crop image', key: 'Ctrl+C' },
                    { action: 'Refresh image', key: 'R / F5' },
                    { action: 'Zoom in', key: '+' },
                    { action: 'Zoom out', key: '-' },
                    { action: 'Pan image', key: 'Drag' },
                  ].map((shortcut, index) => (
                    <div key={index} className="flex items-center justify-between p-3">
                      <span className="text-sm text-gray-700">{shortcut.action}</span>
                      <span className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
                        {shortcut.key}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
