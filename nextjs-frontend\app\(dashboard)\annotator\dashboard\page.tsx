'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { useAuth } from '@/lib/auth-context';
import { 
  FileImage, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Play,
  Eye,
  BarChart3
} from 'lucide-react';
import Link from 'next/link';
import { formatDateTime } from '@/lib/utils';

interface AnnotationTask {
  id: string;
  image_path: string;
  status: 'pending' | 'in_progress' | 'completed' | 'reviewed';
  created_at: string;
  updated_at: string;
}

interface DashboardStats {
  total_tasks: number;
  completed_tasks: number;
  pending_tasks: number;
  in_progress_tasks: number;
}

export default function AnnotatorDashboard() {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<AnnotationTask[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    total_tasks: 0,
    completed_tasks: 0,
    pending_tasks: 0,
    in_progress_tasks: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [tasksResponse, statsResponse] = await Promise.all([
        api.annotations.list({ assigned_to: user?.id, page: 1 }),
        api.dashboard.userStats(user?.id),
      ]);

      if (tasksResponse.data.success) {
        setTasks(tasksResponse.data.data.items || []);
      }

      if (statsResponse.data.success) {
        setStats(statsResponse.data.data || stats);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-warning-500" />;
      case 'in_progress':
        return <Play className="w-4 h-4 text-info-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-success-500" />;
      case 'reviewed':
        return <Eye className="w-4 h-4 text-primary-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-warning-100 text-warning-700';
      case 'in_progress':
        return 'bg-info-100 text-info-700';
      case 'completed':
        return 'bg-success-100 text-success-700';
      case 'reviewed':
        return 'bg-primary-100 text-primary-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="annotator" title="Annotator Dashboard">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="annotator" title="Annotator Dashboard">
      <div className="container space-y-6">
        {/* Welcome Section */}
        <div className="card">
          <div className="card-body">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back, {user?.full_name || user?.username}!
            </h2>
            <p className="text-gray-600">
              Here's an overview of your annotation tasks and progress.
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_tasks}</p>
                </div>
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-primary-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-warning-600">{stats.pending_tasks}</p>
                </div>
                <div className="w-12 h-12 bg-warning-100 rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-warning-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">In Progress</p>
                  <p className="text-2xl font-bold text-info-600">{stats.in_progress_tasks}</p>
                </div>
                <div className="w-12 h-12 bg-info-100 rounded-full flex items-center justify-center">
                  <Play className="w-6 h-6 text-info-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold text-success-600">{stats.completed_tasks}</p>
                </div>
                <div className="w-12 h-12 bg-success-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-success-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Tasks */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold">Recent Tasks</h3>
          </div>
          <div className="card-body">
            {tasks.length === 0 ? (
              <div className="text-center py-12">
                <FileImage className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks available</h3>
                <p className="text-gray-600">
                  You don't have any annotation tasks assigned yet. Check back later or contact your administrator.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Image
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Updated
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {tasks.slice(0, 10).map((task) => (
                      <tr key={task.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <FileImage className="w-5 h-5 text-gray-400 mr-3" />
                            <div className="text-sm font-medium text-gray-900">
                              {task.image_path.split('/').pop()}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                            {getStatusIcon(task.status)}
                            <span className="ml-1 capitalize">{task.status.replace('_', ' ')}</span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDateTime(task.created_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDateTime(task.updated_at)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {task.status === 'pending' || task.status === 'in_progress' ? (
                            <Link
                              href={`/annotator/annotate/${task.id}`}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              {task.status === 'pending' ? 'Start' : 'Continue'}
                            </Link>
                          ) : (
                            <Link
                              href={`/annotator/review/${task.id}`}
                              className="text-gray-600 hover:text-gray-900"
                            >
                              View
                            </Link>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold">Quick Actions</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href="/annotator/tasks"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <FileImage className="w-8 h-8 text-primary-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">View All Tasks</h4>
                  <p className="text-sm text-gray-600">Browse all your annotation tasks</p>
                </div>
              </Link>

              <Link
                href="/annotator/help"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <AlertCircle className="w-8 h-8 text-info-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Help & Guidelines</h4>
                  <p className="text-sm text-gray-600">Learn annotation best practices</p>
                </div>
              </Link>

              <Link
                href="/change-password"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Eye className="w-8 h-8 text-success-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Account Settings</h4>
                  <p className="text-sm text-gray-600">Update your profile and password</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
