'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  FileText, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Maximize,
  Save,
  ArrowRight,
  Download,
  CheckCircle,
  ChevronDown
} from 'lucide-react';
import toast from 'react-hot-toast';

interface DocumentFile {
  id: string;
  name: string;
  model_type: string;
  drive_link?: string;
  status: 'processing' | 'completed' | 'error';
}

interface ExtractedField {
  name: string;
  value: string;
  confidence?: number;
  type: 'text' | 'date' | 'number' | 'email';
}

interface DocumentData {
  id: string;
  image_path: string;
  extracted_fields: ExtractedField[];
}

export default function ReviewPage() {
  const params = useParams();
  const router = useRouter();
  const taskId = params.id as string;

  const [files, setFiles] = useState<DocumentFile[]>([]);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [documentData, setDocumentData] = useState<DocumentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [showCompletion, setShowCompletion] = useState(false);
  
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    fetchDocuments();
  }, [taskId]);

  useEffect(() => {
    if (files.length > 0 && currentFileIndex < files.length) {
      fetchDocumentData(files[currentFileIndex].id);
    }
  }, [files, currentFileIndex]);

  const fetchDocuments = async () => {
    try {
      const response = await api.annotations.get(taskId);
      if (response.data.success) {
        setFiles(response.data.data.files || []);
      } else {
        toast.error('Failed to load documents');
        router.push('/annotator/dashboard');
      }
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      toast.error('Failed to load documents');
      router.push('/annotator/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const fetchDocumentData = async (fileId: string) => {
    try {
      const response = await api.annotations.get(`${taskId}/document/${fileId}`);
      if (response.data.success) {
        setDocumentData(response.data.data);
      } else {
        toast.error('Failed to load document data');
      }
    } catch (error) {
      console.error('Failed to fetch document data:', error);
      toast.error('Failed to load document data');
    }
  };

  const handleSaveAndNext = async () => {
    if (!documentData) return;

    setSaving(true);
    try {
      const response = await api.annotations.save(taskId, {
        document_id: documentData.id,
        extracted_fields: documentData.extracted_fields,
      });

      if (response.data.success) {
        toast.success('Document saved successfully');
        
        if (currentFileIndex < files.length - 1) {
          setCurrentFileIndex(prev => prev + 1);
        } else {
          setShowCompletion(true);
        }
      } else {
        toast.error('Failed to save document');
      }
    } catch (error) {
      console.error('Failed to save document:', error);
      toast.error('Failed to save document');
    } finally {
      setSaving(false);
    }
  };

  const handleSaveAll = async () => {
    setSaving(true);
    try {
      const response = await api.annotations.submit(taskId);
      if (response.data.success) {
        toast.success('All documents saved successfully');
        setShowCompletion(true);
      } else {
        toast.error('Failed to save all documents');
      }
    } catch (error) {
      console.error('Failed to save all documents:', error);
      toast.error('Failed to save all documents');
    } finally {
      setSaving(false);
    }
  };

  const handleNext = () => {
    if (currentFileIndex < files.length - 1) {
      setCurrentFileIndex(prev => prev + 1);
    } else {
      setShowCompletion(true);
    }
  };

  const handleFieldChange = (fieldName: string, value: string) => {
    if (!documentData) return;

    setDocumentData(prev => ({
      ...prev!,
      extracted_fields: prev!.extracted_fields.map(field =>
        field.name === fieldName ? { ...field, value } : field
      ),
    }));
  };

  const zoomIn = () => setZoomLevel(prev => Math.min(prev + 25, 300));
  const zoomOut = () => setZoomLevel(prev => Math.max(prev - 25, 25));
  const resetZoom = () => setZoomLevel(100);
  const fitToScreen = () => setZoomLevel(100);

  const downloadCsv = () => {
    if (!documentData) return;
    
    const csvContent = documentData.extracted_fields
      .map(field => `${field.name},${field.value}`)
      .join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${files[currentFileIndex]?.name || 'document'}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const downloadTxt = () => {
    if (!documentData) return;
    
    const txtContent = documentData.extracted_fields
      .map(field => `${field.name}: ${field.value}`)
      .join('\n');
    
    const blob = new Blob([txtContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${files[currentFileIndex]?.name || 'document'}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="annotator">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (showCompletion) {
    return (
      <DashboardLayout requiredRole="annotator">
        <div className="container">
          <div className="text-center py-12">
            <CheckCircle className="w-20 h-20 text-success-500 mx-auto mb-6" />
            <h2 className="text-3xl font-bold text-gray-900 mb-4">All documents processed!</h2>
            <p className="text-gray-600 mb-8">You have successfully processed and reviewed all documents.</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => router.push('/annotator/supervision')}
                className="btn btn-primary btn-lg"
              >
                Process More Documents
              </button>
              <button
                onClick={() => router.push('/annotator/dashboard')}
                className="btn btn-primary btn-lg"
              >
                Annotator Dashboard
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const currentFile = files[currentFileIndex];

  return (
    <DashboardLayout requiredRole="annotator">
      <div className="container-fluid py-4">
        <div className="grid grid-cols-12 gap-4 h-[calc(100vh-200px)]">
          {/* Document Queue Sidebar */}
          <div className="col-span-12 lg:col-span-2">
            <div className="card h-full">
              <div className="card-header">
                <h5 className="text-lg font-semibold">Document Queue</h5>
              </div>
              <div className="card-body p-0 overflow-y-auto">
                <div className="divide-y divide-gray-200">
                  {files.map((file, index) => (
                    <div
                      key={file.id}
                      onClick={() => setCurrentFileIndex(index)}
                      className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
                        index === currentFileIndex ? 'bg-primary-50 border-l-4 border-primary-500' : ''
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {file.name}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {file.model_type.charAt(0).toUpperCase() + file.model_type.slice(1)}
                          </div>
                        </div>
                        <div className="flex flex-col items-end">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            file.status === 'completed' ? 'bg-success-100 text-success-700' :
                            file.status === 'processing' ? 'bg-warning-100 text-warning-700' :
                            'bg-error-100 text-error-700'
                          }`}>
                            {file.status === 'processing' ? 'Processing' : 
                             file.status === 'completed' ? 'Completed' : 'Error'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Main Review Area */}
          <div className="col-span-12 lg:col-span-10">
            <div className="card h-full">
              <div className="card-header">
                <div className="flex justify-between items-center">
                  <div>
                    <h5 className="text-lg font-semibold">Document Review</h5>
                    {currentFile && (
                      <small className="text-gray-600">
                        {currentFile.model_type.charAt(0).toUpperCase() + currentFile.model_type.slice(1)} Model
                      </small>
                    )}
                  </div>
                  <span className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                    {currentFileIndex + 1} of {files.length}
                  </span>
                </div>
              </div>

              <div className="card-body">
                {!documentData ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                    <h5 className="text-lg font-semibold mb-2">Processing documents...</h5>
                    <p className="text-gray-600">The first document will appear here when ready</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-12 gap-6 h-full">
                    {/* Document Image */}
                    <div className="col-span-12 lg:col-span-7">
                      <div className="card h-full">
                        <div className="card-header">
                          <h6 className="font-semibold">Document Image</h6>
                        </div>
                        <div className="card-body p-0 text-center" style={{ height: 'calc(100vh - 350px)' }}>
                          {/* Zoom Controls */}
                          <div className="flex justify-center items-center space-x-2 py-2 border-b">
                            <button onClick={zoomIn} className="btn btn-outline btn-sm">
                              <ZoomIn className="w-4 h-4" />
                            </button>
                            <span className="text-sm font-medium min-w-[3rem] text-center">
                              {zoomLevel}%
                            </span>
                            <button onClick={zoomOut} className="btn btn-outline btn-sm">
                              <ZoomOut className="w-4 h-4" />
                            </button>
                            <button onClick={resetZoom} className="btn btn-outline btn-sm">
                              <RotateCcw className="w-4 h-4" />
                            </button>
                            <button onClick={fitToScreen} className="btn btn-outline btn-sm">
                              <Maximize className="w-4 h-4" />
                            </button>
                          </div>

                          {/* Image Container */}
                          <div className="flex items-center justify-center h-full overflow-hidden">
                            <img
                              ref={imageRef}
                              src={`/api/images/${documentData.image_path}`}
                              alt="Document"
                              className="max-w-full max-h-full object-contain transition-transform duration-200"
                              style={{ transform: `scale(${zoomLevel / 100})` }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Extracted Data */}
                    <div className="col-span-12 lg:col-span-5">
                      <div className="card h-full">
                        <div className="card-header">
                          <div className="flex justify-between items-center">
                            <h6 className="font-semibold">Extracted Data</h6>
                            <div className="flex space-x-2">
                              <button
                                onClick={handleSaveAndNext}
                                disabled={saving}
                                className="btn btn-success btn-sm"
                              >
                                <Save className="w-4 h-4 mr-1" />
                                Save & Next
                              </button>
                              <button
                                onClick={handleSaveAll}
                                disabled={saving}
                                className="btn btn-warning btn-sm"
                              >
                                <Save className="w-4 h-4 mr-1" />
                                Save All
                              </button>
                              <button
                                onClick={handleNext}
                                className="btn btn-primary btn-sm"
                              >
                                <ArrowRight className="w-4 h-4 mr-1" />
                                Next
                              </button>
                              <div className="relative">
                                <button className="btn btn-outline btn-sm flex items-center">
                                  <Download className="w-4 h-4 mr-1" />
                                  Download
                                  <ChevronDown className="w-4 h-4 ml-1" />
                                </button>
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10 hidden group-hover:block">
                                  <button
                                    onClick={downloadCsv}
                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                  >
                                    Download CSV
                                  </button>
                                  <button
                                    onClick={downloadTxt}
                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                  >
                                    Download TXT
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="card-body overflow-y-auto" style={{ height: 'calc(100vh - 350px)' }}>
                          <form className="space-y-4">
                            <input type="hidden" value={documentData.id} />
                            {documentData.extracted_fields.map((field, index) => (
                              <div key={index} className="form-group">
                                <label className="form-label">
                                  {field.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  {field.confidence && (
                                    <span className="ml-2 text-xs text-gray-500">
                                      ({Math.round(field.confidence * 100)}% confidence)
                                    </span>
                                  )}
                                </label>
                                {field.type === 'date' ? (
                                  <input
                                    type="date"
                                    value={field.value}
                                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                    className="form-input"
                                  />
                                ) : field.type === 'email' ? (
                                  <input
                                    type="email"
                                    value={field.value}
                                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                    className="form-input"
                                  />
                                ) : field.type === 'number' ? (
                                  <input
                                    type="number"
                                    value={field.value}
                                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                    className="form-input"
                                  />
                                ) : (
                                  <textarea
                                    value={field.value}
                                    onChange={(e) => handleFieldChange(field.name, e.target.value)}
                                    className="form-textarea"
                                    rows={3}
                                  />
                                )}
                              </div>
                            ))}
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
