'use client';

import { useState, useRef } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  Upload, 
  FileText, 
  Zap, 
  CheckSquare, 
  Square, 
  RefreshCw,
  CloudDownload,
  Info,
  ChevronDown,
  ChevronUp,
  Settings,
  Folder
} from 'lucide-react';
import toast from 'react-hot-toast';

interface DriveFile {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  selected: boolean;
}

export default function SupervisionPage() {
  const [localForm, setLocalForm] = useState({
    documentType: '',
    modelType: '',
    files: [] as File[],
  });
  const [driveForm, setDriveForm] = useState({
    documentType: '',
    modelType: '',
  });
  const [driveFiles, setDriveFiles] = useState<DriveFile[]>([]);
  const [showDriveFiles, setShowDriveFiles] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);

  const documentTypes = [
    { value: 'check', label: 'Cheque' },
    { value: 'passport', label: 'Passport' },
    { value: 'invoice', label: 'Invoice' },
  ];

  const modelTypes = [
    { value: 'standard', label: 'Standard (Recommended)' },
    { value: 'enhanced', label: 'Enhanced (Complex Documents)' },
    { value: 'premium', label: 'Premium (Difficult Documents)' },
  ];

  const handleLocalFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setLocalForm(prev => ({ ...prev, files }));
  };

  const handleLocalSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!localForm.documentType || !localForm.modelType || localForm.files.length === 0) {
      toast.error('Please fill all fields and select files');
      return;
    }

    setProcessing(true);
    setProgress(0);
    setStatusMessage('Uploading files...');

    try {
      const formData = new FormData();
      formData.append('document_type', localForm.documentType);
      formData.append('model_type', localForm.modelType);
      
      localForm.files.forEach((file, index) => {
        formData.append(`files[]`, file);
      });

      const response = await api.files.upload(formData);

      if (response.data.success) {
        setProgress(100);
        setStatusMessage('Files processed successfully!');
        toast.success('Documents processed successfully!');
        
        // Reset form
        setLocalForm({ documentType: '', modelType: '', files: [] });
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        toast.error('Failed to process documents');
      }
    } catch (error) {
      console.error('Failed to process documents:', error);
      toast.error('Failed to process documents');
    } finally {
      setProcessing(false);
      setTimeout(() => {
        setProgress(0);
        setStatusMessage('');
      }, 3000);
    }
  };

  const loadDriveFiles = async () => {
    if (!driveForm.documentType || !driveForm.modelType) {
      toast.error('Please select document type and processing power first');
      return;
    }

    setLoading(true);
    try {
      const response = await api.connectors.googleDrive.status();
      if (response.data.success && response.data.data.connected) {
        // Mock drive files for now
        const mockFiles: DriveFile[] = [
          { id: '1', name: 'document1.pdf', size: 1024000, mimeType: 'application/pdf', selected: false },
          { id: '2', name: 'document2.jpg', size: 512000, mimeType: 'image/jpeg', selected: false },
          { id: '3', name: 'document3.png', size: 768000, mimeType: 'image/png', selected: false },
        ];
        setDriveFiles(mockFiles);
        setShowDriveFiles(true);
        toast.success('Drive files loaded successfully');
      } else {
        toast.error('Google Drive not connected. Please connect in admin settings.');
      }
    } catch (error) {
      console.error('Failed to load drive files:', error);
      toast.error('Failed to load drive files');
    } finally {
      setLoading(false);
    }
  };

  const toggleFileSelection = (fileId: string) => {
    setDriveFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, selected: !file.selected } : file
    ));
  };

  const selectAllFiles = () => {
    setDriveFiles(prev => prev.map(file => ({ ...file, selected: true })));
  };

  const deselectAllFiles = () => {
    setDriveFiles(prev => prev.map(file => ({ ...file, selected: false })));
  };

  const processDriveFiles = async () => {
    const selectedFiles = driveFiles.filter(file => file.selected);
    if (selectedFiles.length === 0) {
      toast.error('Please select at least one file');
      return;
    }

    setProcessing(true);
    setProgress(0);
    setStatusMessage('Processing selected files...');

    try {
      // Mock processing
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      setStatusMessage('Files processed successfully!');
      toast.success('Drive files processed successfully!');
      
      // Reset
      setDriveFiles([]);
      setShowDriveFiles(false);
      setDriveForm({ documentType: '', modelType: '' });
    } catch (error) {
      console.error('Failed to process drive files:', error);
      toast.error('Failed to process drive files');
    } finally {
      setProcessing(false);
      setTimeout(() => {
        setProgress(0);
        setStatusMessage('');
      }, 3000);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <DashboardLayout requiredRole="annotator" title="Process Documents">
      <div className="container space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Process Documents</h2>
          <p className="text-lg text-gray-600">
            Extract valuable information from your documents with our advanced AI processing
          </p>
        </div>

        {/* Source Options */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Local File Upload */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                  <Upload className="w-6 h-6 text-primary-500" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Process Local Files</h3>
                  <p className="text-gray-600">Upload documents from your computer for processing</p>
                </div>
              </div>

              <form onSubmit={handleLocalSubmit} className="space-y-4">
                <div className="form-group">
                  <label className="form-label">Document Type</label>
                  <select
                    value={localForm.documentType}
                    onChange={(e) => setLocalForm(prev => ({ ...prev, documentType: e.target.value }))}
                    className="form-select"
                    required
                  >
                    <option value="">Select document type</option>
                    {documentTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Processing Power</label>
                  <select
                    value={localForm.modelType}
                    onChange={(e) => setLocalForm(prev => ({ ...prev, modelType: e.target.value }))}
                    className="form-select"
                    required
                  >
                    <option value="">Select processing power</option>
                    {modelTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Select Files</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors">
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      onChange={handleLocalFileChange}
                      className="hidden"
                      accept=".pdf,.jpg,.jpeg,.png"
                    />
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="btn btn-outline"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Choose Files
                    </button>
                    <p className="text-sm text-gray-600 mt-2">
                      Drag and drop files or click to browse
                    </p>
                    {localForm.files.length > 0 && (
                      <div className="mt-3">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-700">
                          {localForm.files.length} Files Selected
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <button
                  type="submit"
                  disabled={processing}
                  className="btn btn-primary w-full disabled:opacity-50"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Process Local Files
                </button>
              </form>
            </div>
          </div>

          {/* Google Drive */}
          <div className="card">
            <div className="card-body">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-success-100 rounded-lg flex items-center justify-center mr-4">
                  <Folder className="w-6 h-6 text-success-500" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Process from Google Drive</h3>
                  <p className="text-gray-600">Connect to your Google Drive to select documents for processing</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="form-group">
                  <label className="form-label">Document Type</label>
                  <select
                    value={driveForm.documentType}
                    onChange={(e) => setDriveForm(prev => ({ ...prev, documentType: e.target.value }))}
                    className="form-select"
                    required
                  >
                    <option value="">Select document type...</option>
                    {documentTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <div className="form-group">
                  <label className="form-label">Processing Power</label>
                  <select
                    value={driveForm.modelType}
                    onChange={(e) => setDriveForm(prev => ({ ...prev, modelType: e.target.value }))}
                    className="form-select"
                    required
                  >
                    <option value="">Select processing power</option>
                    {modelTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                <button
                  type="button"
                  onClick={loadDriveFiles}
                  disabled={loading}
                  className="btn btn-success w-full disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Load Drive Files
                </button>

                {/* Processing Status */}
                {(processing || progress > 0) && (
                  <div className="space-y-2">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    {statusMessage && (
                      <p className="text-sm text-gray-600 text-center">{statusMessage}</p>
                    )}
                  </div>
                )}

                {/* Drive Files List */}
                {showDriveFiles && (
                  <div className="space-y-3">
                    <h6 className="font-semibold text-gray-900">Available Files:</h6>
                    <div className="max-h-48 overflow-y-auto space-y-2">
                      {driveFiles.map(file => (
                        <div
                          key={file.id}
                          onClick={() => toggleFileSelection(file.id)}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            file.selected ? 'border-primary-500 bg-primary-50' : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              {file.selected ? (
                                <CheckSquare className="w-4 h-4 text-primary-500 mr-2" />
                              ) : (
                                <Square className="w-4 h-4 text-gray-400 mr-2" />
                              )}
                              <div>
                                <div className="font-medium text-gray-900">{file.name}</div>
                                <div className="text-sm text-gray-500">{formatFileSize(file.size)}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={selectAllFiles}
                        className="btn btn-outline btn-sm flex-1"
                      >
                        <CheckSquare className="w-4 h-4 mr-1" />
                        Select All
                      </button>
                      <button
                        type="button"
                        onClick={deselectAllFiles}
                        className="btn btn-outline btn-sm flex-1"
                      >
                        <Square className="w-4 h-4 mr-1" />
                        Deselect All
                      </button>
                    </div>

                    <button
                      type="button"
                      onClick={processDriveFiles}
                      disabled={processing || driveFiles.filter(f => f.selected).length === 0}
                      className="btn btn-success w-full disabled:opacity-50"
                    >
                      <CloudDownload className="w-4 h-4 mr-2" />
                      Process Selected Files
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Processing Power Guide */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Zap className="w-5 h-5 text-primary-500 mr-2" />
                <h4 className="text-lg font-semibold">Processing Power Guide</h4>
              </div>
              <button
                onClick={() => setShowGuide(!showGuide)}
                className="btn btn-outline btn-sm"
              >
                <Info className="w-4 h-4 mr-1" />
                {showGuide ? (
                  <>
                    Hide Guide
                    <ChevronUp className="w-4 h-4 ml-1" />
                  </>
                ) : (
                  <>
                    Show Guide
                    <ChevronDown className="w-4 h-4 ml-1" />
                  </>
                )}
              </button>
            </div>
          </div>

          {showGuide && (
            <div className="card-body">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="card border-2 border-blue-200">
                  <div className="card-body text-center">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="w-6 h-6 text-blue-500" />
                    </div>
                    <h5 className="font-semibold text-gray-900 mb-2">Standard</h5>
                    <div className="flex justify-center mb-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-6 bg-blue-500 rounded"></div>
                        <div className="w-2 h-6 bg-gray-300 rounded"></div>
                        <div className="w-2 h-6 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">
                      Perfect for everyday document processing with clear text and standard layouts. Cost-effective and fast.
                    </p>
                    <p className="text-xs text-gray-500">Best for: Clean, high-quality documents</p>
                  </div>
                </div>

                <div className="card border-2 border-yellow-200">
                  <div className="card-body text-center">
                    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="w-6 h-6 text-yellow-500" />
                    </div>
                    <h5 className="font-semibold text-gray-900 mb-2">Enhanced</h5>
                    <div className="flex justify-center mb-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-6 bg-yellow-500 rounded"></div>
                        <div className="w-2 h-6 bg-yellow-500 rounded"></div>
                        <div className="w-2 h-6 bg-gray-300 rounded"></div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">
                      Improved accuracy for complex layouts, handwriting, and moderate image quality issues.
                    </p>
                    <p className="text-xs text-gray-500">Best for: Mixed quality documents, complex forms</p>
                  </div>
                </div>

                <div className="card border-2 border-red-200">
                  <div className="card-body text-center">
                    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="w-6 h-6 text-red-500" />
                    </div>
                    <h5 className="font-semibold text-gray-900 mb-2">Premium</h5>
                    <div className="flex justify-center mb-3">
                      <div className="flex space-x-1">
                        <div className="w-2 h-6 bg-red-500 rounded"></div>
                        <div className="w-2 h-6 bg-red-500 rounded"></div>
                        <div className="w-2 h-6 bg-red-500 rounded"></div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm mb-3">
                      Advanced computation for challenging documents with poor image quality, extensive handwriting or complex layouts.
                    </p>
                    <p className="text-xs text-gray-500">Best for: Difficult documents, poor scans</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
