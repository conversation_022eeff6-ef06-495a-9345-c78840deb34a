'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { useAuth } from '@/lib/auth-context';
import { 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertCircle,
  BarChart3,
  FileImage,
  User
} from 'lucide-react';
import Link from 'next/link';
import { formatDateTime } from '@/lib/utils';

interface AuditTask {
  id: string;
  annotation_task_id: string;
  image_path: string;
  annotator: string;
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  created_at: string;
  reviewed_at?: string;
}

interface AuditStats {
  total_audits: number;
  pending_audits: number;
  approved_audits: number;
  rejected_audits: number;
}

export default function AuditorDashboard() {
  const { user } = useAuth();
  const [audits, setAudits] = useState<AuditTask[]>([]);
  const [stats, setStats] = useState<AuditStats>({
    total_audits: 0,
    pending_audits: 0,
    approved_audits: 0,
    rejected_audits: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [auditsResponse, statsResponse] = await Promise.all([
        api.audits.list({ page: 1 }),
        api.dashboard.userStats(user?.id),
      ]);

      if (auditsResponse.data.success) {
        setAudits(auditsResponse.data.data.items || []);
      }

      if (statsResponse.data.success) {
        setStats(statsResponse.data.data || stats);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-warning-500" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-success-500" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-error-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-warning-100 text-warning-700';
      case 'approved':
        return 'bg-success-100 text-success-700';
      case 'rejected':
        return 'bg-error-100 text-error-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredRole="auditor" title="Auditor Dashboard">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="auditor" title="Auditor Dashboard">
      <div className="container space-y-6">
        {/* Welcome Section */}
        <div className="card">
          <div className="card-body">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome back, {user?.full_name || user?.username}!
            </h2>
            <p className="text-gray-600">
              Review and audit annotation tasks to ensure quality and accuracy.
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Audits</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total_audits}</p>
                </div>
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-primary-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Review</p>
                  <p className="text-2xl font-bold text-warning-600">{stats.pending_audits}</p>
                </div>
                <div className="w-12 h-12 bg-warning-100 rounded-full flex items-center justify-center">
                  <Clock className="w-6 h-6 text-warning-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-success-600">{stats.approved_audits}</p>
                </div>
                <div className="w-12 h-12 bg-success-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-success-500" />
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-error-600">{stats.rejected_audits}</p>
                </div>
                <div className="w-12 h-12 bg-error-100 rounded-full flex items-center justify-center">
                  <XCircle className="w-6 h-6 text-error-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Audits */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold">Pending Audits</h3>
          </div>
          <div className="card-body">
            {audits.filter(audit => audit.status === 'pending').length === 0 ? (
              <div className="text-center py-12">
                <Eye className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No pending audits</h3>
                <p className="text-gray-600">
                  All annotation tasks have been reviewed. Great job!
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Image
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Annotator
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {audits
                      .filter(audit => audit.status === 'pending')
                      .slice(0, 10)
                      .map((audit) => (
                        <tr key={audit.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <FileImage className="w-5 h-5 text-gray-400 mr-3" />
                              <div className="text-sm font-medium text-gray-900">
                                {audit.image_path.split('/').pop()}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <User className="w-4 h-4 text-gray-400 mr-2" />
                              <div className="text-sm text-gray-900">{audit.annotator}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(audit.status)}`}>
                              {getStatusIcon(audit.status)}
                              <span className="ml-1 capitalize">{audit.status}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDateTime(audit.created_at)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <Link
                              href={`/auditor/review/${audit.id}`}
                              className="text-primary-600 hover:text-primary-900"
                            >
                              Review
                            </Link>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold">Recent Activity</h3>
          </div>
          <div className="card-body">
            {audits.length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-600">No recent audit activity</p>
              </div>
            ) : (
              <div className="space-y-4">
                {audits.slice(0, 5).map((audit) => (
                  <div key={audit.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(audit.status)}
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {audit.status === 'pending' ? 'Pending review' : 
                           audit.status === 'approved' ? 'Approved' : 'Rejected'} - {audit.image_path.split('/').pop()}
                        </p>
                        <p className="text-xs text-gray-500">
                          Annotated by {audit.annotator} • {formatDateTime(audit.created_at)}
                        </p>
                      </div>
                    </div>
                    <Link
                      href={`/auditor/review/${audit.id}`}
                      className="text-primary-600 hover:text-primary-900 text-sm font-medium"
                    >
                      View
                    </Link>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-xl font-semibold">Quick Actions</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link
                href="/auditor/tasks"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Eye className="w-8 h-8 text-primary-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Available Tasks</h4>
                  <p className="text-sm text-gray-600">Review pending annotation tasks</p>
                </div>
              </Link>

              <Link
                href="/auditor/history"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Clock className="w-8 h-8 text-info-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Audit History</h4>
                  <p className="text-sm text-gray-600">View your audit history</p>
                </div>
              </Link>

              <Link
                href="/change-password"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <User className="w-8 h-8 text-success-500 mr-3" />
                <div>
                  <h4 className="font-medium text-gray-900">Account Settings</h4>
                  <p className="text-sm text-gray-600">Update your profile and password</p>
                </div>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
