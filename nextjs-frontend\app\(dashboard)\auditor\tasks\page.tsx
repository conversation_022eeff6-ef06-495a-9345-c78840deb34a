'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  CheckSquare, 
  Download, 
  Eye, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Save,
  MessageSquare,
  ChevronUp,
  ChevronDown,
  Inbox,
  Image as ImageIcon
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Dataset {
  id: string;
  name: string;
}

interface AuditTask {
  id: string;
  image_path: string;
  image_name: string;
  label: string;
  annotator: string;
  verification_mode: string;
}

export default function AvailableTasksPage() {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [tasks, setTasks] = useState<AuditTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(true);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showCommentsModal, setShowCommentsModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string>('');
  const [comments, setComments] = useState('');
  const [zoomLevel, setZoomLevel] = useState(100);
  
  const [formData, setFormData] = useState({
    verification_mode: '',
    dataset: '',
    image_folder: '',
    verifier: '',
    json_file: '',
  });

  useEffect(() => {
    fetchDatasets();
  }, []);

  const fetchDatasets = async () => {
    try {
      const response = await api.audits.list(); // This would be a datasets endpoint
      if (response.data.success) {
        // Mock datasets for now
        setDatasets([
          { id: '1', name: 'Dataset 1' },
          { id: '2', name: 'Dataset 2' },
        ]);
      }
    } catch (error) {
      console.error('Failed to fetch datasets:', error);
    }
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await api.audits.list({
        verification_mode: formData.verification_mode,
        dataset: formData.dataset,
      });

      if (response.data.success) {
        setTasks(response.data.data.items || []);
        toast.success(`Loaded ${response.data.data.items?.length || 0} tasks`);
      } else {
        toast.error('Failed to load tasks');
      }
    } catch (error) {
      console.error('Failed to load tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAll = async () => {
    try {
      const response = await api.audits.approve('batch', comments);
      if (response.data.success) {
        toast.success('All labels saved successfully');
        setTasks([]);
      } else {
        toast.error('Failed to save labels');
      }
    } catch (error) {
      console.error('Failed to save labels:', error);
      toast.error('Failed to save labels');
    }
  };

  const handleLabelChange = (taskId: string, newLabel: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, label: newLabel } : task
    ));
  };

  const openImageModal = (imagePath: string) => {
    setSelectedImage(imagePath);
    setShowImageModal(true);
    setZoomLevel(100);
  };

  const zoomIn = () => setZoomLevel(prev => Math.min(prev + 25, 300));
  const zoomOut = () => setZoomLevel(prev => Math.max(prev - 25, 25));
  const resetZoom = () => setZoomLevel(100);

  return (
    <DashboardLayout requiredRole="auditor" title="Review Available Tasks">
      <div className="container space-y-6">
        {/* Task Selection Form */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold flex items-center">
              <CheckSquare className="w-5 h-5 mr-2" />
              Select Task Criteria
            </h2>
          </div>
          
          {showForm && (
            <div className="card-body">
              <form onSubmit={handleFormSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {/* Verification Mode */}
                  <div>
                    <label className="form-label">Verification Mode</label>
                    <select
                      value={formData.verification_mode}
                      onChange={(e) => setFormData(prev => ({ ...prev, verification_mode: e.target.value }))}
                      className="form-select"
                      required
                    >
                      <option value="">Select mode...</option>
                      <option value="manual">Manual Label Verification</option>
                      <option value="agentic">Agentic Verification</option>
                    </select>
                  </div>

                  {/* Dataset */}
                  <div>
                    <label className="form-label">Dataset</label>
                    <select
                      value={formData.dataset}
                      onChange={(e) => setFormData(prev => ({ ...prev, dataset: e.target.value }))}
                      className="form-select"
                      required
                      disabled={!formData.verification_mode}
                    >
                      <option value="">Select dataset...</option>
                      {datasets.map(dataset => (
                        <option key={dataset.id} value={dataset.id}>
                          {dataset.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Image Folder */}
                  <div>
                    <label className="form-label">Image Folder</label>
                    <select
                      value={formData.image_folder}
                      onChange={(e) => setFormData(prev => ({ ...prev, image_folder: e.target.value }))}
                      className="form-select"
                      disabled={!formData.dataset}
                    >
                      <option value="">Select image folder...</option>
                      <option value="folder1">Folder 1</option>
                      <option value="folder2">Folder 2</option>
                    </select>
                  </div>

                  {/* Verifier */}
                  <div>
                    <label className="form-label">Verifier</label>
                    <select
                      value={formData.verifier}
                      onChange={(e) => setFormData(prev => ({ ...prev, verifier: e.target.value }))}
                      className="form-select"
                      disabled={!formData.image_folder}
                    >
                      <option value="">Select verifier...</option>
                      <option value="verifier1">Verifier 1</option>
                      <option value="verifier2">Verifier 2</option>
                    </select>
                  </div>

                  {/* JSON File */}
                  <div>
                    <label className="form-label">Verification File</label>
                    <select
                      value={formData.json_file}
                      onChange={(e) => setFormData(prev => ({ ...prev, json_file: e.target.value }))}
                      className="form-select"
                      disabled={!formData.verifier}
                    >
                      <option value="">Select file...</option>
                      <option value="file1.json">file1.json</option>
                      <option value="file2.json">file2.json</option>
                    </select>
                  </div>

                  {/* Load Button */}
                  <div className="flex items-end">
                    <button
                      type="submit"
                      disabled={!formData.verification_mode || !formData.dataset || loading}
                      className="btn btn-primary w-full disabled:opacity-50"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      {loading ? 'Loading...' : 'Load Tasks'}
                    </button>
                  </div>
                </div>
              </form>
            </div>
          )}
        </div>

        {/* Toggle Form Button */}
        <button
          onClick={() => setShowForm(!showForm)}
          className="btn btn-outline btn-sm"
        >
          {showForm ? (
            <>
              <ChevronUp className="w-4 h-4 mr-2" />
              Hide Selection Form
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4 mr-2" />
              Show Selection Form
            </>
          )}
        </button>

        {/* Tasks Container */}
        {(tasks.length > 0 || loading) && (
          <div className="card">
            <div className="card-header">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-semibold flex items-center">
                  <ImageIcon className="w-5 h-5 mr-2" />
                  Review Area
                </h2>
                <span className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm">
                  {tasks.length} tasks
                </span>
              </div>
            </div>

            <div className="card-body">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading tasks...</p>
                </div>
              ) : tasks.length === 0 ? (
                <div className="text-center py-12">
                  <Inbox className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">No tasks available for the selected criteria.</p>
                </div>
              ) : (
                <>
                  {/* Tasks Table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                            Image
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">
                            Labels (Editable)
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {tasks.map((task) => (
                          <tr key={task.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4">
                              <div className="flex items-center space-x-3">
                                <button
                                  onClick={() => openImageModal(task.image_path)}
                                  className="flex-shrink-0"
                                >
                                  <img
                                    src={`/api/images/${task.image_path}`}
                                    alt={task.image_name}
                                    className="w-20 h-20 object-cover rounded border hover:opacity-80 transition-opacity cursor-pointer"
                                  />
                                </button>
                                <div>
                                  <div className="font-medium text-gray-900">{task.image_name}</div>
                                  <div className="text-sm text-gray-500">
                                    Annotated by: {task.annotator}
                                  </div>
                                  <button
                                    onClick={() => openImageModal(task.image_path)}
                                    className="text-primary-600 hover:text-primary-900 text-sm flex items-center mt-1"
                                  >
                                    <Eye className="w-4 h-4 mr-1" />
                                    View Full Size
                                  </button>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4">
                              <input
                                type="text"
                                value={task.label}
                                onChange={(e) => handleLabelChange(task.id, e.target.value)}
                                className="form-input w-full"
                                placeholder="Enter or edit label"
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      onClick={() => setShowCommentsModal(true)}
                      className="btn btn-outline"
                    >
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Add Comments
                    </button>
                    <button
                      onClick={handleSaveAll}
                      className="btn btn-success"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Save All Labels
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Comments Modal */}
      {showCommentsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6 border-b">
              <h3 className="text-lg font-semibold">Add Batch Comments</h3>
            </div>
            
            <div className="p-6">
              <label className="form-label">Comments (will be stored in audit history)</label>
              <textarea
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                className="form-textarea"
                rows={4}
                placeholder="Enter any notes or comments about this batch..."
              />
            </div>
            
            <div className="flex justify-end space-x-3 p-6 border-t">
              <button
                onClick={() => setShowCommentsModal(false)}
                className="btn btn-outline"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowCommentsModal(false);
                  toast.success('Comments saved');
                }}
                className="btn btn-primary"
              >
                Save Comments
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {showImageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b">
              <h3 className="text-lg font-semibold">Image Preview</h3>
              <button
                onClick={() => setShowImageModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-6 text-center max-h-[70vh] overflow-auto">
              <img
                src={`/api/images/${selectedImage}`}
                alt="Preview"
                className="max-w-full max-h-full object-contain transition-transform duration-200"
                style={{ transform: `scale(${zoomLevel / 100})` }}
              />
            </div>
            
            <div className="flex justify-between items-center p-6 border-t">
              <div className="flex space-x-2">
                <button onClick={zoomOut} className="btn btn-outline btn-sm">
                  <ZoomOut className="w-4 h-4" />
                </button>
                <button onClick={zoomIn} className="btn btn-outline btn-sm">
                  <ZoomIn className="w-4 h-4" />
                </button>
                <button onClick={resetZoom} className="btn btn-outline btn-sm">
                  <RotateCcw className="w-4 h-4" />
                  Reset
                </button>
              </div>
              <button
                onClick={() => setShowImageModal(false)}
                className="btn btn-outline"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
}
