'use client';

import { useSearchParams } from 'next/navigation';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { useAuth } from '@/lib/auth-context';
import { 
  Inbox, 
  Home, 
  Mail, 
  Clock,
  CheckCircle,
  Eye,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';

export default function NoTasksPage() {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  
  const message = searchParams.get('message') || 'No tasks are currently available for you.';
  const mode = searchParams.get('mode') || user?.annotation_mode || 'annotation';
  const isVerificationMode = mode === 'verification';

  const getDashboardLink = () => {
    switch (user?.role) {
      case 'admin':
        return '/admin/dashboard';
      case 'auditor':
        return '/auditor/dashboard';
      case 'annotator':
        return '/annotator/dashboard';
      default:
        return '/';
    }
  };

  const getActionItems = () => {
    const baseItems = [
      'Wait for an administrator to assign new tasks',
      'Contact your administrator for assistance',
    ];

    if (user?.role === 'annotator') {
      return [
        ...baseItems,
        'Check back later for new annotation tasks',
        'Review completed tasks in your dashboard',
      ];
    } else if (user?.role === 'auditor') {
      return [
        'Wait for new annotation tasks to be completed',
        'Review your audit history',
        'Contact administrators if you need access to specific datasets',
      ];
    }

    return baseItems;
  };

  const getModeIcon = () => {
    if (isVerificationMode) {
      return <CheckCircle className="w-5 h-5 text-info-500" />;
    }
    return <Eye className="w-5 h-5 text-primary-500" />;
  };

  const getModeColor = () => {
    if (isVerificationMode) {
      return 'bg-info-100 text-info-700';
    }
    return 'bg-primary-100 text-primary-700';
  };

  return (
    <DashboardLayout title="No Tasks Available">
      <div className="container max-w-4xl">
        <div className="card">
          <div className="card-header bg-info-500 text-white">
            <h2 className="text-xl font-semibold flex items-center">
              <Inbox className="w-5 h-5 mr-2" />
              No Tasks Available
            </h2>
          </div>

          <div className="card-body">
            {/* Message Alert */}
            <div className="alert alert-info mb-6">
              <div className="flex items-start">
                <Inbox className="w-5 h-5 text-info-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-info-800 font-medium">{message}</p>
                </div>
              </div>
            </div>

            {/* Current Mode */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Current Status</h3>
              <div className="flex items-center space-x-4">
                <span className="text-gray-700">You are currently in</span>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getModeColor()}`}>
                  {getModeIcon()}
                  <span className="ml-2 capitalize">
                    {isVerificationMode ? 'Verification' : mode} Mode
                  </span>
                </span>
              </div>
            </div>

            {/* What You Can Do */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What you can do:</h3>
              <ul className="space-y-3">
                {getActionItems().map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
              <Link
                href={getDashboardLink()}
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
              >
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-200 transition-colors">
                  <Home className="w-5 h-5 text-primary-500" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Return to Dashboard</h4>
                  <p className="text-sm text-gray-600">Go back to your main dashboard</p>
                </div>
              </Link>

              <button
                onClick={() => window.location.reload()}
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
              >
                <div className="w-10 h-10 bg-success-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-success-200 transition-colors">
                  <RefreshCw className="w-5 h-5 text-success-500" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Refresh Page</h4>
                  <p className="text-sm text-gray-600">Check for new tasks</p>
                </div>
              </button>

              <a
                href="mailto:<EMAIL>"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors group"
              >
                <div className="w-10 h-10 bg-warning-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-warning-200 transition-colors">
                  <Mail className="w-5 h-5 text-warning-500" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Contact Support</h4>
                  <p className="text-sm text-gray-600">Get help from administrators</p>
                </div>
              </a>
            </div>

            {/* Additional Information */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                What happens next?
              </h4>
              <div className="space-y-2 text-gray-700">
                {user?.role === 'annotator' && (
                  <>
                    <p>• New annotation tasks will appear automatically when available</p>
                    <p>• You'll be notified when tasks are assigned to you</p>
                    <p>• Check your dashboard regularly for updates</p>
                  </>
                )}
                {user?.role === 'auditor' && (
                  <>
                    <p>• New audit tasks will appear when annotations are completed</p>
                    <p>• You'll be able to review and approve/reject annotations</p>
                    <p>• Check the available tasks page for new items to review</p>
                  </>
                )}
                {user?.role === 'admin' && (
                  <>
                    <p>• You can assign new tasks to annotators</p>
                    <p>• Check the admin dashboard for system status</p>
                    <p>• Manage user assignments and data connectors</p>
                  </>
                )}
              </div>
            </div>

            {/* Return Button */}
            <div className="mt-8 text-center">
              <Link href={getDashboardLink()} className="btn btn-primary">
                <Home className="w-4 h-4 mr-2" />
                Return to Dashboard
              </Link>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
