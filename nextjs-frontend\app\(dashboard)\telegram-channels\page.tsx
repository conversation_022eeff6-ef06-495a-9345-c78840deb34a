'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { api } from '@/lib/api-client';
import { 
  ArrowLeft,
  Phone,
  Key,
  Shield,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2,
  MessageSquare
} from 'lucide-react';
import Link from 'next/link';
import toast from 'react-hot-toast';

interface TelegramSession {
  connected: boolean;
  phone?: string;
  status: 'disconnected' | 'connecting' | 'verification_needed' | 'password_needed' | 'connected';
}

export default function TelegramChannelsPage() {
  const [session, setSession] = useState<TelegramSession>({
    connected: false,
    status: 'disconnected'
  });
  const [formData, setFormData] = useState({
    api_id: '',
    api_hash: '',
    phone: '',
    code: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [checkingSession, setCheckingSession] = useState(true);

  useEffect(() => {
    checkExistingSession();
  }, []);

  const checkExistingSession = async () => {
    try {
      const response = await api.telegram.getSession();
      if (response.data.success) {
        setSession(response.data.data);
      }
    } catch (error) {
      console.error('Failed to check session:', error);
    } finally {
      setCheckingSession(false);
    }
  };

  const handleConnect = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.api_id || !formData.api_hash || !formData.phone) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const response = await api.telegram.connect({
        api_id: formData.api_id,
        api_hash: formData.api_hash,
        phone: formData.phone
      });

      if (response.data.success) {
        setSession(prev => ({ ...prev, status: 'verification_needed', phone: formData.phone }));
        toast.success('Verification code sent to your phone');
      } else {
        toast.error(response.data.message || 'Failed to connect');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to connect');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.code) {
      toast.error('Please enter the verification code');
      return;
    }

    setLoading(true);
    try {
      const response = await api.telegram.verifyCode({
        code: formData.code
      });

      if (response.data.success) {
        if (response.data.data.requires_password) {
          setSession(prev => ({ ...prev, status: 'password_needed' }));
          toast.info('2FA password required');
        } else {
          setSession(prev => ({ ...prev, status: 'connected', connected: true }));
          toast.success('Successfully connected to Telegram!');
        }
      } else {
        toast.error(response.data.message || 'Invalid verification code');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to verify code');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.password) {
      toast.error('Please enter your 2FA password');
      return;
    }

    setLoading(true);
    try {
      const response = await api.telegram.verifyPassword({
        password: formData.password
      });

      if (response.data.success) {
        setSession(prev => ({ ...prev, status: 'connected', connected: true }));
        toast.success('Successfully connected to Telegram!');
        setFormData(prev => ({ ...prev, password: '' })); // Clear password
      } else {
        toast.error(response.data.message || 'Invalid password');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to verify password');
    } finally {
      setLoading(false);
    }
  };

  const handleResetSession = async () => {
    setLoading(true);
    try {
      const response = await api.telegram.resetSession();
      if (response.data.success) {
        setSession({ connected: false, status: 'disconnected' });
        setFormData({
          api_id: '',
          api_hash: '',
          phone: '',
          code: '',
          password: ''
        });
        toast.success('Session reset successfully');
      } else {
        toast.error('Failed to reset session');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to reset session');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  if (checkingSession) {
    return (
      <DashboardLayout requiredRole="admin" title="Telegram Channels">
        <div className="container">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredRole="admin" title="Telegram Channels">
      <div className="container max-w-4xl space-y-6">
        {/* Background Effects */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-purple-500/5 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-cyan-500/5 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Connect to Telegram</h1>
          <Link href="/admin/data-sources" className="btn btn-outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Data Sources
          </Link>
        </div>

        {/* Session Status */}
        {session.connected && (
          <div className="alert alert-success">
            <CheckCircle className="w-5 h-5 mr-2" />
            <div>
              <strong>Connected to Telegram</strong>
              {session.phone && <p className="text-sm mt-1">Phone: {session.phone}</p>}
            </div>
          </div>
        )}

        {/* Connection Form */}
        {session.status === 'disconnected' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold flex items-center">
                <MessageSquare className="w-5 h-5 mr-2" />
                Connect to Telegram
              </h3>
            </div>
            <div className="card-body">
              <form onSubmit={handleConnect} className="space-y-4">
                <div className="form-group">
                  <label htmlFor="api_id" className="form-label">
                    API ID <span className="text-error-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Key className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="api_id"
                      name="api_id"
                      value={formData.api_id}
                      onChange={handleChange}
                      className="form-input pl-10"
                      placeholder="Enter your Telegram API ID"
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="api_hash" className="form-label">
                    API Hash <span className="text-error-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Shield className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="api_hash"
                      name="api_hash"
                      value={formData.api_hash}
                      onChange={handleChange}
                      className="form-input pl-10"
                      placeholder="Enter your Telegram API Hash"
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="phone" className="form-label">
                    Phone Number <span className="text-error-500">*</span>
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="form-input pl-10"
                      placeholder="Enter your phone number"
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="flex justify-between pt-4">
                  <button
                    type="button"
                    onClick={handleResetSession}
                    className="btn btn-outline btn-danger"
                    disabled={loading}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Reset Session
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="btn btn-primary disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      'Connect'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Verification Code Form */}
        {session.status === 'verification_needed' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold flex items-center">
                <AlertCircle className="w-5 h-5 mr-2" />
                Enter Verification Code
              </h3>
            </div>
            <div className="card-body">
              <div className="alert alert-info mb-4">
                A verification code has been sent to your phone number: {session.phone}
              </div>
              <form onSubmit={handleVerifyCode} className="space-y-4">
                <div className="form-group">
                  <label htmlFor="code" className="form-label">
                    Verification Code <span className="text-error-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter the verification code"
                    required
                    disabled={loading}
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Code'
                  )}
                </button>
              </form>
            </div>
          </div>
        )}

        {/* 2FA Password Form */}
        {session.status === 'password_needed' && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Enter 2FA Password
              </h3>
            </div>
            <div className="card-body">
              <div className="alert alert-info mb-4">
                Your account has 2FA enabled. Please enter your password.
              </div>
              <form onSubmit={handleVerifyPassword} className="space-y-4">
                <div className="form-group">
                  <label htmlFor="password" className="form-label">
                    Password <span className="text-error-500">*</span>
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="form-input"
                    placeholder="Enter your 2FA password"
                    required
                    disabled={loading}
                  />
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="btn btn-primary disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Password'
                  )}
                </button>
              </form>
            </div>
          </div>
        )}

        {/* Connected State */}
        {session.connected && (
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold flex items-center">
                <CheckCircle className="w-5 h-5 mr-2 text-success-500" />
                Telegram Connected
              </h3>
            </div>
            <div className="card-body">
              <p className="text-gray-600 mb-4">
                You are now connected to Telegram. You can manage channels and download media content.
              </p>
              <div className="flex space-x-4">
                <Link href="/admin/data-sources/telegram/channels" className="btn btn-primary">
                  Manage Channels
                </Link>
                <button
                  onClick={handleResetSession}
                  className="btn btn-outline btn-danger"
                  disabled={loading}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Disconnect
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <h4 className="font-semibold text-blue-900 mb-3">How to get Telegram API credentials:</h4>
          <ol className="list-decimal list-inside text-blue-800 space-y-2">
            <li>Go to <a href="https://my.telegram.org" target="_blank" rel="noopener noreferrer" className="underline">my.telegram.org</a></li>
            <li>Log in with your phone number</li>
            <li>Go to "API development tools"</li>
            <li>Create a new application to get your API ID and API Hash</li>
          </ol>
        </div>
      </div>
    </DashboardLayout>
  );
}
