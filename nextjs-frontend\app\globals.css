@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    --primary-color: #4a6fa5;
    --secondary-color: #6d8cc7;
    --accent-color: #ffa500;
    --text-color: #333;
    --light-bg: #f5f7fa;
    --dark-bg: #2d3748;
    --border-color: #e2e8f0;
    --error-color: #e53e3e;
    --success-color: #38a169;
    --warning-color: #ed8936;
    --info-color: #4299e1;
  }

  * {
    @apply box-border;
  }

  html,
  body {
    @apply h-full font-sans text-gray-900 antialiased;
    font-family: 'Poppins', sans-serif;
    line-height: 1.1;
  }

  body {
    @apply min-h-screen flex flex-col bg-transparent;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center gap-2 px-4 py-2 rounded-md font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
  }

  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500;
  }

  .btn-danger {
    @apply bg-error-500 text-white hover:bg-error-600 focus:ring-error-500;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  /* Form Components */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 bg-white;
  }

  .form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 resize-vertical;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-md border;
  }

  .alert-success {
    @apply bg-success-50 border-success-200 text-success-800;
  }

  .alert-error {
    @apply bg-error-50 border-error-200 text-error-800;
  }

  .alert-warning {
    @apply bg-warning-50 border-warning-200 text-warning-800;
  }

  .alert-info {
    @apply bg-info-50 border-info-200 text-info-800;
  }

  /* Navigation */
  .navbar {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 shadow-medium;
  }

  .nav-link {
    @apply text-white/85 font-medium px-4 py-2 rounded transition-all duration-300 hover:text-white hover:bg-white/10;
  }

  .nav-link.active {
    @apply bg-white/20 text-white font-semibold;
  }

  /* Layout */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .main-content {
    @apply flex-1 py-8;
  }

  /* Login Page */
  .login-container {
    @apply min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-500 to-secondary-500;
  }

  .login-box {
    @apply w-full max-w-md p-8 bg-white rounded-xl shadow-strong;
  }

  /* File Browser */
  .file-browser {
    @apply bg-white rounded-lg shadow-soft overflow-hidden;
  }

  .file-list {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-5;
  }

  .file-item {
    @apply border border-gray-200 rounded-md p-4 transition-shadow duration-300 hover:shadow-medium;
  }

  .file-item.folder {
    @apply border-primary-200 bg-primary-50;
  }

  /* Annotation Interface */
  .annotation-container {
    @apply flex h-screen;
  }

  .annotation-sidebar {
    @apply w-80 bg-white border-r border-gray-200 p-5 overflow-y-auto;
  }

  .annotation-workspace {
    @apply flex-1 flex flex-col p-5 overflow-hidden;
  }

  .canvas-container {
    @apply flex-1 relative overflow-hidden bg-gray-100 rounded-md flex items-center justify-center min-h-96;
  }

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .annotation-container {
      @apply flex-col;
    }

    .annotation-sidebar {
      @apply w-full h-auto border-r-0 border-b border-gray-200 p-4 flex flex-wrap gap-5;
    }
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .shadow-soft {
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  }

  .shadow-medium {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  }

  .shadow-strong {
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
  }
}
