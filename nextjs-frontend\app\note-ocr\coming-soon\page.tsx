'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Brain, 
  ImageIcon, 
  Code, 
  Globe, 
  Twitter, 
  Linkedin,
  Rocket
} from 'lucide-react';

export default function ComingSoonPage() {
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; size: number; speed: number }>>([]);

  useEffect(() => {
    // Generate floating particles
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 2,
      speed: Math.random() * 2 + 1,
    }));
    setParticles(newParticles);

    // Animate particles
    const interval = setInterval(() => {
      setParticles(prev => prev.map(particle => ({
        ...particle,
        y: (particle.y + particle.speed * 0.1) % 100,
      })));
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const services = [
    {
      href: '/documind',
      icon: Brain,
      title: 'Documind',
      description: 'Intelligent document analysis and processing',
      color: 'from-blue-500 to-purple-500'
    },
    {
      href: '/note-ocr/extractor',
      icon: ImageIcon,
      title: 'Image Extractor',
      description: 'Extract content from images with advanced OCR',
      color: 'from-green-500 to-teal-500'
    },
    {
      href: '/synthetic',
      icon: Code,
      title: 'Syn Ground',
      description: 'Synthetic data generation and analysis',
      color: 'from-orange-500 to-red-500'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Background Grid */}
      <div className="fixed inset-0 bg-[url('/pattern.png')] opacity-5 pointer-events-none"></div>
      
      {/* Floating Shapes */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-cyan-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {particles.map(particle => (
          <div
            key={particle.id}
            className="absolute bg-white/20 rounded-full animate-pulse"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              animationDelay: `${particle.id * 0.1}s`,
            }}
          />
        ))}
      </div>

      {/* Back Button */}
      <div className="absolute top-8 left-8 z-10">
        <Link
          href="/note-ocr"
          className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors bg-gray-800/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-gray-700 hover:border-gray-600"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-20 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* Coming Soon Title */}
          <h1 className="text-6xl lg:text-8xl font-bold mb-8 bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent">
            Coming Soon
          </h1>
          
          <p className="text-xl lg:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto">
            We're working hard to bring you something amazing. Stay tuned for updates!
          </p>

          {/* Divider */}
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-12 rounded-full"></div>

          {/* Services Section */}
          <h2 className="text-3xl lg:text-4xl font-bold mb-12 text-white">
            Till then, try our other services
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {services.map((service, index) => (
              <Link
                key={index}
                href={service.href}
                className="group bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 hover:border-gray-600 transition-all duration-300 hover:transform hover:scale-105"
              >
                <div className="mb-6">
                  <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-r ${service.color} p-4 group-hover:scale-110 transition-transform duration-300`}>
                    <service.icon className="w-full h-full text-white" />
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-blue-400 transition-colors">
                  {service.title}
                </h3>
                
                <p className="text-gray-400 group-hover:text-gray-300 transition-colors">
                  {service.description}
                </p>
              </Link>
            ))}
          </div>

          {/* Get Started Button */}
          <div className="mb-16">
            <Link
              href="/login"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-4 rounded-lg transition-all duration-300 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <Rocket className="w-5 h-5" />
              <span>Get Started</span>
            </Link>
          </div>

          {/* Divider */}
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-12 rounded-full"></div>

          {/* Social Icons */}
          <div className="flex justify-center space-x-6">
            <a
              href="https://www.processvenue.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-gray-800/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-gray-700 hover:border-gray-600 text-gray-400 hover:text-white transition-all duration-300 hover:transform hover:scale-110"
              aria-label="Website"
            >
              <Globe className="w-5 h-5" />
            </a>
            
            <a
              href="https://twitter.com/processvenue"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-gray-800/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-gray-700 hover:border-gray-600 text-gray-400 hover:text-blue-400 transition-all duration-300 hover:transform hover:scale-110"
              aria-label="Twitter"
            >
              <Twitter className="w-5 h-5" />
            </a>
            
            <a
              href="https://www.linkedin.com/showcase/processvenue/about/"
              target="_blank"
              rel="noopener noreferrer"
              className="w-12 h-12 bg-gray-800/50 backdrop-blur-sm rounded-full flex items-center justify-center border border-gray-700 hover:border-gray-600 text-gray-400 hover:text-blue-600 transition-all duration-300 hover:transform hover:scale-110"
              aria-label="LinkedIn"
            >
              <Linkedin className="w-5 h-5" />
            </a>
          </div>

          {/* Footer Text */}
          <div className="mt-16 text-gray-500 text-sm">
            <p>© 2025 Process Venue. All rights reserved.</p>
          </div>
        </div>
      </div>

      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
    </div>
  );
}
