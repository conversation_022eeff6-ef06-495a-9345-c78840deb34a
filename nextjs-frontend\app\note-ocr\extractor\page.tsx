'use client';

import { useState, useRef, useCallback } from 'react';
import Link from 'next/link';
import { 
  Home,
  Upload,
  FileText,
  Edit,
  Download,
  Copy,
  Send,
  X,
  Check,
  MessageCircle,
  ChevronUp,
  ChevronDown,
  Folder,
  Wand2,
  Loader2
} from 'lucide-react';
import Image from 'next/image';
import toast from 'react-hot-toast';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

export default function ExtractorPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [extractedText, setExtractedText] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [mode, setMode] = useState<'standard' | 'custom' | 'chat' | null>(null);
  const [customPrompt, setCustomPrompt] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'system',
      content: 'You can ask questions about what you see in this image. For example: "What text appears in this image?" or "Describe what you see in this image."',
      timestamp: new Date(),
    }
  ]);
  const [chatInput, setChatInput] = useState('');
  const [isChatProcessing, setIsChatProcessing] = useState(false);
  const [showChat, setShowChat] = useState(true);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const handleFileSelect = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    setSelectedFile(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    setExtractedText('');
    setMode(null);
    
    // Reset chat messages to initial state
    setChatMessages([{
      id: '1',
      type: 'system',
      content: 'You can ask questions about what you see in this image. For example: "What text appears in this image?" or "Describe what you see in this image."',
      timestamp: new Date(),
    }]);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const extractText = async (prompt?: string) => {
    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    setIsProcessing(true);
    try {
      // Mock OCR processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const mockText = prompt 
        ? `Extracted text using custom prompt: "${prompt}"\n\nSample extracted content from the image...`
        : 'Sample extracted text from the image:\n\nThis is a demonstration of OCR text extraction. In a real implementation, this would contain the actual text extracted from your uploaded image using advanced OCR technology.';
      
      setExtractedText(mockText);
      setMode(prompt ? 'custom' : 'standard');
      toast.success('Text extracted successfully!');
    } catch (error) {
      toast.error('Failed to extract text');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleChatSubmit = async () => {
    if (!chatInput.trim() || !selectedFile) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date(),
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsChatProcessing(true);

    try {
      // Mock AI response
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Based on the image you uploaded, I can see that this appears to be a document or image with text content. Your question was: "${userMessage.content}". This is a mock response - in a real implementation, this would analyze your specific image and provide detailed answers about its contents.`,
        timestamp: new Date(),
      };

      setChatMessages(prev => [...prev, assistantMessage]);
      setMode('chat');
    } catch (error) {
      toast.error('Failed to process chat message');
    } finally {
      setIsChatProcessing(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(extractedText);
    toast.success('Text copied to clipboard!');
  };

  const downloadText = () => {
    const blob = new Blob([extractedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'extracted-text.txt';
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Text downloaded!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Image Extractor</h1>
          <Link
            href="/note-ocr"
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <Home className="w-4 h-4" />
            <span>Back to Dashboard</span>
          </Link>
        </div>
        <p className="text-gray-600 mt-1">Extract text from any image with powerful OCR technology</p>
      </div>

      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold">Upload Image</h3>
            </div>
            <div className="card-body">
              <div
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-500 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                {previewUrl ? (
                  <div className="space-y-4">
                    <div className="relative w-full h-48">
                      <Image
                        src={previewUrl}
                        alt="Preview"
                        fill
                        className="object-contain rounded-lg"
                      />
                    </div>
                    <p className="text-sm text-gray-600">{selectedFile?.name}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-gray-600">Drag and drop your image here</p>
                      <p className="text-gray-500 text-sm">or</p>
                      <button
                        type="button"
                        className="btn btn-outline mt-2"
                      >
                        <Folder className="w-4 h-4 mr-2" />
                        Browse Files
                      </button>
                    </div>
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>

              {selectedFile && (
                <div className="mt-6 space-y-4">
                  <h4 className="font-semibold text-gray-900">Choose Mode:</h4>
                  <div className="grid grid-cols-1 gap-3">
                    <button
                      onClick={() => extractText()}
                      disabled={isProcessing}
                      className="btn btn-primary disabled:opacity-50"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Wand2 className="w-4 h-4 mr-2" />
                          Extract with System Prompt
                        </>
                      )}
                    </button>

                    <button
                      onClick={() => setMode('custom')}
                      disabled={isProcessing}
                      className="btn btn-outline disabled:opacity-50"
                    >
                      <FileText className="w-4 h-4 mr-2" />
                      Extract with Custom Prompt
                    </button>

                    <button
                      onClick={() => setMode('chat')}
                      disabled={isProcessing}
                      className="btn btn-outline disabled:opacity-50"
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Chat with Image
                    </button>
                  </div>

                  {mode === 'custom' && (
                    <div className="space-y-3">
                      <label className="form-label">Enter custom extraction prompt:</label>
                      <textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        className="form-textarea"
                        rows={3}
                        placeholder="Extract all text from this image carefully..."
                      />
                      <button
                        onClick={() => extractText(customPrompt)}
                        disabled={isProcessing || !customPrompt.trim()}
                        className="btn btn-primary disabled:opacity-50"
                      >
                        <Wand2 className="w-4 h-4 mr-2" />
                        Extract with Custom Prompt
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Results Section */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center">
                  <FileText className="w-5 h-5 mr-2" />
                  Results
                </h3>
                {extractedText && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setIsEditing(!isEditing)}
                      className="btn btn-outline btn-sm"
                    >
                      <Edit className="w-4 h-4 mr-1" />
                      {isEditing ? 'Cancel' : 'Edit'}
                    </button>
                    <button
                      onClick={copyToClipboard}
                      className="btn btn-outline btn-sm"
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </button>
                    <button
                      onClick={downloadText}
                      className="btn btn-outline btn-sm"
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </button>
                  </div>
                )}
              </div>
            </div>
            <div className="card-body">
              {!selectedFile ? (
                <div className="text-center py-12 text-gray-500">
                  <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p>Upload an image to see extracted text here</p>
                </div>
              ) : mode === 'chat' ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold flex items-center">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Chat with your image
                    </h4>
                    <button
                      onClick={() => setShowChat(!showChat)}
                      className="btn btn-outline btn-sm"
                    >
                      {showChat ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                    </button>
                  </div>

                  {showChat && (
                    <div className="space-y-4">
                      <div className="h-64 overflow-y-auto border border-gray-200 rounded-lg p-4 space-y-3">
                        {chatMessages.map((message) => (
                          <div
                            key={message.id}
                            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div
                              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                message.type === 'user'
                                  ? 'bg-primary-500 text-white'
                                  : message.type === 'system'
                                  ? 'bg-gray-100 text-gray-700'
                                  : 'bg-gray-200 text-gray-800'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                            </div>
                          </div>
                        ))}
                        {isChatProcessing && (
                          <div className="flex justify-start">
                            <div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">
                              <div className="flex items-center space-x-2">
                                <Loader2 className="w-4 h-4 animate-spin" />
                                <span className="text-sm">Analyzing image...</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        <input
                          type="text"
                          value={chatInput}
                          onChange={(e) => setChatInput(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleChatSubmit()}
                          className="form-input flex-1"
                          placeholder="Ask about this image..."
                          disabled={isChatProcessing}
                        />
                        <button
                          onClick={handleChatSubmit}
                          disabled={!chatInput.trim() || isChatProcessing}
                          className="btn btn-primary disabled:opacity-50"
                        >
                          <Send className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : extractedText ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">Extracted Text</h4>
                    {isEditing && (
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setIsEditing(false)}
                          className="btn btn-outline btn-sm"
                        >
                          <X className="w-4 h-4 mr-1" />
                          Cancel
                        </button>
                        <button
                          onClick={() => {
                            setIsEditing(false);
                            toast.success('Changes saved!');
                          }}
                          className="btn btn-success btn-sm"
                        >
                          <Check className="w-4 h-4 mr-1" />
                          Save
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {isEditing ? (
                    <textarea
                      ref={textAreaRef}
                      value={extractedText}
                      onChange={(e) => setExtractedText(e.target.value)}
                      className="form-textarea h-64"
                    />
                  ) : (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 h-64 overflow-y-auto">
                      <pre className="whitespace-pre-wrap text-sm text-gray-800">{extractedText}</pre>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p>Choose a mode above to process your image</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
