'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  Home,
  FileText,
  Table,
  ImageIcon,
  Calculator,
  ArrowRight,
  Menu,
  X
} from 'lucide-react';

export default function NoteOCRHomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [typingText, setTypingText] = useState('');
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  const typingMessages = [
    'Human-AI Collaboration',
    'Intelligent Document Processing',
    'Structured Data Extraction',
    'PDF Content Analysis'
  ];

  useEffect(() => {
    const message = typingMessages[currentMessageIndex];
    let currentIndex = 0;
    
    const typingInterval = setInterval(() => {
      if (currentIndex <= message.length) {
        setTypingText(message.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        setTimeout(() => {
          setCurrentMessageIndex((prev) => (prev + 1) % typingMessages.length);
        }, 2000);
      }
    }, 100);

    return () => clearInterval(typingInterval);
  }, [currentMessageIndex]);

  const features = [
    {
      icon: FileText,
      title: 'Text Extraction',
      description: 'Accurately extract and organize text content from PDF documents with precise layout preservation.'
    },
    {
      icon: Table,
      title: 'Table Extraction',
      description: 'Identify and extract tables from PDFs with structure preservation, converting them to usable CSV format.'
    },
    {
      icon: ImageIcon,
      title: 'Image Extraction',
      description: 'Extract embedded images from PDF documents while maintaining quality and resolution.'
    },
    {
      icon: Calculator,
      title: 'Formula Extraction',
      description: 'Identify and extract mathematical formulas from technical documents with high precision.'
    }
  ];

  const steps = [
    {
      number: 1,
      title: 'PDF Document Input',
      description: 'Place PDF documents in the input directory for batch processing or submit individual files'
    },
    {
      number: 2,
      title: 'Automated Processing',
      description: 'The system analyzes document structure, detects elements, and applies specialized extraction for each element type'
    },
    {
      number: 3,
      title: 'Structured Output',
      description: 'Extracted content organized into dedicated folders for text, tables (CSV), images, and formulas with detailed timing reports'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20 pointer-events-none"></div>
      <div className="fixed inset-0 bg-[url('/pattern.png')] opacity-5 pointer-events-none"></div>

      {/* Floating Shapes */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-20 w-40 h-40 bg-cyan-500/10 rounded-full blur-xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-40 right-10 w-20 h-20 bg-pink-500/10 rounded-full blur-xl animate-pulse delay-500"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-indigo-500/5 rounded-full blur-2xl animate-pulse delay-1500"></div>
      </div>

      {/* Header */}
      <header className="relative z-50 bg-gray-900/80 backdrop-blur-sm border-b border-gray-800">
        <div className="container">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <Image
                  src="/img/PVlogo-1024x780.png"
                  alt="NOTE-OCR Logo"
                  width={40}
                  height={30}
                  className="h-8 w-auto"
                />
              </Link>
              <div className="hidden sm:block">
                <div className="text-sm font-medium text-white">
                  End to End Data Solutions
                </div>
                <div className="text-xs text-blue-400 font-semibold">
                  Human-AI Collaboration • Coming Soon
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </a>
              <Link href="/note-ocr/coming-soon" className="text-gray-300 hover:text-white transition-colors">
                PDF Processing
              </Link>
              <Link href="/note-ocr/extractor" className="text-gray-300 hover:text-white transition-colors">
                Image Extraction
              </Link>
              <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">
                How It Works
              </a>
            </nav>

            <div className="flex items-center space-x-4">
              <Link href="/" className="btn btn-outline text-white border-white hover:bg-white hover:text-gray-900">
                <Home className="w-4 h-4 mr-2" />
                Back to Home
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 text-gray-300 hover:text-white"
              >
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-800">
              <div className="flex flex-col space-y-2">
                <a
                  href="#features"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Features
                </a>
                <Link
                  href="/note-ocr/coming-soon"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  PDF Processing
                </Link>
                <Link
                  href="/note-ocr/extractor"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Image Extraction
                </Link>
                <a
                  href="#how-it-works"
                  className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  How It Works
                </a>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32">
        <div className="container relative">
          <div className="max-w-6xl mx-auto text-center">
            <h1 className="text-5xl lg:text-7xl font-bold mb-6">
              N<span className="text-4xl lg:text-5xl">OTE</span>OCR<br />
              <span className="text-gradient bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                PDF
              </span>{' '}
              E<span className="text-4xl lg:text-5xl">XTRACTION</span>
            </h1>

            <div className="mb-8 h-8">
              <span className="text-xl lg:text-2xl text-blue-400 font-medium">
                {typingText}
                <span className="animate-pulse">|</span>
              </span>
            </div>

            {/* Process Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <div className="w-12 h-12 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-blue-400 mb-2">Element Extraction</h3>
                <p className="text-gray-300">Text, Tables, Images & Formulas</p>
              </div>

              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <div className="w-12 h-12 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ArrowRight className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-purple-400 mb-2">Human Validation</h3>
                <p className="text-gray-300">Intelligent Layout Detection</p>
              </div>

              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
                <div className="w-12 h-12 bg-cyan-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Table className="w-6 h-6 text-cyan-400" />
                </div>
                <h3 className="text-xl font-semibold text-cyan-400 mb-2">Structured Output</h3>
                <p className="text-gray-300">Organized Data Files</p>
              </div>
            </div>

            {/* Typing Animation */}
            <div className="mb-12">
              <div className="text-lg text-gray-400 italic">
                "Extract structured content from complex PDF documents."
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/note-ocr/coming-soon"
                className="btn btn-primary bg-blue-600 hover:bg-blue-700 border-blue-600"
              >
                <FileText className="w-5 h-5 mr-2" />
                PDF Processing
              </Link>
              <Link
                href="/note-ocr/extractor"
                className="btn btn-primary bg-purple-600 hover:bg-purple-700 border-purple-600"
              >
                <ImageIcon className="w-5 h-5 mr-2" />
                Image Extraction
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative py-20 bg-gray-800/30">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-16">
            Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-8 border border-gray-700 hover:border-gray-600 transition-all duration-300 text-center"
              >
                <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="relative py-20">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-white mb-16">
            How It Works
          </h2>
          <div className="max-w-4xl mx-auto space-y-8">
            {steps.map((step, index) => (
              <div
                key={index}
                className="flex items-start space-x-6 bg-gray-800/30 backdrop-blur-sm rounded-lg p-8 border border-gray-700"
              >
                <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                  {step.number}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-3">{step.title}</h3>
                  <p className="text-gray-300">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative bg-gray-900/80 border-t border-gray-800 py-8">
        <div className="container text-center">
          <p className="text-gray-400">
            NOTE-OCR • Document Extraction Service • All Rights Reserved • © 2025
          </p>
        </div>
      </footer>
    </div>
  );
}
