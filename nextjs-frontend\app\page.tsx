'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  Database, 
  Shield, 
  Eye, 
  Users, 
  FolderOpen, 
  Lightbulb,
  ShieldCheck,
  Rocket,
  Book,
  Layers,
  FileImage,
  FileText,
  Globe,
  Linkedin,
  Twitter,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';

export default function LandingPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 pointer-events-none"></div>
      <div className="fixed inset-0 bg-[url('/pattern.png')] opacity-5 pointer-events-none"></div>

      {/* Header */}
      <header className="relative z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200">
        <div className="container">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <Image
                  src="/img/PVlogo-1024x780.png"
                  alt="ProcessVenue Logo"
                  width={40}
                  height={30}
                  className="h-8 w-auto"
                />
              </Link>
              <div className="hidden sm:block">
                <div className="text-sm font-medium text-gray-600">
                  Human Augmented AI Agents
                </div>
                <div className="text-xs text-primary-500 font-semibold">
                  Beta Version
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/" className="text-gray-700 hover:text-primary-500 transition-colors">
                Home
              </Link>
              <div className="relative group">
                <button className="text-gray-700 hover:text-primary-500 transition-colors">
                  Services
                </button>
                <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Link href="/synthetic" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                    SynGround
                  </Link>
                  <Link href="/documind" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                    Documind-o
                  </Link>
                  <Link href="/note-ocr" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                    NoteOCR
                  </Link>
                </div>
              </div>
              <Link href="#about" className="text-gray-700 hover:text-primary-500 transition-colors">
                About
              </Link>
              <Link href="#contact" className="text-gray-700 hover:text-primary-500 transition-colors">
                Contact
              </Link>
              <Link href="/login" className="btn btn-primary">
                Login
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-600 hover:text-gray-900"
            >
              <div className="w-6 h-6 flex flex-col justify-center space-y-1">
                <span className={`block h-0.5 w-6 bg-current transition-transform ${isMenuOpen ? 'rotate-45 translate-y-1.5' : ''}`}></span>
                <span className={`block h-0.5 w-6 bg-current transition-opacity ${isMenuOpen ? 'opacity-0' : ''}`}></span>
                <span className={`block h-0.5 w-6 bg-current transition-transform ${isMenuOpen ? '-rotate-45 -translate-y-1.5' : ''}`}></span>
              </div>
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden py-4 border-t border-gray-200">
              <div className="flex flex-col space-y-2">
                <Link href="/" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  Home
                </Link>
                <Link href="/synthetic" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  SynGround
                </Link>
                <Link href="/documind" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  Documind-o
                </Link>
                <Link href="/note-ocr" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  NoteOCR
                </Link>
                <Link href="#about" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  About
                </Link>
                <Link href="#contact" className="block px-4 py-2 text-gray-700 hover:bg-gray-50">
                  Contact
                </Link>
                <Link href="/login" className="block mx-4 mt-2 btn btn-primary text-center">
                  Login
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Floating Shapes */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-20 animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-secondary-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-accent-200 rounded-full opacity-20 animate-pulse delay-2000"></div>
          <div className="absolute bottom-40 right-10 w-12 h-12 bg-primary-300 rounded-full opacity-20 animate-pulse delay-500"></div>
        </div>

        <div className="container relative">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">DATA</span>{' '}
              <span className="text-2xl lg:text-4xl">ANALYTICS</span>{' '}
              <span className="text-gradient">&</span>{' '}
              <span className="text-gradient">D</span>
              <span className="text-2xl lg:text-4xl">ELIVERY</span>{' '}
              <span className="text-gradient">P</span>
              <span className="text-2xl lg:text-4xl">LATFORM</span>
            </h1>
            
            <h3 className="text-xl lg:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
              HAI-Agent platform with HITL for precise AI training Datasets & Enterprise Data Management.
            </h3>

            {/* Tagline Animation */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <div className="flex items-center justify-center px-6 py-3 bg-primary-50 rounded-lg border border-primary-200">
                <Shield className="w-5 h-5 text-primary-500 mr-3" />
                <span className="font-medium text-primary-700">Your Data</span>
              </div>
              <div className="flex items-center justify-center px-6 py-3 bg-success-50 rounded-lg border border-success-200">
                <ShieldCheck className="w-5 h-5 text-success-500 mr-3" />
                <span className="font-medium text-success-700">Our AI Processing</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 justify-center">
              <Link href="/login" className="btn btn-primary">
                <Rocket className="w-4 h-4 mr-2" />
                Get Started
              </Link>
              <Link href="#features" className="btn btn-outline">
                <Book className="w-4 h-4 mr-2" />
                Learn More
              </Link>
              <Link 
                href="https://huggingface.co/Process-Venue" 
                target="_blank"
                className="btn btn-outline"
              >
                <Database className="w-4 h-4 mr-2" />
                See Sample Dataset
              </Link>
            </div>

            {/* Service Links */}
            <div className="flex flex-wrap gap-3 justify-center mt-8">
              <Link href="/synthetic" className="btn btn-outline btn-sm">
                <Layers className="w-4 h-4 mr-2" />
                SynGround
              </Link>
              <Link href="/documind" className="btn btn-outline btn-sm">
                <FileImage className="w-4 h-4 mr-2" />
                Documind-o
              </Link>
              <Link href="/note-ocr" className="btn btn-outline btn-sm">
                <FileText className="w-4 h-4 mr-2" />
                NoteOCR
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Platform Features
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Lightbulb className="w-8 h-8 text-primary-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Efficient Annotation
              </h3>
              <p className="text-gray-600">
                Accelerate your workflow with fast, accurate, and intuitive data annotation.
              </p>
            </div>

            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShieldCheck className="w-8 h-8 text-success-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Auditing Mode
              </h3>
              <p className="text-gray-600">
                Ensure quality and compliance with seamless, real-time auditing capabilities.
              </p>
            </div>

            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-info-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-info-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Team Collaboration
              </h3>
              <p className="text-gray-600">
                Collaborate with your team in real-time, share annotations, and track progress.
              </p>
            </div>

            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FolderOpen className="w-8 h-8 text-warning-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                File Management
              </h3>
              <p className="text-gray-600">
                Organize and manage your data with ease.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20">
        <div className="container">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              About ProcessVenue
            </h2>
            <h3 className="text-xl text-gray-600 mb-8">
              Transforming Data into Actionable Intelligence
            </h3>
            <p className="text-lg text-gray-600 mb-12">
              Founded in 2014, ProcessVenue delivers secure, compliant, & AI-powered data solutions to businesses worldwide.
            </p>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <div className="card card-body text-left">
                <h4 className="text-xl font-semibold text-gray-900 mb-4">
                  Data Analytics & Delivery Platform (DADP)
                </h4>
                <p className="text-gray-600">
                  AI-powered platform with human-in-the-loop expertise for precise AI training Datasets & Agentic Process Automations.
                </p>
              </div>

              <div className="card card-body text-left">
                <h4 className="text-xl font-semibold text-gray-900 mb-4">
                  Business & Knowledge Process Outsourcing
                </h4>
                <p className="text-gray-600">
                  AI & ML driven back-office support for finance, customer service, content moderation, and data management.
                </p>
              </div>
            </div>

            <div className="card card-body">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">
                Compliance & Security
              </h4>
              <p className="text-gray-600 mb-6">
                We ensure global data protection with industry-leading certifications:
              </p>
              <div className="flex flex-wrap gap-3 justify-center">
                <span className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full font-medium">SOC2</span>
                <span className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full font-medium">HIPAA</span>
                <span className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full font-medium">GDPR</span>
                <span className="px-4 py-2 bg-primary-100 text-primary-700 rounded-full font-medium">ISO 27001</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Get in Touch
          </h2>

          {/* Social Links */}
          <div className="flex flex-wrap gap-4 justify-center mb-12">
            <Link 
              href="https://www.processvenue.com/" 
              target="_blank"
              className="btn btn-outline"
            >
              <Globe className="w-4 h-4 mr-2" />
              Visit Website
            </Link>
            <Link 
              href="https://www.linkedin.com/showcase/processvenue/about/" 
              target="_blank"
              className="btn btn-outline"
            >
              <Linkedin className="w-4 h-4 mr-2" />
              LinkedIn
            </Link>
            <Link 
              href="https://twitter.com/processvenue" 
              target="_blank"
              className="btn btn-outline"
            >
              <Twitter className="w-4 h-4 mr-2" />
              Twitter
            </Link>
          </div>

          {/* Contact Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-primary-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Email Us</h3>
              <Link 
                href="mailto:<EMAIL>"
                className="text-primary-500 hover:text-primary-600"
              >
                <EMAIL>
              </Link>
            </div>

            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="w-8 h-8 text-success-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Call Us</h3>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">India:</span>{' '}
                  <Link href="tel:+************" className="text-primary-500 hover:text-primary-600">
                    +91 ************
                  </Link>
                </div>
                <div>
                  <span className="font-medium">USA:</span>{' '}
                  <Link href="tel:+14156854332" className="text-primary-500 hover:text-primary-600">
                    ****** 685 4332
                  </Link>
                </div>
                <div>
                  <span className="font-medium">UK:</span>{' '}
                  <Link href="tel:+************" className="text-primary-500 hover:text-primary-600">
                    +44 20 3289 4232
                  </Link>
                </div>
              </div>
            </div>

            <div className="card card-body text-center">
              <div className="w-16 h-16 bg-info-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <MapPin className="w-8 h-8 text-info-500" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Visit Us</h3>
              <p className="text-gray-600 text-sm">
                130, New Sanganer Rd, opp. Metro Station,<br />
                Shiva Colony, Sodala,<br />
                Jaipur, Rajasthan 302019
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container text-center">
          <p className="text-gray-400">
            Data Analytics & Delivery Platform • All Rights Reserved • © {new Date().getFullYear()}
          </p>
        </div>
      </footer>
    </div>
  );
}
