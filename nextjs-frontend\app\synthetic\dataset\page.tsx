'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface DatasetType {
  id: string;
  name: string;
  description: string;
  icon: string;
  examples: string[];
  enabled: boolean;
}

interface QAPair {
  id: string;
  question: string;
  answer: string;
  source: string;
}

export default function SyntheticDatasetPage() {
  const [datasetTypes, setDatasetTypes] = useState<DatasetType[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<Set<string>>(new Set());
  const [customQueries, setCustomQueries] = useState<string[]>([]);
  const [newQuery, setNewQuery] = useState('');
  const [extractedKeywords, setExtractedKeywords] = useState<string[]>([]);
  const [qaPreview, setQaPreview] = useState<QAPair[]>([]);
  const [generating, setGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    initializeDatasetTypes();
    loadExtractedKeywords();
  }, []);

  const initializeDatasetTypes = () => {
    const types: DatasetType[] = [
      {
        id: 'qa',
        name: 'Question & Answer',
        description: 'Generate question-answer pairs from your documents',
        icon: 'fas fa-question-circle',
        examples: ['What is machine learning?', 'How does neural network work?'],
        enabled: true
      },
      {
        id: 'classification',
        name: 'Text Classification',
        description: 'Create labeled text samples for classification tasks',
        icon: 'fas fa-tags',
        examples: ['Positive/Negative sentiment', 'Topic categorization'],
        enabled: true
      },
      {
        id: 'summarization',
        name: 'Text Summarization',
        description: 'Generate summaries of varying lengths from source text',
        icon: 'fas fa-compress-alt',
        examples: ['Executive summaries', 'Key point extraction'],
        enabled: true
      },
      {
        id: 'ner',
        name: 'Named Entity Recognition',
        description: 'Extract and label entities like names, dates, locations',
        icon: 'fas fa-user-tag',
        examples: ['Person names', 'Organizations', 'Dates'],
        enabled: true
      },
      {
        id: 'paraphrasing',
        name: 'Paraphrasing',
        description: 'Generate alternative phrasings of the same content',
        icon: 'fas fa-sync-alt',
        examples: ['Sentence variations', 'Style adaptations'],
        enabled: true
      },
      {
        id: 'dialogue',
        name: 'Dialogue Generation',
        description: 'Create conversational exchanges based on content',
        icon: 'fas fa-comments',
        examples: ['Customer support', 'Educational conversations'],
        enabled: true
      }
    ];
    
    setDatasetTypes(types);
  };

  const loadExtractedKeywords = () => {
    // Mock extracted keywords - in real app, this would come from processed sources
    const keywords = [
      'machine learning', 'artificial intelligence', 'neural networks', 'deep learning',
      'data science', 'algorithms', 'python programming', 'statistics',
      'natural language processing', 'computer vision', 'reinforcement learning'
    ];
    setExtractedKeywords(keywords);
  };

  const toggleDatasetType = (typeId: string) => {
    setSelectedTypes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(typeId)) {
        newSet.delete(typeId);
      } else {
        newSet.add(typeId);
      }
      return newSet;
    });
  };

  const addCustomQuery = () => {
    if (newQuery.trim() && !customQueries.includes(newQuery.trim())) {
      setCustomQueries(prev => [...prev, newQuery.trim()]);
      setNewQuery('');
    }
  };

  const removeCustomQuery = (index: number) => {
    setCustomQueries(prev => prev.filter((_, i) => i !== index));
  };

  const addKeywordAsQuery = (keyword: string) => {
    if (!customQueries.includes(keyword)) {
      setCustomQueries(prev => [...prev, keyword]);
      toast.success(`Added "${keyword}" as custom query`);
    }
  };

  const generateDataset = async () => {
    if (selectedTypes.size === 0) {
      toast.error('Please select at least one dataset type');
      return;
    }

    setGenerating(true);
    try {
      // Mock generation process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock generated QA pairs
      const mockQAPairs: QAPair[] = [
        {
          id: '1',
          question: 'What is machine learning?',
          answer: 'Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed.',
          source: 'Document 1'
        },
        {
          id: '2',
          question: 'How do neural networks work?',
          answer: 'Neural networks are computing systems inspired by biological neural networks. They consist of interconnected nodes that process information using mathematical operations.',
          source: 'Document 2'
        },
        {
          id: '3',
          question: 'What are the applications of deep learning?',
          answer: 'Deep learning applications include image recognition, natural language processing, speech recognition, autonomous vehicles, and medical diagnosis.',
          source: 'Document 3'
        }
      ];
      
      setQaPreview(mockQAPairs);
      setShowPreview(true);
      toast.success('Dataset generated successfully!');
    } catch (error) {
      toast.error('Failed to generate dataset');
    } finally {
      setGenerating(false);
    }
  };

  const exportDataset = async (format: string) => {
    try {
      // Mock export process
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success(`Dataset exported as ${format.toUpperCase()}`);
    } catch (error) {
      toast.error('Failed to export dataset');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-purple-100 rounded-full opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-green-100 rounded-full opacity-20 animate-pulse delay-500"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Generate AI Training Dataset
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Create various types of training data from your documents for model training or evaluation
          </p>
          
          {/* Back to Upload Button */}
          <div className="mt-6">
            <button
              onClick={() => window.location.href = '/synthetic/upload'}
              className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
            >
              <i className="fas fa-arrow-left mr-2"></i>
              Back to Upload
            </button>
          </div>
        </div>

        {/* Dataset Type Selection */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              <i className="fas fa-database mr-2"></i>
              Select Dataset Types
            </h2>
            <p className="text-gray-600 mt-1">Choose the types of synthetic data you want to generate</p>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {datasetTypes.map((type) => (
                <div
                  key={type.id}
                  className={`relative p-6 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                    selectedTypes.has(type.id)
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => toggleDatasetType(type.id)}
                >
                  {/* Selection Indicator */}
                  <div className="absolute top-4 right-4">
                    <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      selectedTypes.has(type.id)
                        ? 'bg-primary-600 border-primary-600'
                        : 'border-gray-300'
                    }`}>
                      {selectedTypes.has(type.id) && (
                        <i className="fas fa-check text-white text-xs"></i>
                      )}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${
                    selectedTypes.has(type.id) ? 'bg-primary-100' : 'bg-gray-100'
                  }`}>
                    <i className={`${type.icon} text-xl ${
                      selectedTypes.has(type.id) ? 'text-primary-600' : 'text-gray-600'
                    }`}></i>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{type.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{type.description}</p>
                  
                  {/* Examples */}
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Examples:</p>
                    {type.examples.map((example, index) => (
                      <p key={index} className="text-xs text-gray-500">• {example}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Custom Queries */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              <i className="fas fa-edit mr-2"></i>
              Custom Queries & Topics
            </h2>
            <p className="text-gray-600 mt-1">Add specific questions or topics to focus the dataset generation</p>
          </div>
          
          <div className="p-6">
            {/* Add Custom Query */}
            <div className="flex space-x-3 mb-6">
              <input
                type="text"
                value={newQuery}
                onChange={(e) => setNewQuery(e.target.value)}
                placeholder="Enter a custom query or topic..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                onKeyPress={(e) => e.key === 'Enter' && addCustomQuery()}
              />
              <button
                onClick={addCustomQuery}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
              >
                <i className="fas fa-plus mr-2"></i>
                Add
              </button>
            </div>

            {/* Custom Queries List */}
            {customQueries.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Added Queries:</h3>
                <div className="flex flex-wrap gap-2">
                  {customQueries.map((query, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                    >
                      {query}
                      <button
                        onClick={() => removeCustomQuery(index)}
                        className="ml-2 text-primary-600 hover:text-primary-800"
                      >
                        <i className="fas fa-times text-xs"></i>
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Keywords Suggestions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              <i className="fas fa-tags mr-2"></i>
              Suggested Topics
            </h2>
            <p className="text-gray-600 mt-1">Click on keywords to add them as custom queries or topics</p>
          </div>
          
          <div className="p-6">
            {extractedKeywords.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {extractedKeywords.map((keyword, index) => (
                  <button
                    key={index}
                    onClick={() => addKeywordAsQuery(keyword)}
                    className="inline-flex items-center px-3 py-2 rounded-md text-sm bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                  >
                    <i className="fas fa-plus mr-2 text-xs"></i>
                    {keyword}
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <i className="fas fa-spinner fa-spin text-2xl text-gray-400 mb-2"></i>
                <p className="text-gray-500">Extracting keywords from sources...</p>
              </div>
            )}
          </div>
        </div>

        {/* Generation Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              <i className="fas fa-cog mr-2"></i>
              Generation Settings
            </h2>
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Number of Samples
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option value="50">50 samples</option>
                  <option value="100">100 samples</option>
                  <option value="250">250 samples</option>
                  <option value="500">500 samples</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quality Level
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option value="standard">Standard</option>
                  <option value="high">High Quality</option>
                  <option value="premium">Premium</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Diversity Level
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </select>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                onClick={generateDataset}
                disabled={selectedTypes.size === 0 || generating}
                className="px-8 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {generating ? (
                  <>
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Generating Dataset...
                  </>
                ) : (
                  <>
                    <i className="fas fa-magic mr-2"></i>
                    Generate Synthetic Dataset
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">
                <i className="fas fa-eye mr-2"></i>
                Dataset Preview
              </h2>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  <strong>{qaPreview.length}</strong> pairs generated
                </span>
                <button
                  onClick={generateDataset}
                  className="px-3 py-1 bg-gray-100 text-gray-700 rounded text-sm hover:bg-gray-200 transition-colors"
                >
                  <i className="fas fa-sync-alt mr-1"></i>
                  Regenerate
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="space-y-4 mb-6">
                {qaPreview.map((pair) => (
                  <div key={pair.id} className="bg-gray-50 rounded-lg p-4">
                    <div className="mb-3">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Question</span>
                      <p className="text-gray-900 mt-1">{pair.question}</p>
                    </div>
                    <div className="mb-3">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Answer</span>
                      <p className="text-gray-700 mt-1">{pair.answer}</p>
                    </div>
                    <div className="text-xs text-gray-500">
                      Source: {pair.source}
                    </div>
                  </div>
                ))}
              </div>

              {/* Export Options */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Export Dataset</h3>
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={() => exportDataset('json')}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <i className="fas fa-file-code mr-2"></i>
                    Export as JSON
                  </button>
                  <button
                    onClick={() => exportDataset('csv')}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    <i className="fas fa-file-csv mr-2"></i>
                    Export as CSV
                  </button>
                  <button
                    onClick={() => exportDataset('xlsx')}
                    className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  >
                    <i className="fas fa-file-excel mr-2"></i>
                    Export as Excel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
