'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  FileText, 
  Settings, 
  Download, 
  Zap,
  Home,
  ChevronDown,
  Rocket,
  Bot,
  Cpu,
  Brain,
  Server,
  HelpCircle,
  Tag,
  Book,
  Layers,
  List,
  Scale,
  Sitemap,
  Code,
  Gavel,
  ArrowRight,
  RotateCcw,
  Target,
  Folder,
  Wand2,
  Share,
  TrendingUp,
  GraduationCap
} from 'lucide-react';

export default function SyntheticHomePage() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const datasetTypes = [
    { icon: HelpCircle, title: 'Question-Answer Pairs', description: 'Standard format with questions and corresponding correct answers' },
    { icon: Tag, title: 'Entity Extraction', description: 'Identify and extract named entities, terms, and key information points' },
    { icon: Book, title: 'Concept Definitions', description: 'Explanations and definitions of key terms and concepts' },
    { icon: Layers, title: 'Summarization', description: 'Condensed versions of longer texts capturing key information' },
    { icon: List, title: 'Procedures', description: 'Step-by-step instructions for completing specific tasks' },
    { icon: Scale, title: 'Comparisons', description: 'Contrasting different items, concepts, or approaches' },
    { icon: Sitemap, title: 'Role Relationships', description: 'Connections and interactions between different entities' },
    { icon: Code, title: 'Code Examples', description: 'Examples of code snippets based on the topic from the source' },
    { icon: Gavel, title: 'Fact vs Opinion', description: 'Classification of statements as factual or subjective' },
    { icon: ArrowRight, title: 'Cause-Effect Relationships', description: 'Identifying connections between causes and their results' },
    { icon: RotateCcw, title: 'Paraphrases', description: 'Alternative ways to express the same information' },
    { icon: Target, title: 'Intent Detection', description: 'Identifying the purpose or goal behind a piece of text' },
    { icon: Folder, title: 'Topic Classification', description: 'Categorizing content according to subject matter' },
  ];

  const models = [
    { icon: Bot, title: 'OpenAI', description: 'Advanced GPT models for generating high-quality, contextual question-answer pairs' },
    { icon: Cpu, title: 'Claude', description: 'Specialized in detailed and nuanced responses for complex domain knowledge' },
    { icon: Brain, title: 'Gemini', description: "Google's multimodal model with strong reasoning and content generation capabilities" },
    { icon: Server, title: 'Deepseek r1', description: 'Run various open-source models locally using Ollama with customizable parameters' },
  ];

  const features = [
    { icon: FileText, title: 'Source Analysis', description: 'Upload PDFs, text files, and other documents like web page urls to generate comprehensive datasets' },
    { icon: Settings, title: 'Customizable Settings', description: 'Configure number of QA pairs, difficulty levels, and query (optional)' },
    { icon: Download, title: 'Export Options', description: 'Download your generated datasets in multiple formats for training or evaluation' },
    { icon: Zap, title: 'Model Distillation', description: 'Create compact datasets for transferring knowledge from large to smaller models' },
  ];

  const steps = [
    { number: 1, title: 'Select Dataset Types', description: 'Choose from multiple dataset types based on your specific needs' },
    { number: 2, title: 'Upload Sources', description: 'Add documents or web pages to generate datasets from' },
    { number: 3, title: 'Add Keywords (Optional)', description: 'Provide specific keywords as queries to focus the dataset generation' },
    { number: 4, title: 'Select AI Model', description: 'Choose from OpenAI, Claude, Gemini, or Ollama based on your requirements' },
    { number: 5, title: 'Configure Settings', description: 'Set the number of items to generate and difficulty level' },
    { number: 6, title: 'Generate Dataset', description: 'Create your custom dataset using the selected parameters' },
    { number: 7, title: 'Download Results', description: 'Export your dataset in various formats including JSON, CSV, and TXT' },
  ];

  const distillationTypes = [
    { icon: Wand2, title: 'Data-Free Knowledge Distillation', description: 'Transfer knowledge from a teacher model to a student model without relying on original training data by generating synthetic data to guide the student\'s learning' },
    { icon: Share, title: 'Response Based Knowledge Distillation', description: 'Transfers information from the final output layer of the teacher model by training the student model to output logits that match the teacher model\'s predictions' },
    { icon: Layers, title: 'Feature Based Knowledge Distillation', description: 'Focuses on information conveyed in intermediate layers where neural networks perform feature extraction, training the student to learn the same features as the teacher' },
    { icon: Sitemap, title: 'Relation Based Knowledge Distillation', description: 'Focuses on the relationships between different layers or between feature maps representing the activations at different layers or locations' },
  ];

  const useCases = [
    { icon: Brain, title: 'AI Model Training', description: 'Create training datasets to improve AI model performance on domain-specific knowledge' },
    { icon: TrendingUp, title: 'Performance Evaluation', description: 'Generate test sets to evaluate how well AI models understand your content' },
    { icon: GraduationCap, title: 'Educational Resources', description: 'Create Q&A materials for educational purposes or knowledge assessment' },
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Background Elements */}
      <div className="fixed inset-0 bg-gradient-to-br from-primary-500/5 to-secondary-500/5 pointer-events-none"></div>

      {/* Navigation */}
      <header className="relative z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200">
        <div className="container">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center">
                <Image
                  src="/img/PVlogo-1024x780.png"
                  alt="ProcessVenue Logo"
                  width={40}
                  height={30}
                  className="h-8 w-auto"
                />
              </Link>
              <div className="hidden sm:block">
                <div className="text-sm font-medium text-gray-900">
                  End to End Synthetic Dataset Solutions
                </div>
                <div className="text-xs text-primary-500 font-semibold">
                  Human-AI Collaboration • BETA VERSION
                </div>
              </div>
            </div>

            <nav className="hidden md:flex items-center space-x-8">
              <a href="#top" className="text-gray-700 hover:text-primary-500 transition-colors">Home</a>
              <a href="#features" className="text-gray-700 hover:text-primary-500 transition-colors">Features</a>
              <a href="#how-it-works" className="text-gray-700 hover:text-primary-500 transition-colors">How It Works</a>
              <a href="#dataset-types" className="text-gray-700 hover:text-primary-500 transition-colors">Datasets</a>
              <a href="#model-distillation" className="text-gray-700 hover:text-primary-500 transition-colors">Distillation</a>
              <a href="#use-cases" className="text-gray-700 hover:text-primary-500 transition-colors">Use Cases</a>
            </nav>

            <Link href="/" className="btn btn-outline">
              <Home className="w-4 h-4 mr-2" />
              Back to DADP
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section id="top" className="relative py-20 lg:py-32 overflow-hidden">
        <div className="container relative">
          <div className={`max-w-4xl mx-auto text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">SynGround</span>
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 mb-8">
              Accelerate model training with high-quality synthetic data and efficient knowledge transfer
            </p>

            <div className="space-y-4 mb-12">
              <div className="text-lg text-primary-600 font-medium">Generate synthetic datasets from any source</div>
              <div className="text-lg text-secondary-600 font-medium">Train and optimize AI models efficiently</div>
              <div className="text-lg text-accent-600 font-medium">Transfer knowledge between models seamlessly</div>
            </div>

            <Link href="/login" className="btn btn-primary btn-lg">
              <Rocket className="w-5 h-5 mr-2" />
              Get Started
            </Link>

            <div className="mt-12">
              <a href="#features" className="text-gray-400 hover:text-gray-600 transition-colors">
                <ChevronDown className="w-6 h-6 mx-auto animate-bounce" />
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Dataset Generation Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card card-body text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-8 h-8 text-primary-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            How Dataset Generation Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {steps.map((step, index) => (
              <div key={index} className="card card-body text-center">
                <div className="w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {step.number}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Models Available Section */}
      <section id="models" className="py-20 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Models Available
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {models.map((model, index) => (
              <div key={index} className="card card-body text-center">
                <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <model.icon className="w-8 h-8 text-secondary-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{model.title}</h3>
                <p className="text-gray-600">{model.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Dataset Types Section */}
      <section id="dataset-types" className="py-20">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Types of Datasets
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {datasetTypes.map((type, index) => (
              <div key={index} className="card card-body text-center">
                <div className="w-12 h-12 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <type.icon className="w-6 h-6 text-accent-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{type.title}</h3>
                <p className="text-gray-600 text-sm">{type.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Model Distillation Section */}
      <section id="model-distillation" className="py-20 bg-gray-50">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-8">
            Model Distillation
          </h2>
          <div className="max-w-4xl mx-auto text-center mb-16">
            <p className="text-lg text-gray-600">
              Model Distillation is a compression technique where a large Teacher Model transfers its knowledge to a smaller Student Model while keeping performance high. The goal is to make AI models smaller, faster, and more efficient for real-world applications.
            </p>
          </div>

          <h3 className="text-2xl font-bold text-center text-gray-900 mb-8">Types of Model Distillation</h3>

          <div className="mb-12">
            <h4 className="text-xl font-semibold text-gray-900 mb-6">When no real data available:</h4>
            <div className="grid grid-cols-1 gap-8">
              <div className="card card-body text-center">
                <div className="w-16 h-16 bg-warning-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Wand2 className="w-8 h-8 text-warning-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Data-Free Knowledge Distillation</h3>
                <p className="text-gray-600">Transfer knowledge from a teacher model to a student model without relying on original training data by generating synthetic data to guide the student's learning</p>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-xl font-semibold text-gray-900 mb-6">When real data is available:</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {distillationTypes.slice(1).map((type, index) => (
                <div key={index} className="card card-body text-center">
                  <div className="w-16 h-16 bg-info-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <type.icon className="w-8 h-8 text-info-500" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{type.title}</h3>
                  <p className="text-gray-600">{type.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section id="use-cases" className="py-20">
        <div className="container">
          <h2 className="text-3xl lg:text-4xl font-bold text-center text-gray-900 mb-16">
            Dataset Use Cases
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {useCases.map((useCase, index) => (
              <div key={index} className="card card-body text-center">
                <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <useCase.icon className="w-8 h-8 text-success-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{useCase.title}</h3>
                <p className="text-gray-600">{useCase.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container text-center">
          <p className="text-gray-400">
            © 2025 Process Venue. All rights reserved.
          </p>
        </div>
      </footer>

      {/* Back to Top Button */}
      <a
        href="#top"
        className="fixed bottom-8 right-8 w-12 h-12 bg-primary-500 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-primary-600 transition-colors z-50"
      >
        <ChevronDown className="w-5 h-5 rotate-180" />
      </a>
    </div>
  );
}
