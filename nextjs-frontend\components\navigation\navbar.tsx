'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth-context';
import { Menu, X, ChevronDown, Database, Shield, Eye, User } from 'lucide-react';

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const isActive = (path: string) => pathname === path;

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="w-4 h-4" />;
      case 'auditor':
        return <Eye className="w-4 h-4" />;
      default:
        return <User className="w-4 h-4" />;
    }
  };

  const getNavItems = () => {
    if (!user) return [];

    const items = [];

    if (user.role === 'admin') {
      items.push(
        {
          label: 'Dashboard',
          href: '/admin/dashboard',
          icon: 'bi-speedometer2',
        },
        {
          label: 'Users',
          href: '/admin/users',
          icon: 'bi-people-fill',
        }
      );
    }

    if (user.role === 'auditor') {
      items.push(
        {
          label: 'Auditor Dashboard',
          href: '/auditor/dashboard',
          icon: 'bi-speedometer2',
        },
        {
          label: 'Audit History',
          href: '/auditor/history',
          icon: 'bi-clock-history',
        }
      );
    }

    if (user.role === 'annotator') {
      items.push(
        {
          label: 'Dashboard',
          href: '/annotator/dashboard',
          icon: 'bi-grid-1x2-fill',
        }
      );
    }

    return items;
  };

  const navItems = getNavItems();

  return (
    <nav className="navbar">
      <div className="container">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 text-white font-bold text-xl">
            <Database className="w-6 h-6" />
            <span>DADP</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`nav-link ${isActive(item.href) ? 'active' : ''}`}
              >
                <i className={`bi ${item.icon} mr-2`}></i>
                {item.label}
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <>
                {/* Role Badge */}
                <div className="flex items-center space-x-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm">
                  {getRoleIcon(user.role)}
                  <span className="capitalize">{user.role}</span>
                </div>

                {/* User Dropdown */}
                <div className="relative">
                  <button
                    onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                    className="flex items-center space-x-2 text-white hover:text-white/80 transition-colors"
                  >
                    <User className="w-5 h-5" />
                    <span>{user.full_name || user.username}</span>
                    <ChevronDown className="w-4 h-4" />
                  </button>

                  {isUserMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50">
                      {user.role === 'admin' && (
                        <>
                          <Link
                            href="/admin/users"
                            className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <i className="bi bi-people mr-2 text-primary-500"></i>
                            Manage Users
                          </Link>
                          <hr className="my-1" />
                        </>
                      )}
                      <Link
                        href="/change-password"
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <i className="bi bi-key mr-2 text-primary-500"></i>
                        Change Password
                      </Link>
                      <hr className="my-1" />
                      <button
                        onClick={() => {
                          logout();
                          setIsUserMenuOpen(false);
                        }}
                        className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      >
                        <i className="bi bi-box-arrow-right mr-2 text-primary-500"></i>
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <Link
                href="/login"
                className="nav-link"
              >
                <i className="bi bi-box-arrow-in-right mr-2"></i>
                Login
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-white hover:text-white/80 transition-colors"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-white/20">
            <div className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`nav-link ${isActive(item.href) ? 'active' : ''}`}
                  onClick={() => setIsOpen(false)}
                >
                  <i className={`bi ${item.icon} mr-2`}></i>
                  {item.label}
                </Link>
              ))}

              {user ? (
                <>
                  <div className="border-t border-white/20 pt-2 mt-2">
                    <div className="flex items-center space-x-2 px-4 py-2 text-white text-sm">
                      {getRoleIcon(user.role)}
                      <span className="capitalize">{user.role}</span>
                      <span>•</span>
                      <span>{user.full_name || user.username}</span>
                    </div>
                  </div>
                  
                  {user.role === 'admin' && (
                    <Link
                      href="/admin/users"
                      className="nav-link"
                      onClick={() => setIsOpen(false)}
                    >
                      <i className="bi bi-people mr-2"></i>
                      Manage Users
                    </Link>
                  )}
                  
                  <Link
                    href="/change-password"
                    className="nav-link"
                    onClick={() => setIsOpen(false)}
                  >
                    <i className="bi bi-key mr-2"></i>
                    Change Password
                  </Link>
                  
                  <button
                    onClick={() => {
                      logout();
                      setIsOpen(false);
                    }}
                    className="nav-link text-left w-full"
                  >
                    <i className="bi bi-box-arrow-right mr-2"></i>
                    Logout
                  </button>
                </>
              ) : (
                <Link
                  href="/login"
                  className="nav-link"
                  onClick={() => setIsOpen(false)}
                >
                  <i className="bi bi-box-arrow-in-right mr-2"></i>
                  Login
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
