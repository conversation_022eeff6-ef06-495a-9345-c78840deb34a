'use client';

import { useState, useEffect } from 'react';
import { X, CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';
import { AlertProps } from '@/types';

export function Alert({ 
  type, 
  message, 
  dismissible = true, 
  onDismiss 
}: AlertProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (dismissible) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [dismissible]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) {
      onDismiss();
    }
  };

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-success-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-error-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-warning-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-info-500" />;
      default:
        return <Info className="w-5 h-5 text-info-500" />;
    }
  };

  const getAlertClasses = () => {
    const baseClasses = 'alert flex items-center justify-between rounded-lg border-none shadow-sm';
    
    switch (type) {
      case 'success':
        return `${baseClasses} alert-success`;
      case 'error':
        return `${baseClasses} alert-error`;
      case 'warning':
        return `${baseClasses} alert-warning`;
      case 'info':
        return `${baseClasses} alert-info`;
      default:
        return `${baseClasses} alert-info`;
    }
  };

  return (
    <div className={getAlertClasses()}>
      <div className="flex items-center space-x-3">
        {getIcon()}
        <div className="font-medium">
          {message}
        </div>
      </div>
      
      {dismissible && (
        <button
          onClick={handleDismiss}
          className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Close"
        >
          <X className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}

interface FlashMessagesProps {
  messages?: Array<{
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
  }>;
}

export function FlashMessages({ messages = [] }: FlashMessagesProps) {
  const [visibleMessages, setVisibleMessages] = useState(messages);

  const removeMessage = (index: number) => {
    setVisibleMessages(prev => prev.filter((_, i) => i !== index));
  };

  if (visibleMessages.length === 0) return null;

  return (
    <div className="container mt-4 space-y-3">
      {visibleMessages.map((message, index) => (
        <Alert
          key={index}
          type={message.type}
          message={message.message}
          onDismiss={() => removeMessage(index)}
        />
      ))}
    </div>
  );
}
