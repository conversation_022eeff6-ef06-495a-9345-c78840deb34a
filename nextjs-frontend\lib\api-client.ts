import axios from 'axios';
import { ApiResponse } from '@/types';

// Create axios instance with base configuration
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('auth_token');
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// API helper functions
export const api = {
  // Authentication
  auth: {
    login: (credentials: { username: string; password: string }) =>
      apiClient.post<ApiResponse>('/auth/login', credentials),
    
    logout: () =>
      apiClient.post<ApiResponse>('/auth/logout'),
    
    me: () =>
      apiClient.get<ApiResponse>('/auth/me'),
    
    changePassword: (data: { current_password: string; new_password: string }) =>
      apiClient.post<ApiResponse>('/auth/change-password', data),
  },

  // User management
  users: {
    list: (params?: { page?: number; per_page?: number; role?: string }) =>
      apiClient.get<ApiResponse>('/users', { params }),
    
    create: (userData: any) =>
      apiClient.post<ApiResponse>('/users', userData),
    
    update: (id: string, userData: any) =>
      apiClient.put<ApiResponse>(`/users/${id}`, userData),
    
    delete: (id: string) =>
      apiClient.delete<ApiResponse>(`/users/${id}`),
    
    get: (id: string) =>
      apiClient.get<ApiResponse>(`/users/${id}`),
  },

  // File management
  files: {
    browse: (path?: string) =>
      apiClient.get<ApiResponse>('/files/browse', { params: { path } }),
    
    upload: (formData: FormData) =>
      apiClient.post<ApiResponse>('/files/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      }),
    
    delete: (path: string) =>
      apiClient.delete<ApiResponse>('/files', { params: { path } }),
  },

  // Annotation tasks
  annotations: {
    list: (params?: { status?: string; assigned_to?: string; page?: number }) =>
      apiClient.get<ApiResponse>('/annotations', { params }),
    
    get: (id: string) =>
      apiClient.get<ApiResponse>(`/annotations/${id}`),
    
    create: (data: any) =>
      apiClient.post<ApiResponse>('/annotations', data),
    
    update: (id: string, data: any) =>
      apiClient.put<ApiResponse>(`/annotations/${id}`, data),
    
    save: (id: string, annotationData: any) =>
      apiClient.post<ApiResponse>(`/annotations/${id}/save`, annotationData),
    
    submit: (id: string) =>
      apiClient.post<ApiResponse>(`/annotations/${id}/submit`),
  },

  // Audit tasks
  audits: {
    list: (params?: { status?: string; page?: number }) =>
      apiClient.get<ApiResponse>('/audits', { params }),
    
    get: (id: string) =>
      apiClient.get<ApiResponse>(`/audits/${id}`),
    
    approve: (id: string, feedback?: string) =>
      apiClient.post<ApiResponse>(`/audits/${id}/approve`, { feedback }),
    
    reject: (id: string, feedback: string) =>
      apiClient.post<ApiResponse>(`/audits/${id}/reject`, { feedback }),
  },

  // Data connectors
  connectors: {
    nas: {
      connect: (config: any) =>
        apiClient.post<ApiResponse>('/connectors/nas/connect', config),
      
      disconnect: () =>
        apiClient.post<ApiResponse>('/connectors/nas/disconnect'),
      
      status: () =>
        apiClient.get<ApiResponse>('/connectors/nas/status'),
      
      browse: (path?: string) =>
        apiClient.get<ApiResponse>('/connectors/nas/browse', { params: { path } }),
    },

    googleDrive: {
      configure: (config: any) =>
        apiClient.post<ApiResponse>('/connectors/google-drive/configure', config),
      
      status: () =>
        apiClient.get<ApiResponse>('/connectors/google-drive/status'),
      
      reset: () =>
        apiClient.post<ApiResponse>('/connectors/google-drive/reset'),
    },
  },

  // Dashboard
  dashboard: {
    stats: () =>
      apiClient.get<ApiResponse>('/dashboard/stats'),
    
    userStats: (userId?: string) =>
      apiClient.get<ApiResponse>('/dashboard/user-stats', { params: { user_id: userId } }),
  },
};
