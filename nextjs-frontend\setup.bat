@echo off
echo 🚀 Setting up DADP Next.js Frontend...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Node.js version: 
node --version

REM Install dependencies
echo 📦 Installing dependencies...
where yarn >nul 2>&1
if %errorlevel% equ 0 (
    echo Using Yarn...
    yarn install
) else (
    echo Using npm...
    npm install
)

REM Copy environment file if it doesn't exist
if not exist .env.local (
    echo 📝 Creating environment file...
    copy .env.example .env.local
    echo ✅ Created .env.local from .env.example
    echo ⚠️  Please update the environment variables in .env.local
) else (
    echo ✅ Environment file already exists
)

REM Create public/img directory
echo 📁 Setting up static assets...
if not exist public\img mkdir public\img

REM Check if original static directory exists
if exist ..\static\img (
    echo 📋 Copying images from ..\static\img...
    xcopy ..\static\img\* public\img\ /E /I /Y >nul 2>&1
    echo ✅ Images copied to public\img\
) else (
    echo ⚠️  Original static\img directory not found. Please copy images manually to public\img\
)

echo.
echo 🎉 Setup complete!
echo.
echo Next steps:
echo 1. Update environment variables in .env.local
echo 2. Ensure your backend API is running on http://localhost:5000
echo 3. Copy any missing images to public\img\
echo 4. Run 'npm run dev' or 'yarn dev' to start the development server
echo.
echo 📚 For more information, see README.md
pause
