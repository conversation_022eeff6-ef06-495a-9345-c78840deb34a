#!/bin/bash

# DADP Frontend Setup Script
echo "🚀 Setting up DADP Next.js Frontend..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install dependencies
echo "📦 Installing dependencies..."
if command -v yarn &> /dev/null; then
    echo "Using Yarn..."
    yarn install
else
    echo "Using npm..."
    npm install
fi

# Copy environment file if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating environment file..."
    cp .env.example .env.local
    echo "✅ Created .env.local from .env.example"
    echo "⚠️  Please update the environment variables in .env.local"
else
    echo "✅ Environment file already exists"
fi

# Create public/img directory and copy assets
echo "📁 Setting up static assets..."
mkdir -p public/img

# Check if original static directory exists
if [ -d "../static/img" ]; then
    echo "📋 Copying images from ../static/img..."
    cp -r ../static/img/* public/img/ 2>/dev/null || echo "⚠️  Some images could not be copied"
    echo "✅ Images copied to public/img/"
else
    echo "⚠️  Original static/img directory not found. Please copy images manually to public/img/"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Update environment variables in .env.local"
echo "2. Ensure your backend API is running on http://localhost:5000"
echo "3. Copy any missing images to public/img/"
echo "4. Run 'npm run dev' or 'yarn dev' to start the development server"
echo ""
echo "📚 For more information, see README.md"
