/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f4f8',
          100: '#d9e6f2',
          200: '#b3cde0',
          300: '#8db4ce',
          400: '#6d8cc7',
          500: '#4a6fa5',
          600: '#3d5a8a',
          700: '#30456f',
          800: '#233054',
          900: '#161b39',
        },
        secondary: {
          50: '#f2f5f9',
          100: '#e1eaf4',
          200: '#c3d5e9',
          300: '#a5c0de',
          400: '#87abd3',
          500: '#6d8cc7',
          600: '#5a73a3',
          700: '#475a7f',
          800: '#34415b',
          900: '#212837',
        },
        accent: {
          50: '#fff8e1',
          100: '#ffecb3',
          200: '#ffe082',
          300: '#ffd54f',
          400: '#ffca28',
          500: '#ffa500',
          600: '#ff8f00',
          700: '#ff6f00',
          800: '#e65100',
          900: '#bf360c',
        },
        success: {
          50: '#f0fff4',
          100: '#c6f6d5',
          200: '#9ae6b4',
          300: '#68d391',
          400: '#48bb78',
          500: '#38a169',
          600: '#2f855a',
          700: '#276749',
          800: '#22543d',
          900: '#1a202c',
        },
        error: {
          50: '#fed7d7',
          100: '#feb2b2',
          200: '#fc8181',
          300: '#f56565',
          400: '#e53e3e',
          500: '#c53030',
          600: '#9b2c2c',
          700: '#742a2a',
          800: '#4a5568',
          900: '#2d3748',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#ed8936',
          600: '#d69e2e',
          700: '#b7791f',
          800: '#975a16',
          900: '#744210',
        },
        info: {
          50: '#ebf8ff',
          100: '#bee3f8',
          200: '#90cdf4',
          300: '#63b3ed',
          400: '#4299e1',
          500: '#3182ce',
          600: '#2b77cb',
          700: '#2c5aa0',
          800: '#2a4365',
          900: '#1a365d',
        },
        light: '#f5f7fa',
        dark: '#2d3748',
        border: '#e2e8f0',
      },
      fontFamily: {
        sans: ['Poppins', 'sans-serif'],
      },
      fontSize: {
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
        '5xl': '3rem',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '1rem',
        '2xl': '1.5rem',
      },
      boxShadow: {
        'soft': '0 2px 10px rgba(0,0,0,0.05)',
        'medium': '0 4px 20px rgba(0,0,0,0.1)',
        'strong': '0 8px 30px rgba(0,0,0,0.15)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
};
