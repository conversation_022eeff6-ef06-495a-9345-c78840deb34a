// User and Authentication Types
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  role: 'admin' | 'auditor' | 'annotator';
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface Session {
  user: User;
  token: string;
  expires: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  role?: 'annotator' | 'auditor';
}

// File and Directory Types
export interface FileItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modified?: string;
  extension?: string;
}

export interface DirectoryListing {
  current_path: string;
  parent_path?: string;
  items: FileItem[];
}

// Annotation Types
export interface AnnotationObject {
  id: string;
  type: 'rectangle' | 'polygon' | 'point';
  label: string;
  coordinates: number[];
  properties?: Record<string, any>;
}

export interface AnnotationData {
  image_path: string;
  image_width: number;
  image_height: number;
  objects: AnnotationObject[];
  metadata?: Record<string, any>;
}

export interface AnnotationTask {
  id: string;
  image_path: string;
  status: 'pending' | 'in_progress' | 'completed' | 'reviewed';
  assigned_to?: string;
  created_at: string;
  updated_at: string;
  annotation_data?: AnnotationData;
}

// Audit Types
export interface AuditTask {
  id: string;
  annotation_task_id: string;
  image_path: string;
  annotator: string;
  status: 'pending' | 'approved' | 'rejected';
  feedback?: string;
  created_at: string;
  reviewed_at?: string;
  reviewer?: string;
}

// Data Connector Types
export interface NASConnection {
  type: 'ftp' | 'sftp' | 'smb';
  url: string;
  username: string;
  password: string;
  connected: boolean;
}

export interface GoogleDriveConnection {
  client_id: string;
  client_secret: string;
  folder_id?: string;
  connected: boolean;
  authorized: boolean;
}

// Dashboard Types
export interface DashboardStats {
  total_tasks: number;
  completed_tasks: number;
  pending_tasks: number;
  total_users: number;
  active_users: number;
}

export interface UserStats {
  user_id: string;
  username: string;
  tasks_completed: number;
  tasks_pending: number;
  accuracy_rate?: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  pages: number;
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'file';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  validation?: any;
}

// Component Props Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  dismissible?: boolean;
  onDismiss?: () => void;
}

// Navigation Types
export interface NavItem {
  label: string;
  href: string;
  icon?: string;
  active?: boolean;
  children?: NavItem[];
}

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export type SortDirection = 'asc' | 'desc';

export interface SortConfig {
  field: string;
  direction: SortDirection;
}

export interface FilterConfig {
  field: string;
  value: any;
  operator?: 'eq' | 'ne' | 'gt' | 'lt' | 'gte' | 'lte' | 'contains';
}
