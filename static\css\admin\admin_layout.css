/* admin_layout.css */

/* Corporate Theme Variables */
:root {
    /* Primary Colors */
    --primary-blue: #0056b3;
    --primary-dark: #004494;
    --primary-light: #0066cc;
    
    /* Secondary Colors */
    --secondary-blue: #003366;
    --secondary-light: #e6f0ff;
    
    /* Neutral Colors */
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #e9ecef;
    --neutral-400: #dee2e6;
    --neutral-500: #adb5bd;
    --neutral-600: #6c757d;
    --neutral-700: #495057;
    --neutral-800: #343a40;
    
    /* System Colors */
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    
    /* Layout */
    --sidebar-width: 260px;
    --sidebar-collapsed: 70px;
    --header-height: 60px;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* Typography */
    --font-main: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif;
}

/* Base Styles */
body {
    display: flex;
    min-height: 100vh;
    background-color: var(--neutral-200);
    font-family: var(--font-main);
    color: var(--neutral-700);
    margin: 0;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: var(--primary-blue);
    color: var(--neutral-100);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    z-index: 1030;
    box-shadow: var(--shadow-md);
}

.sidebar-header {
    margin-bottom: 1.5rem;
    padding: 0.5rem;
}

.header-flex {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--neutral-100);
    text-decoration: none;
    letter-spacing: 1px;
}

.sidebar-toggle {
    border: none;
    background: none;
    color: var(--neutral-100);
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.sidebar-toggle i {
    font-size: 1.75rem;
}

/* Navigation Links */
.sidebar .nav-link {
    padding: 0.8rem 1rem;
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    border-radius: var(--radius-sm);
    margin-bottom: 0.25rem;
    position: relative;
}

.sidebar .nav-link i {
    font-size: 1.25rem;
    width: 35px;
    text-align: center;
}

.sidebar .nav-link span {
    margin-left: 0.5rem;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background-color: var(--primary-dark);
}

.sidebar .nav-link.active {
    background-color: var(--primary-dark);
    color: var(--neutral-100);
}

/* Collapsed State */
body.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed);
}

body.sidebar-collapsed .sidebar-header {
    padding: 0.5rem 0;
}

body.sidebar-collapsed .sidebar-brand span,
body.sidebar-collapsed .sidebar .nav-link span,
body.sidebar-collapsed .sidebar-footer span {
    display: none;
}

body.sidebar-collapsed .sidebar .nav-link {
    padding: 0.8rem 0;
    justify-content: center;
}

body.sidebar-collapsed .sidebar .nav-link i {
    margin: 0;
    width: auto;
}

body.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed);
    width: calc(100% - var(--sidebar-collapsed));
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    padding: 2rem;
    flex-grow: 1;
}

/* Cards */
.card {
    background: var(--neutral-100);
    border: none;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.card-header {
    background: var(--primary-blue);
    color: var(--neutral-100);
    border-bottom: none;
    padding: 1rem 1.25rem;
    font-weight: 600;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: var(--radius-sm);
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    border: none;
    box-shadow: var(--shadow-sm);
}

.btn-primary {
    background-color: var(--primary-blue);
    color: var(--neutral-100);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

/* Mobile Styles */
@media (max-width: 768px) {
    .sidebar {
        width: var(--sidebar-collapsed);
    }

    .sidebar-brand span,
    .sidebar .nav-link span {
        display: none;
    }

    .sidebar .nav-link {
        padding: 0.8rem 0;
        justify-content: center;
    }

    .sidebar .nav-link i {
        margin: 0;
        width: auto;
    }

    .main-content {
        margin-left: var(--sidebar-collapsed);
        width: calc(100% - var(--sidebar-collapsed));
    }
}

/* Footer */
.sidebar-footer {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .nav-link {
    opacity: 0.8;
}

.sidebar-footer .nav-link:hover {
    opacity: 1;
}

.flash-container {
    position: sticky;
    top: 10px;
    z-index: 1050;
    margin-bottom: 1rem;
}

/* General Card Styling */
.card:hover {
    box-shadow: var(--shadow-lg);
}

/* Ensure header text is easily readable */
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
    color: inherit;
    margin-bottom: 0;
}

/* Button Styling */
.btn i {
    margin-right: 0.5rem;
}

/* Secondary button */
.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: var(--text-white);
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Outline buttons */
.btn-outline-primary {
    color: var(--primary-blue);
    border-color: var(--primary-blue);
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: var(--primary-blue);
    color: var(--neutral-100);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Success, danger, info, warning buttons */
.btn-success {
    background-color: var(--success);
    border-color: var(--success);
    color: var(--text-white);
    font-weight: 500;
    color: var(--text-white);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background-color: var(--danger);
    border-color: var(--danger);
    color: var(--text-white);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background-color: var(--warning);
    border-color: var(--warning);
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-info {
    background-color: var(--info);
    border-color: var(--info);
    color: var(--text-white);
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Form controls */
.form-control {
    border-radius: var(--radius-sm);
    padding: 0.6rem 1rem;
    border: 1px solid var(--neutral-400);
    transition: var(--transition-smooth);
}

.form-control:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--neutral-700);
    background-color: rgba(0, 0, 0, 0.03);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Text colors */
.text-muted {
    color: var(--neutral-600) !important;
}

.text-primary {
    color: var(--primary-blue) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-info {
    color: var(--info) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

/* Admin Dropdown Styling */
.dropdown-menu {
    background-color: var(--primary-blue);
    border: none;
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
    min-width: 200px;
    margin-top: 0.5rem;
}

.dropdown-item {
    color: var(--neutral-100) !important;
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.dropdown-item i {
    font-size: 1rem;
    width: 20px;
    color: rgba(255, 255, 255, 0.8);
}

.dropdown-item:hover {
    background-color: var(--primary-dark);
    color: white !important;
}

.dropdown-divider {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Admin text color */
.admin-text {
    color: var(--neutral-100);
    font-weight: 500;
}

/* Dark overlay for dropdown */
.dropdown-menu {
    background: linear-gradient(to bottom, var(--primary-blue), var(--secondary-blue));
}

/* Tooltip styling for sidebar icons - Only show in collapsed state */
.sidebar .nav-link::before {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    margin-left: 10px;
    z-index: 1040;
    pointer-events: none;
}

/* Hide tooltips when sidebar is expanded */
.sidebar .nav-link:hover::before {
    opacity: 0;
    visibility: hidden;
}

/* Only show tooltips when sidebar is collapsed */
body.sidebar-collapsed .sidebar .nav-link:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Position tooltips properly in collapsed state */
body.sidebar-collapsed .sidebar .nav-link::before {
    left: 100%;
    margin-left: 10px;
}
 