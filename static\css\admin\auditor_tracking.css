/* Base styles */
:root {
    --primary-color: #2D4185;
    --primary-light: rgba(45, 65, 133, 0.1);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #0d6efd;
    --danger-color: #dc3545;
    --light-gray: #f8f9fa;
    --border-color: #e9ecef;
    --text-muted: #6c757d;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

.tracking-card {
    border-radius: 8px;
    margin-bottom: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.tracking-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem;
}

.progress {
    height: 0.8rem;
    border-radius: 0.4rem;
    overflow: hidden;
}

.stats-card {
    border-left: 4px solid var(--text-muted);
    transition: var(--transition);
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stats-card.manual {
    border-left-color: var(--success-color);
}

.stats-card.verification {
    border-left-color: var(--info-color);
}

.stats-card.total {
    border-left-color: var(--primary-color);
}

.auditor-card {
    border-radius: 8px;
    transition: var(--transition);
    padding: 1rem;
    background-color: white;
    margin-bottom: 1rem;
}

.auditor-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.mode-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 50rem;
}

.refresh-btn {
    font-size: 0.875rem;
    color: var(--primary-color);
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: var(--transition);
    text-decoration: none;
}

.refresh-btn:hover {
    background-color: var(--primary-light);
    text-decoration: none;
    color: var(--primary-color);
}

.no-auditors-message {
    padding: 3rem;
    text-align: center;
    color: var(--text-muted);
}

.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    background-color: var(--primary-light);
    color: var(--primary-color);
}

/* Page layout */
.page-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0rem 0rem;
}

.unified-container {
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin: 0 auto;
}

.page-title {
    text-align: center;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
}

.page-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
}

.content-row {
    display: flex;
    margin-top: 2rem;
    gap: 2rem;
    background-color: var(--light-gray);
    border-radius: 8px;
    padding: 1.5rem;
}

.status-column {
    width: 30%;
    min-width: 280px;
}

.main-column {
    flex: 1;
}

.status-section-header {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-light);
}

.dataset-selection {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.dataset-selection label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-color);
}

.dataset-selection select {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    font-size: 16px;
    width: 100%;
    background-color: white;
    transition: var(--transition);
}

.dataset-selection select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem var(--primary-light);
    outline: none;
}

.stats-container {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.batch-heading {
    color: var(--primary-color);
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.75rem;
    margin-bottom: 1.5rem;
}

.batch-heading:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 2px;
    background-color: var(--primary-color);
}

.stats-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.completion-count {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-color);
}

.status-badge {
    display: inline-block;
    border-radius: 50rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: white;
}

.status-badge.complete {
    background-color: var(--success-color);
}

.status-badge.pending {
    background-color: var(--warning-color);
    color: #212529;
}

.progress-bar {
    background-color: var(--primary-color);
    transition: width 0.5s ease;
}

.info-row {
    display: flex;
    align-items: center;
    margin: 15px 0;
    background-color: var(--light-gray);
    padding: 1rem;
    border-radius: 8px;
}

.info-icon {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 15px;
    flex-shrink: 0;
}

.info-icon.success { background-color: rgba(40, 167, 69, 0.1); color: var(--success-color); }
.info-icon.warning { background-color: rgba(255, 193, 7, 0.1); color: var(--warning-color); }
.info-icon.info { background-color: rgba(0, 123, 255, 0.1); color: var(--info-color); }

.output-path {
    background-color: var(--light-gray);
    padding: 12px 15px;
    border-radius: 8px;
    font-family: monospace;
    margin: 20px 0;
    font-size: 14px;
    color: #555;
    border: 1px dashed #ddd;
}

.actions-row {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.storage-action-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

#storage-destination {
    min-width: 180px;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    background-color: white;
    transition: var(--transition);
}

#storage-destination:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem var(--primary-light);
    outline: none;
}

.merge-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
}

.merge-btn:hover:not(:disabled) {
    background-color: #1e2c5e;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.merge-btn:disabled {
    background-color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.7;
}

#merge-result {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1050;
    min-width: 350px;
    max-width: 500px;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

/* Dataset Status Styles */
.status-section {
    margin-bottom: 1.5rem;
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: var(--box-shadow);
}

.status-heading {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-light);
    display: flex;
    align-items: center;
}

.dataset-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dataset-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eee;
    font-size: 0.95rem;
    background-color: var(--light-gray);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.dataset-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.dataset-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.dataset-name {
    display: block;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    padding: 0.25rem 0;
}

.dataset-progress {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

.merged-badge, .pending-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.merged-badge {
    background-color: var(--success-color);
}

.pending-badge {
    background-color: var(--warning-color);
}

@media (max-width: 992px) {
    .content-row {
        flex-direction: column;
    }
    
    .status-column {
        width: 100%;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 768px) {
    .page-container {
        padding: 1rem;
    }
    
    .unified-container {
        padding: 1rem;
    }
    
    .content-row {
        padding: 1rem;
    }
    
    .completion-count {
        font-size: 24px;
    }
    
    .actions-row {
        flex-direction: column;
    }
    
    .storage-action-container {
        flex-direction: column;
        width: 100%;
    }
    
    #storage-destination {
        width: 100%;
    }
    
    .merge-btn {
        width: 100%;
        justify-content: center;
    }
} 