/* Browser page styles */
:root {
    --primary-color: #0056b3;
    --secondary-color: #003366;
    --accent-color: #4a90e2;
    --accent-light: rgba(74, 144, 226, 0.1);
    --text-light: #E5E5E5;
    --text-white: #FFFFFF;
    --text-color: #495057;
    --bg-color: #ffffff;
    --bg-light: #f8f9fa;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #dee2e6;
    --input-focus: var(--accent-color);
    --error-color: #dc3545;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.25s ease;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #0078d4);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #0056b3);
    --card-radius: 8px;
    --button-radius: 4px;
    --sidebar-width: 260px;
    --font-main: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

body {
    background-color: var(--bg-light);
    color: var(--text-color);
    font-family: var(--font-main);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 1px;
    color: var(--primary-color);
}

/* Browser container */
.browser-container {
    padding: 2rem;
    background-color: var(--bg-light);
    min-height: calc(100vh - 60px);
}

/* Folder navigation */
.folder-nav {
    background-color: var(--card-bg);
    padding: 1rem;
    border-radius: var(--card-radius);
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.folder-actions .btn {
    transition: var(--transition-smooth);
}

.folder-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.folder-dropdown .dropdown-menu {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: var(--shadow-md);
    padding: 0.5rem;
}

.folder-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    border-radius: var(--button-radius);
    transition: var(--transition-smooth);
}

.folder-dropdown .dropdown-item:hover {
    background-color: var(--accent-light);
    color: var(--primary-color);
}

.folder-dropdown .dropdown-item i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Breadcrumb navigation */
.folder-path {
    background-color: var(--card-bg);
    padding: 1rem;
    border-radius: var(--card-radius);
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-smooth);
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
}

.breadcrumb-item.active {
    color: var(--text-color);
}

/* Image grid */
.image-grid-container {
    background-color: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--card-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.image-card {
    background-color: var(--bg-light);
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-smooth);
    cursor: pointer;
    border: 1px solid var(--input-border);
    user-select: none;
}

.image-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.image-card:active {
    transform: translateY(-2px);
}

.image-preview {
    position: relative;
    aspect-ratio: 1;
    overflow: hidden;
    background-color: var(--bg-light);
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: var(--transition-smooth);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.image-card:hover .image-overlay {
    opacity: 1;
}

.image-overlay .btn {
    transform: translateY(20px);
    transition: var(--transition-smooth);
    pointer-events: none;
}

.image-card:hover .image-overlay .btn {
    transform: translateY(0);
}

.image-info {
    padding: 0.75rem;
    background-color: var(--card-bg);
    border-top: 1px solid var(--input-border);
}

.image-name {
    display: block;
    font-size: 0.875rem;
    color: var(--text-color);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* No images message */
.no-images {
    padding: 2rem;
    text-align: center;
}

.no-images .alert {
    display: inline-flex;
    align-items: center;
    padding: 1rem 1.5rem;
    border-radius: var(--card-radius);
    background-color: var(--accent-light);
    color: var(--primary-color);
    border: none;
}

/* Pagination */
.pagination-container {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--input-border);
}

.pagination .page-link {
    border: none;
    margin: 0 0.25rem;
    padding: 0.5rem 0.75rem;
    border-radius: var(--button-radius);
    color: var(--text-color);
    transition: var(--transition-smooth);
}

.pagination .page-link:hover {
    background-color: var(--accent-light);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    color: white;
}

.pagination .page-item.disabled .page-link {
    background-color: transparent;
    color: var(--text-color);
    opacity: 0.5;
}

/* Image Modal */
.modal-content {
    background-color: rgba(0, 0, 0, 0.95);
    border: none;
    border-radius: var(--card-radius);
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
}

.modal-title {
    color: white;
    font-size: 1rem;
}

.btn-close {
    filter: invert(1);
    opacity: 0.7;
    transition: var(--transition-smooth);
}

.btn-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 0;
    position: relative;
}

.image-viewer {
    position: relative;
    width: 100%;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-container {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.image-container img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
    transition: transform 0.2s ease;
}

.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-smooth);
    z-index: 1;
    cursor: pointer;
}

.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
}

.nav-btn:active {
    transform: translateY(-50%) scale(0.95);
}

.prev-btn {
    left: 1rem;
}

.next-btn {
    right: 1rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .browser-container {
        padding: 1rem;
    }

    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .nav-btn {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 576px) {
    .browser-container {
        padding: 0.5rem;
    }

    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .folder-nav,
    .folder-path,
    .image-grid-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .nav-btn {
        width: 32px;
        height: 32px;
    }
}

/* Add folder title styles */
.folder-title {
    background-color: var(--card-bg);
    padding: 1rem;
    border-radius: var(--card-radius);
    box-shadow: var(--shadow-sm);
}

.folder-title h5 {
    color: var(--primary-color);
    font-weight: 500;
}

.folder-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Update back button styles */
.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.back-btn:hover {
    transform: translateX(-2px);
}

.back-btn:active {
    transform: translateX(-1px);
}