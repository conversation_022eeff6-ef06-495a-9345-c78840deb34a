/* Import variables from admin_layout.css */
:root {
    --primary-color: #0056b3;      /* Corporate blue */
    --secondary-color: #003366;    /* Darker blue */
    --accent-color: #4a90e2;       /* Accent blue */
    --accent-light: rgba(74, 144, 226, 0.1);
    --text-light: #E5E5E5;
    --text-white: #FFFFFF;
    --text-color: #495057;
    --bg-color: #ffffff;
    --bg-light: #f8f9fa;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #dee2e6;
    --input-focus: var(--accent-color);
    --error-color: #dc3545;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.25s ease;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #0078d4);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #0056b3);
    --card-radius: 8px;
    --button-radius: 4px;
    --sidebar-width: 260px;
    --font-main: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

body {
    background-color: var(--bg-light);
    color: var(--text-color);
    font-family: var(--font-main);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 1px;
    color: var(--primary-color);
}

/* Dashboard cards */
.card {
    transition: var(--transition-smooth);
    margin-bottom: 20px;
    border-radius: var(--card-radius);
    overflow: hidden;
    border: none;
    box-shadow: var(--shadow-sm);
    background-color: var(--card-bg);
}

/* .card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
} */

.card-header {
    background: var(--corporate-gradient);
    color: var(--primary-color);
    font-weight: 600;
    padding: 15px 20px;
    border-bottom: none;
}

.card-body {
    padding: 20px;
    background-color: var(--card-bg);
}

/* Stats cards */
.stats-card {
    background-color: var(--card-bg);
    border-radius: var(--card-radius);
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-smooth);
}

.stats-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stats-label {
    color: var(--text-color);
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Buttons */
.btn-primary {
    background: var(--corporate-gradient);
    border: none;
    transition: var(--transition-smooth);
    border-radius: var(--button-radius);
    color: var(--text-white);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: var(--corporate-gradient-hover);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--button-radius);
    transition: var(--transition-smooth);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}
/* Progress bars */
.progress {
    height: 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    background-color: var(--bg-light);
    overflow: hidden;
}

.progress-bar {
    background: var(--corporate-gradient);
}

/* Tables */
.table-responsive {
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table {
    margin-bottom: 0;
}

.table th {
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 600;
    color: var(--text-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Instructions form styles */
.instructions-form {
    display: none;
    background-color: var(--bg-light);
    border: 1px solid var(--input-border);
    border-radius: var(--card-radius);
    padding: 20px;
    margin-top: 15px;
    box-shadow: var(--shadow-sm);
}

.instructions-form.show {
    display: block;
}

/* Tabs */
.nav-tabs .nav-link {
    color: var(--text-color);
    border-radius: 0;
    transition: var(--transition-smooth);
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    font-weight: 500;
    border-color: transparent;
    border-bottom: 2px solid var(--primary-color);
}

/* Stat cards with icons */
.stat-card {
    border-radius: var(--card-radius);
    padding: 20px;
    transition: var(--transition-smooth);
    background-color: var(--card-bg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--card-radius);
    background: var(--corporate-gradient);
    color: var(--text-white);
    margin-right: 15px;
}

.stat-icon i {
    font-size: 1.25rem;
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0;
}

/* Animation for expanding sections */
.transition-icon {
    transition: transform 0.3s ease;
}

[aria-expanded="true"] .transition-icon {
    transform: rotate(180deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        padding: 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
}

/* Auditor Settings Styles */
.settings-container {
    background-color: var(--bg-light);
    border-radius: var(--card-radius);
    padding: 1.5rem;
}

.folder-input-container {
    background: var(--card-bg);
    border-radius: var(--button-radius);
    padding: 1rem;
}

.folder-input-container .input-group {
    box-shadow: var(--shadow-sm);
}

.folder-input-container .input-group-text {
    border-right: none;
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.folder-input-container .form-control {
    border-left: none;
    border-right: none;
    background-color: var(--bg-light);
}

.folder-input-container .form-control:focus {
    box-shadow: none;
    border-color: var(--input-border);
}

#saveAuditorFolderBtn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#saveAuditorFolderBtn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

#saveAuditorFolderBtn:disabled {
    background: var(--bg-light);
    border-color: var(--input-border);
    color: var(--text-color);
    opacity: 0.7;
}

/* Toast Styles */
.toast-container {
    z-index: 1060;
}

.toast {
    opacity: 0.95;
    backdrop-filter: blur(4px);
    box-shadow: var(--shadow-lg);
}

.toast .toast-body {
    font-weight: 500;
}

/* Status Badge Styles */
.badge.bg-primary-soft {
    background-color: transparent;
    color: white;
    font-weight: 500;
    padding: 0.5em 1em;
}

.badge.bg-primary-soft i {
    font-size: 0.5rem;
    vertical-align: middle;
}

/* Update the dashboard title styles */
.page-header {
    text-align: center;
    padding: 1rem 0;
    margin-bottom: 1rem;
    background: transparent;
}

.dashboard-title {
    color: #1a365d;
    font-size: 2.5rem;
    font-weight: 800;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
    margin: 0;
    padding: 0 1rem;
    text-transform: uppercase;
}

.dashboard-title::after {
    content: "";
    display: block;
    width: 80px;
    height: 5px;
    background: var(--corporate-gradient);
    margin: 0.5rem auto 0;
    border-radius: 3px;
}

/* Update responsive styles */
@media (max-width: 768px) {
    .dashboard-title {
        font-size: 2rem;
        font-weight: 800;
        padding: 0 0.75rem;
    }
    
    .dashboard-title::after {
        width: 60px;
        height: 4px;
    }
}
.card-header h5{
    color: var(--text-white);
}
/* Add spacing for the Data Connector section */
.card.mb-4:first-of-type {
    margin-top: 2rem;  /* Adds space after the title */
}

/* If you want more space, you can also add bottom margin to the title container */
.container-fluid:first-of-type {
    margin-bottom: 1.5rem;
}

/* OCR Directory Button styles */
#btnGoToOcrDirectory {
    transition: var(--transition-smooth);
    font-weight: 500;
    border-width: 2px;
    padding: 0.75rem 1rem;
}

#btnGoToOcrDirectory:hover {
    background-color: var(--success-color);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}