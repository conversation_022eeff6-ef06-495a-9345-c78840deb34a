/* Data Sources - Corporate Style */

:root {
  --corporate-primary: #0056b3;
  --corporate-secondary: #003366;
  --corporate-accent: #0072C6;
  --corporate-success: #28a745;
  --corporate-warning: #ffc107;
  --corporate-danger: #dc3545;
  --corporate-light: #f8f9fa;
  --corporate-dark: #343a40;
  --corporate-gray: #6c757d;
  --corporate-light-gray: #dee2e6;
  --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
  --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --transition: all 0.25s ease;
  --card-radius: 8px;
  --button-radius: 4px;
}

body {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  color: #495057;
  background-color: #f5f7fa;
  line-height: 1.6;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Visual Elements - Static versions */
.noise-texture {
  display: none;
}

/* Hide the background grid */
.background-grid {
  display: none;
}

.floating-shapes .shape {
  display: none;
}

/* Content wrapper */
.content-wrapper {
  position: relative;
  min-height: calc(100vh - 50px);
  z-index: 1;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
}

.container-fluid {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* Page header */
.page-header {
  text-align: center;
  margin-bottom: 2.5rem;
  padding: 2.5rem 0;
  background: transparent;
  border-bottom: 1px solid rgba(0,0,0,0.07);
  border-radius: 8px;
  max-width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.page-title {
  color: #003366;
  font-weight: 800;
  margin-bottom: 0.75rem;
  font-size: 3.00rem;
  letter-spacing: -0.02em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: "";
  display: block;
  width: 80px;
  height: 4px;
  background: var(--corporate-gradient);
  margin: 0.5rem auto 0;
  border-radius: 2px;
}

.page-subtitle {
  color: #6c757d;
  font-weight: 400;
  font-size: 1.1rem;
  max-width: 700px;
  margin: 1rem auto 0;
}

/* Flash Message */
.flash-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.flash-content {
  background-color: white;
  border-radius: var(--card-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.flash-header {
  background: var(--corporate-gradient);
  color: white;
  padding: 1.25rem 1.5rem;
  display: flex;
  align-items: center;
  position: relative;
}

.flash-header i {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.flash-header h3 {
  margin: 0;
  font-weight: 600;
  font-size: 1.25rem;
}

.close-flash {
  position: absolute;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.close-flash:hover {
  background: rgba(255, 255, 255, 0.3);
}

.flash-body {
  padding: 1.5rem;
}

.process-step {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--button-radius);
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-step .step-icon {
  background: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  color: var(--corporate-primary);
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Sources Grid */
.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.25rem;
  margin-bottom: 2rem;
}

/* Source Card */
.source-card {
  background-color: white;
  border-radius: var(--card-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: none;
}

.source-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

/* Update source card layout for icons and titles to appear side by side */
.source-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.source-card .card-header .d-flex {
  display: flex;
  align-items: center;
  gap: 12px;
}

.source-card .source-icon {
  font-size: 1.5rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.2s ease;
}

/* Telegram Icon */
.source-card .fa-telegram {
  color: #0088cc;  /* Telegram brand blue */
  background: rgba(0, 136, 204, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* Twitter Icon */
.source-card .fa-twitter {
  color: #1DA1F2;  /* Twitter brand blue */
  background: rgba(29, 161, 242, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* Database Icon */
.source-card .fa-database {
  color: #6366f1;  /* Indigo color */
  background: rgba(99, 102, 241, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* YouTube Icon */
.source-card .fa-youtube {
  color: #FF0000;  /* YouTube red */
  background: rgba(255, 0, 0, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* LinkedIn Icon */
.source-card .fa-linkedin {
  color: #0077B5;  /* LinkedIn blue */
  background: rgba(0, 119, 181, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* NAS (Server) Icon */
.source-card .fa-server {
  color: #4CAF50;  /* Green */
  background: rgba(76, 175, 80, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* Google Drive Icon */
.source-card .fa-google-drive {
  color: #1DA462;  /* Google Drive green */
  background: rgba(29, 164, 98, 0.1);
  padding: 8px;
  border-radius: 8px;
}

/* Instagram Icon */
.source-card .fa-instagram {
  color: #E4405F;  /* Instagram pink/red */
  background: rgba(228, 64, 95, 0.1);
  padding: 8px;
  border-radius: 8px;
}

.source-card:hover .source-icon {
  background-color: transparent;
  transform: none;
}

.source-card .card-body {
  padding: 1rem 1.25rem;
  flex: 1;
}

.source-card .card-header h3 {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 1.25rem;
  letter-spacing: -0.01em;
}

.source-card .card-header button {
  font-size: 1.2rem;
  color: #333;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.source-card h3 {
  margin: 0;
  flex-grow: 1;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.source-card p {
  color: var(--corporate-gray);
  margin-bottom: 1rem;
  line-height: 1.5;
  font-size: 0.95rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 1rem 0;
}

.feature-list li {
  display: flex;
  align-items: center;
  margin-bottom: 0.35rem;
  color: #495057;
  font-size: 0.9rem;
}

.feature-list li i {
  color: var(--corporate-success);
  margin-right: 0.75rem;
}

.feature-list li i.fa-check {
  color: #10B981;  /* Success green */
  background: rgba(16, 185, 129, 0.1);
  padding: 4px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.source-card .card-footer {
  padding: 1rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-badge {
  font-size: 0.875rem;
  padding: 0.4rem 0.8rem;
  border-radius: 50px;
  font-weight: 500;
}

.status-badge.connected {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10B981;
}

.status-badge.coming-soon {
  background-color: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.btn-source {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border-radius: var(--button-radius);
  background: var(--corporate-gradient);
  color: white;
  font-weight: 500;
  text-decoration: none;
  transition: var(--transition);
}

.btn-source:hover {
  background: var(--corporate-gradient-hover);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.source-card .btn-info {
  color: #64748b;  /* Subtle gray */
  transition: color 0.2s ease;
}

.source-card .btn-info:hover {
  color: #0056b3;  /* Corporate blue on hover */
}

/* Compliance Section */
.compliance-section {
  margin-top: 3rem;
}

.compliance-statement {
  background: var(--corporate-gradient);
  border-radius: var(--card-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  color: white;
}

.compliance-icon {
  background: rgba(255, 255, 255, 0.2);
  width: 50px;
  height: 50px;
  flex-shrink: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.compliance-text h4 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-weight: 600;
  font-size: 1.25rem;
}

.compliance-text p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.6;
}

.terms-link {
  color: white;
  text-decoration: underline;
  font-weight: 500;
  transition: var(--transition);
}

.terms-link:hover {
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 992px) {
  .sources-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  
  .content-wrapper {
    padding: 1.5rem;
  }
  
  .page-header {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .sources-grid {
    grid-template-columns: 1fr;
  }
  
  .flash-header {
    padding: 1rem;
  }
  
  .flash-body {
    padding: 1rem;
  }
  
  .compliance-statement {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem;
  }
  
  .compliance-icon {
    margin-bottom: 1rem;
  }
  
  .page-header {
    padding: 1.25rem;
  }
  
  .page-title {
    font-size: 3.00rem;
  }
  
  .process-step {
    align-items: flex-start;
  }
  
  .process-step .step-icon {
    margin-top: 0.25rem;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 1rem;
  }
  
  .source-card .card-header {
    padding: 1rem;
    flex-wrap: wrap;
  }
  
  .source-card .card-header .d-flex {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .source-card .source-icon {
    width: 38px;
    height: 38px;
    font-size: 1.5rem;
  }
  
  .source-card .card-body {
    padding: 1rem;
  }
  
  .source-card .card-footer {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .btn-source {
    width: 100%;
    justify-content: center;
  }
}

/* Override any existing background styles to ensure white backgrounds */
.source-card, 
.source-card .card-header, 
.source-card .source-icon,
.source-card .card-header .d-flex {
  background: white !important;
  background-color: white !important;
  background-image: none !important;
}

/* Remove hover effects */
.source-card:hover,
.source-card:hover .source-icon {
  background-color: white !important;
  transform: none !important;
  box-shadow: var(--box-shadow) !important;
}
