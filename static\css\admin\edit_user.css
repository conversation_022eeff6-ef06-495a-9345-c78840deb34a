/* Edit User Page - Corporate Style */

:root {
    --primary-blue: #4361ee;
    --secondary-blue: #4895ef;
    --primary-dark: #3a56d4;
    --secondary-dark: #3f80d6;
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-300: #e9ecef;
    --neutral-400: #dee2e6;
    --neutral-500: #adb5bd;
    --neutral-600: #6c757d;
    --neutral-700: #495057;
    --neutral-800: #343a40;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 12px 20px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius-lg: 12px;
    --border-radius-md: 10px;
    --border-radius-sm: 8px;
    --border-radius-btn: 50px;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    font-weight: 600;
    padding: 18px 20px;
    border-bottom: none;
}

.card-body {
    padding: 25px;
}

/* Info Cards */
.info-card {
    background-color: var(--neutral-200);
    border-radius: var(--border-radius-md);
    padding: 20px;
    height: 100%;
    border-left: 5px solid var(--primary-blue);
    transition: var(--transition);
}

.info-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

.stats-card {
    background-color: var(--neutral-200);
    border-radius: var(--border-radius-md);
    padding: 20px;
    height: 100%;
    border-left: 5px solid var(--secondary-blue);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

/* Form Elements */
.form-control, .form-select {
    border-radius: var(--border-radius-sm);
    padding: 12px 15px;
    border: 1px solid var(--neutral-400);
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--neutral-700);
}

/* Buttons */
.btn {
    border-radius: var(--border-radius-btn);
    padding: 10px 20px;
    font-weight: 500;
    transition: var(--transition);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-secondary {
    background: var(--neutral-600);
    border: none;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

/* Badges */
.badge {
    padding: 8px 12px;
    border-radius: var(--border-radius-btn);
    font-weight: 500;
    font-size: 0.85rem;
}

/* Section Titles */
.section-title {
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--neutral-300);
}

/* Form Text */
.form-text {
    color: var(--neutral-600);
    font-size: 0.85rem;
    margin-top: 5px;
}

/* Annotator Options */
.annotator-options {
    background-color: var(--neutral-200);
    border-radius: var(--border-radius-md);
    padding: 15px;
    margin-top: 5px;
    border-left: 3px solid var(--primary-blue);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Animation Classes */
.fade-in-animation {
    animation: fadeIn 0.3s ease-in-out;
}

.btn-hover-effect {
    transform: translateY(-2px);
}

/* Input Groups */
.input-group {
    transition: var(--transition);
}

.input-group-text {
    background-color: var(--neutral-200);
    border-color: var(--neutral-400);
    color: var(--primary-blue);
}

.input-group-focus {
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    border-color: var(--primary-blue);
}
