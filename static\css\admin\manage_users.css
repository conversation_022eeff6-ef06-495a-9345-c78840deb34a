/* Corporate Theme Variables - Updated with solid colors */
:root {
    --primary-blue: #0052CC;
    --primary-dark: #0747A6;
    --primary-light: #4C9AFF;
    --secondary-blue: #0065FF;
    --danger-color: #DE350B;
    --success-color: #00875A;
    --warning-color: #FF8B00;
    --info-color: #0065FF;
    
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius: 12px;
    --btn-radius: 8px;
}

/* Page Header Enhancement */
.page-header {
    background: var(--primary-blue);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
}

.page-header h2 {
    color: white;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
    background: transparent;
    margin: 0;
    padding: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

/* Role Cards Styling - Updated */
.role-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.role-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.75rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: var(--card-shadow);
    position: relative;
    overflow: hidden;
}

.role-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-blue);
    opacity: 0;
    transition: var(--transition);
}

.role-card:hover::before {
    opacity: 1;
}

.role-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

.role-card.admin {
    background: rgba(222, 53, 11, 0.08);
    border-left: 4px solid var(--danger-color);
}

.role-card.annotator {
    background: rgba(0, 82, 204, 0.08);
    border-left: 4px solid var(--primary-blue);
}

.role-card.auditor {
    background: rgba(255, 193, 7, 0.08);
    border-left: 4px solid #FFA000;
}

.role-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.25rem;
    font-size: 1.75rem;
    background: var(--primary-blue);
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.role-card.admin .role-icon {
    background: var(--danger-color);
}

.role-card.annotator .role-icon {
    background: var(--primary-blue);
}

.role-card.auditor .role-icon {
    background: #FFA000;
}

.role-card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-weight: 600;
}

.role-count {
    font-size: 2.75rem;
    font-weight: 700;
    margin: 1rem 0;
    color: var(--primary-blue);
    /* -webkit-background-clip: initial; */
    -webkit-text-fill-color: initial;
    background: none;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.role-card.admin .role-count {
    color: var(--danger-color);
}

.role-card.auditor .role-count {
    color: #FFA000;
}

.role-card p.text-muted {
    color: #6b7280 !important;
    font-size: 0.9rem;
}

/* Users Table Enhancement */
.users-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    border: none;
}

.users-header {
    background: var(--primary-blue);
    color: white;
    padding: 1.25rem 1.5rem;
}

.users-header h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.users-header .badge {
    font-size: 0.875rem;
    padding: 0.5em 1em;
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Search Container Enhancement */
.search-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.input-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: var(--btn-radius);
    overflow: hidden;
}

.input-group-text {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
}

.form-control {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.6rem 1rem;
    transition: all 0.3s ease;
    color: #374151;
    font-weight: 500;
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-control::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Filter buttons - Updated */
.btn-group-sm>.btn, .btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-group {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-radius: 0.375rem;
    overflow: hidden;
}

.btn-group .btn {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn.active {
    background: var(--primary-blue);
    color: white;
    font-weight: 500;
    border-color: var(--primary-blue);
}

/* Table Styles */
.table-container {
    position: relative;
    overflow: auto;
    max-height: calc(100vh - 400px);
}

.table {
    margin-bottom: 0;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    position: sticky;
    top: 0;
    background: #f8f9fa;
    padding: 1rem;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: opacity 0.2s ease;
    border-bottom: 1px solid #f3f4f6;
}

.table tbody tr:hover {
    background-color: rgba(4, 47, 175, 0.02);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    color: #4b5563;
    font-weight: 400;
}

.table .small.text-muted {
    color: #6b7280 !important;
    font-weight: 500;
}

.table .user-username {
    font-weight: 600;
    color: #1f2937;
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

/* Status Badges */
.badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
}

.badge.rounded-pill {
    padding: 0.5rem 1rem;
}

/* Primary badge */
.badge.bg-primary, .badge.bg-primary-soft {
    background: rgba(4, 47, 175, 0.1) !important;
    color: #042faf !important;
}

/* Success badge */
.badge.bg-success, .badge.bg-success-soft {
    background: rgba(40, 167, 69, 0.1) !important;
    color: #28a745 !important;
}

/* Danger badge */
.badge.bg-danger, .badge.bg-danger-soft {
    background: rgba(220, 53, 69, 0.1) !important;
    color: #dc3545 !important;
}

/* Warning badge */
.badge.bg-warning, .badge.bg-warning-soft {
    background: rgba(255, 193, 7, 0.1) !important;
    color: #ffc107 !important;
}

/* Info badge */
.badge.bg-info, .badge.bg-info-soft {
    background: rgba(67, 97, 238, 0.1) !important;
    color: #4361ee !important;
}

/* Secondary badge */
.badge.bg-secondary, .badge.bg-secondary-soft {
    background: rgba(107, 114, 128, 0.1) !important;
    color: #4b5563 !important;
}

/* Light badge */
.badge.bg-light {
    background: rgba(249, 250, 251, 0.9) !important;
    color: #1f2937 !important;
    border: 1px solid rgba(209, 213, 219, 0.5);
}

/* Last login styling */
.user-last-login .badge {
    font-weight: 500;
    font-size: 0.8rem;
    padding: 0.4rem 0.7rem;
}

/* Action Buttons - Updated */
.action-buttons .btn {
    padding: 0.5rem;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    color: #64748b;
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-edit {
    background: #f8fafc;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-edit:hover {
    background: var(--primary-blue);
    color: white;
}

.btn-suspend {
    background: #f8fafc;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-suspend:hover {
    background: var(--danger-color);
    color: white;
}

.btn-activate {
    background: #f8fafc;
    color: var(--success-color);
    border-color: var(--success-color);
}

.btn-activate:hover {
    background: var(--success-color);
    color: white;
}

/* Primary button */
.btn-primary, .btn-outline-primary {
    border-color: var(--primary-blue);
    color: white;
}

.btn-primary:hover, .btn-outline-primary:hover {
    background: var(--primary-dark);
    color: white;
    border-color: var(--primary-blue);
}

/* Secondary button */
.btn-secondary, .btn-outline-secondary {
    border-color: #6b7280;
    color: #4b5563;
}

.btn-secondary:hover, .btn-outline-secondary:hover {
    background: #6b7280;
    color: white;
    border-color: #6b7280;
}

/* Success button */
.btn-success, .btn-outline-success {
    border-color: #059669;
    color: #059669;
}

.btn-success:hover, .btn-outline-success:hover {
    background: #059669;
    color: white;
    border-color: #059669;
}

/* Danger button */
.btn-danger, .btn-outline-danger {
    border-color: #dc2626;
    color: #dc2626;
}

.btn-danger:hover, .btn-outline-danger:hover {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
}

/* Warning button */
.btn-warning, .btn-outline-warning {
    border-color: #d97706;
    color: #d97706;
}

.btn-warning:hover, .btn-outline-warning:hover {
    background: #d97706;
    color: white;
    border-color: #d97706;
}

/* Modal Enhancement */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    background: var(--primary-blue);
    color: white;
    border: none;
    padding: 1.5rem;
}

.modal-header .btn-close {
    color: white;
    opacity: 0.8;
}

.modal-header .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem 2rem;
}

.modal-footer {
    padding: 1.25rem 1.5rem 1.5rem;
    border-top: none;
    justify-content: space-between;
}

.modal-footer .btn {
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    border-radius: 8px;
    color: white;
    border: none;
}

.modal-footer .btn-secondary {
    background: #64748b;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background: #4b5563;
}

.modal-footer .btn-danger {
    background: var(--danger-color);
}

.modal-footer .btn-danger:hover {
    background: #C52F0D;
}

.suspension-confirm-btn {
    background: var(--danger-color);
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.suspension-confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 18px rgba(220, 53, 69, 0.5);
}

.suspension-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border-radius: 10px;
}

/* Loading State Enhancement */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
    color: #042faf;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .role-cards {
        grid-template-columns: 1fr;
    }

    .role-card {
        padding: 1.25rem;
    }
    
    .table td, .table th {
        padding: 0.75rem;
    }

    .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .action-buttons .btn {
        flex: 1;
        min-width: 120px;
    }

    .users-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .users-header .d-flex.gap-2 {
        flex-direction: row;
        justify-content: flex-start;
    }
    
    .users-header .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8125rem;
    }
}

/* Add active state styles for role cards */
.role-card.active {
    border-width: 2px;
    transform: translateY(-2px);
}
.role-card.admin.active {
    border-color: #dc2626;
}
.role-card.annotator.active {
    border-color: #4361ee;
}
.role-card.auditor.active {
    border-color: #FFA000;
}

/* Header Action Buttons - Updated */
.page-header .btn {
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 8px;
    color: white;
    border: none;
}

/* Flush DB Button - Updated */
.btn-flush-db {
    background: var(--danger-color) !important;
    color: white !important;
}

.btn-flush-db:hover {
    background: #C52F0D !important;
}



/* Add New User Button - Updated */
.btn-add-user {
    background: var(--success-color) !important;
    color: white !important;
}

.btn-add-user:hover {
    background: #06724c !important;
}

/* Disabled state - Updated */
.btn:disabled {
    background: #e2e8f0 !important;
    color: #94a3b8 !important;
    border-color: #e2e8f0 !important;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Button group spacing */
.d-flex.gap-2 .btn {
    margin-left: 0.5rem;
}

/* Users Table Header Styling */
.users-header {
    background: var(--primary-blue);
    color: white;
    padding: 1.25rem 1.5rem;
}

.users-header h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.users-header .badge {
    font-size: 0.875rem;
    padding: 0.5em 1em;
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Header Action Buttons - Updated */
.users-header .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.users-header .btn-flush-db {
    background: rgba(222, 53, 11, 0.2);
}

.users-header .btn-flush-db:hover {
    background: rgba(222, 53, 11, 0.3);
}

.users-header .btn-init-db {
    background: rgba(0, 82, 204, 0.2);
}

.users-header .btn-init-db:hover {
    background: rgba(0, 82, 204, 0.3);
}

.users-header .btn-add-user {
    background: rgba(0, 135, 90, 0.2);
}

.users-header .btn-add-user:hover {
    background: rgba(0, 135, 90, 0.3);
}

/* Button icons */
.users-header .btn i {
    font-size: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .users-header .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .users-header .d-flex.gap-2 {
        flex-direction: row;
        justify-content: flex-start;
    }
    
    .users-header .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8125rem;
    }
}

/* Clean Header Styling */
.clean-header {
    padding: 2rem 0;
    background: transparent;
    margin-bottom: 3rem;
}

.clean-header .main-title {
    color: #002B5B;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.clean-header .main-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-blue);
    border-radius: 2px;
}

.clean-header .subtitle {
    color: #6B7280;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 1.5rem auto 0;
    line-height: 1.6;
}

/* Update spacing for content below header */
.role-cards {
    margin-top: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .clean-header {
        padding: 1.5rem 0;
    }

    .clean-header .main-title {
        font-size: 2rem;
    }

    .clean-header .subtitle {
        font-size: 1rem;
        padding: 0 1rem;
    }
}

/* Color emphasis for subtitle */
.clean-header .subtitle span {
    color: #042faf;
    font-weight: 500;
}

/* Modern Dashboard Header */
.dashboard-header {
    background: #fff;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1f36;
    margin-bottom: 0.5rem;
}

.header-title p {
    color: #697386;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Stats Overview Section */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.admin { background: var(--danger-color); }
.stat-icon.annotator { background: var(--primary-blue); }
.stat-icon.auditor { background: #FFA000; }

.stat-info h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1a1f36;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: #697386;
    font-size: 0.875rem;
    margin: 0;
}

/* Search and Filters */
.search-filters {
    background: white;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

/* Users Table Styling */
.users-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.table-users {
    margin: 0;
    width: 100%;
}

.table-users thead th {
    background: #f8fafc;
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: #64748b;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e2e8f0;
}

.user-row {
    transition: opacity 0.2s ease;
}

.user-row:hover {
    background-color: #f8fafc;
}

.user-row.d-none {
    opacity: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4834d4, #686de0);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.user-details h6 {
    margin: 0;
    color: #1a1f36;
    font-weight: 600;
}

.user-details span {
    font-size: 0.875rem;
    color: #697386;
}

/* Badge Styling */
.role-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.875rem;
}

.role-badge.role-admin {
    background: rgba(222, 53, 11, 0.1);
    color: var(--danger-color);
}

.role-badge.role-annotator {
    background: rgba(0, 82, 204, 0.1);
    color: var(--primary-blue);
}

.role-badge.role-auditor {
    background: rgba(255, 193, 7, 0.1);
    color: #FFA000;
}

/* Status Badge - Updated */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(0, 135, 90, 0.1);
    color: var(--success-color);
}

.status-badge.suspended {
    background: rgba(222, 53, 11, 0.1);
    color: var(--danger-color);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.3s ease;
    background: #f8fafc;
    color: #64748b;
}

.btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-edit:hover {
    background: var(--primary-blue);
    color: white;
}

.btn-suspend:hover {
    background: var(--danger-color);
    color: white;
}

.btn-activate:hover {
    background: var(--success-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .search-filters {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .user-info {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .user-avatar {
        margin: 0 auto;
    }
}

/* No results message */
#noResultsMessage {
    padding: 2rem;
    text-align: center;
    color: var(--neutral-600);
}

/* Status Filter Buttons - Updated */
.btn-status-filter {
    background: white;
    color: #64748b;
    border: 1px solid #e2e8f0;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

/* All Status button */
.btn-status-filter[data-status="all"] {
    background: white;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-status-filter[data-status="all"]:hover,
.btn-status-filter[data-status="all"].active {
    background: var(--primary-blue);
    color: white;
}

/* Active status button */
.btn-status-filter[data-status="active"] {
    background: white;
    color: var(--success-color);
    border-color: var(--success-color);
}

.btn-status-filter[data-status="active"]:hover,
.btn-status-filter[data-status="active"].active {
    background: var(--success-color);
    color: white;
}

/* Suspended status button */
.btn-status-filter[data-status="suspended"] {
    background: white;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-status-filter[data-status="suspended"]:hover,
.btn-status-filter[data-status="suspended"].active {
    background: var(--danger-color);
    color: white;
}

/* Status Badge - Updated */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(0, 135, 90, 0.1);
    color: var(--success-color);
}

.status-badge.suspended {
    background: rgba(222, 53, 11, 0.1);
    color: var(--danger-color);
}

/* Button group for status filters */
.status-filter-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.status-filter-group .btn-status-filter {
    margin: 0;
}
