/* Root Variables */
:root {
    --primary-color: #0056b3;
    --secondary-color: #003366;
    --accent-color: #4a90e2;
    --text-light: #E5E5E5;
    --text-white: #FFFFFF;
    --text-color: #495057;
    --bg-color: #ffffff;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #dee2e6;
    --input-focus: var(--accent-color);
    --error-color: #dc3545;
    --success-color: #28a745;
    --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.25s ease;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --card-radius: 8px;
    --button-radius: 4px;
}

/* Dataset Selection Section Styles */
.dataset-selection-section {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--input-border);
    margin-bottom: 2rem;
}
.card-header h5{
    color: var(--primary-color);
}

.section-title {
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-divider {
    height: 1px;
    background: var(--input-border);
    margin: 2rem 0;
    position: relative;
}

.section-divider::before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -8px;
    width: 50px;
    height: 16px;
    background: var(--bg-color);
    border-radius: 8px;
}

.dataset-selection-container {
    padding: 1.5rem;
    background: rgba(245, 247, 250, 0.5);
    border-radius: var(--card-radius);
    border: 1px solid var(--input-border);
}

.dataset-info {
    min-height: 150px;
}

/* Nav Tab Styling */
.nav-tabs {
    border-bottom: 2px solid var(--input-border);
}

.nav-tabs .nav-link {
    border: none;
    color: var(--text-color);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    margin-right: 0.5rem;
    border-radius: var(--card-radius) var(--card-radius) 0 0;
    position: relative;
    transition: var(--transition-smooth);
}

.nav-tabs .nav-link:hover {
    background-color: rgba(245, 247, 250, 0.8);
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-color: transparent;
    color: var(--primary-color);
    font-weight: 600;
}

.nav-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--corporate-gradient);
    border-radius: 3px 3px 0 0;
}

/* Dataset Card Styling */
.card {
    border-radius: var(--card-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--input-border);
    transition: var(--transition-smooth);
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: rgba(245, 247, 250, 0.8);
    border-bottom: 1px solid rgba(222, 226, 230, 0.6);
    padding: 1rem 1.5rem;
}

.folder-status-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1rem;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 8px rgba(0,0,0,0.05);
}

.folder-status-header h2 {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.5rem;
}

.folder-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  background: #f9f9f9;
}

.folder-section-header h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.folder-section-body h4 {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.folder-stats {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #555;
  margin-bottom: 0.5rem;
}

.progress-wrapper {
  background: #eee;
  border-radius: 5px;
  overflow: hidden;
  height: 14px;
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: transparent;
  position: relative;
}

.progress-fill {
  height: 100%;
  color: white;
  text-align: right;
  padding-right: 6px;
  font-size: 12px;
  line-height: 14px;
}

.progress-fill.green {
  background-color: #28a745;
}

.progress-fill.blue {
  background-color: #0dcaf0;
}

.folder-action {
  text-align: right;
}

.folder-action a {
  font-size: 14px;
  text-decoration: none;
  color: #007bff;
  border: 1px solid #007bff;
  padding: 6px 12px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.folder-action a:hover {
  background-color: #007bff;
  color: #fff;
}

.empty-message {
  text-align: center;
  padding: 1rem 0;
  color: #666;
}

.empty-message i {
  font-size: 24px;
  margin-bottom: 0.5rem;
  display: block;
}

/* Updated Title Section Styling */
.page-title-section {
  text-align: center;
  margin: 2.5rem 0 3.5rem 0;
  position: relative;
}

.ocr-title {
  color: var(--secondary-color);
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

/* Blue underline effect */
.ocr-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 2px;
}

.ocr-tagline {
  color: var(--text-color);
  font-size: 1.1rem;
  font-weight: 400;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Adjust top spacing for the mode cards */
.mode-cards {
  margin-top: 3rem;
}

/* Mode Cards */
.mode-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.mode-card {
  background: var(--card-bg);
  border-radius: var(--card-radius);
  box-shadow: var(--shadow-sm);
  padding: 1.75rem;
  border: 1px solid var(--input-border);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--corporate-gradient);
  opacity: 0;
  transition: var(--transition-smooth);
}

.mode-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.mode-card:hover::before {
  opacity: 1;
}

.mode-card h5 {
  color: var(--primary-color);
  font-size: 1.35rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
}

.mode-card .input-group {
  margin-bottom: 1.25rem;
}

.mode-card .nas-path-display {
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-color);
  border-radius: var(--button-radius);
  padding: 0.875rem;
  font-size: 0.95rem;
  min-height: 50px;
  transition: var(--transition-smooth);
}

.mode-card .btn {
  height: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: var(--transition-smooth);
}

.mode-card .btn-primary {
  background: var(--corporate-gradient);
  border: none;
  color: var(--text-white);
  border-radius: var(--button-radius);
  transition: var(--transition-smooth);
}

.mode-card .btn-primary:hover {
  background: var(--corporate-gradient-hover);
}

.mode-card .btn-success {
  background: var(--success-color);
  border: none;
  color: var(--text-white);
  border-radius: var(--button-radius);
}

.mode-card .btn-outline-secondary {
  border-color: var(--input-border);
  color: var(--text-color);
}

.mode-card .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Card Content Area */
.mode-card-content {
  flex: 1;
}

/* Button Container */
.mode-card-actions {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

.mode-card-actions .btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--button-radius);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.mode-card-actions .btn-outline-secondary {
  color: var(--text-color);
  border-color: var(--input-border);
}

.mode-card-actions .btn-success {
  background: var(--success-color);
  border: none;
  color: var(--text-white);
}

.mode-card-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Status Section */
.status-section {
  background: var(--card-bg);
  border-radius: var(--card-radius);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--input-border);
}

.status-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.status-section-header h6 {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-bottom: 0;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-section-header .refresh-btn {
  background: transparent;
  color: var(--primary-color);
  border: none;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  width: 40px;
  height: 40px;
}

.status-section-header .refresh-btn:hover {
  background: rgba(0, 86, 179, 0.1);
  transform: rotate(180deg);
}

.status-section-header .refresh-btn i {
  font-size: 1.25rem;
}

.status-item {
  padding: 1.5rem;
  background: var(--input-bg);
  border-radius: var(--card-radius);
  margin-bottom: 1.25rem;
  border: 1px solid var(--input-border);
  transition: var(--transition-smooth);
}

.status-item:hover {
  box-shadow: var(--shadow-sm);
}

.status-item h6 {
  color: var(--text-color);
  font-size: 1.1rem;
  margin-bottom: 1rem;
}

.status-item small {
  color: var(--text-color);
  font-size: 0.9rem;
}

.progress {
  height: 8px;
  background: var(--input-border);
  overflow: hidden;
  border-radius: var(--button-radius);
  margin: 1rem 0;
}

.progress-bar {
  transition: width 0.6s ease;
  background: var(--corporate-gradient);
}

.progress-bar.bg-info {
  background: var(--accent-color);
}

/* NAS Browser Styling */
.nas-browser-modal .modal-dialog {
  max-width: 90%;
  width: 1000px;
}

.nas-browser-modal .modal-content {
  border-radius: var(--card-radius);
  border: none;
  box-shadow: var(--shadow-md);
}

.nas-browser-container {
  height: 400px;
  overflow-y: auto;
  border: 1px solid var(--input-border);
  border-radius: var(--button-radius);
  padding: 1rem;
  width: 100%;
}

.nas-breadcrumb {
  background-color: var(--input-bg);
  padding: 0.75rem 1rem;
  border-radius: var(--button-radius);
  margin-bottom: 1rem;
  border: 1px solid var(--input-border);
}

.nas-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--input-border);
  cursor: pointer;
  transition: var(--transition-smooth);
}

.nas-item:hover {
  background-color: var(--input-bg);
  transform: translateX(5px);
}

.nas-item i {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.nas-item.folder i {
  color: #ffc107;
}

.nas-item.file i {
  color: var(--text-color);
}

.nas-item.file.json i {
  color: #17a2b8;
}

.nas-item .nas-item-name {
  flex-grow: 1;
  font-size: 0.95rem;
  color: var(--text-color);
}

.nas-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: var(--primary-color);
}

/* Input Group Refinements */
.input-group {
  align-items: stretch;
  gap: 0.5rem;
}

.input-group .form-control.nas-path-display {
  margin: 0;
  height: auto;
  min-height: 50px;
  padding: 0.875rem 1rem;
  border-radius: var(--button-radius);
}

/* Alert Styling */
.alert-light {
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-color);
  border-radius: var(--button-radius);
}

/* Button States */
.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .mode-cards {
    grid-template-columns: 1fr;
  }
  
  .ocr-header {
    padding: 1.5rem;
  }
  
  .mode-card, .status-section {
    padding: 1.25rem;
  }
}