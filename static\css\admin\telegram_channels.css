/* Telegram Channel Browser Styling */
:root {
    --primary-color: #4568dc;
    --primary-gradient: linear-gradient(135deg, #4568dc, #b06ab3);
    --secondary-color: #202731;
    --accent-color: #ff5c5c;
    --text-color: #333;
    --light-color: #f8f9fa;
    --dark-color: #202731;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    --card-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --hero-bg: #f4f7ff;
    --header-gradient: linear-gradient(135deg, #f7f9ff, #eef2ff);
    --section-gradient: linear-gradient(135deg, rgba(244, 247, 255, 0.95), rgba(232, 240, 255, 0.9));
    --card-bg: rgba(255, 255, 255, 0.9);
    --heading-color: #2d3748;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.noise-texture {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.03'/%3E%3C/svg%3E");
    opacity: 0.15;
    z-index: -1;
    pointer-events: none;
}

.background-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(to right, rgba(69, 104, 220, 0.03) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(69, 104, 220, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
    opacity: 0.8;
}

.floating-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.1;
    filter: blur(20px);
}

.shape-1 {
    width: 400px;
    height: 400px;
    top: -100px;
    right: -200px;
    animation: floatAnimation 25s infinite linear;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -50px;
    left: -150px;
    animation: floatAnimation 20s infinite linear reverse;
    background: linear-gradient(135deg, #b06ab3, #4568dc);
}

.shape-3 {
    width: 200px;
    height: 200px;
    top: 40%;
    right: 20%;
    animation: floatAnimation 15s infinite linear;
    opacity: 0.08;
}

@keyframes floatAnimation {
    0% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
        transform: translate(10px, 10px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translate(0, 20px) rotate(10deg) scale(1);
    }
    75% {
        transform: translate(-10px, 10px) rotate(5deg) scale(0.95);
    }
    100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
}

/* Main container spacing */
.container {
    position: relative;
    z-index: 1;
}

/* Page title */
h1.mb-4 {
    color: var(--heading-color);
    font-weight: 600;
    font-size: 2rem;
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem !important;
    padding-bottom: 10px;
}

h1.mb-4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Card styles */
.card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(69, 104, 220, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    z-index: 1;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(69, 104, 220, 0.15);
}

.card-title {
    font-weight: 600;
    color: var(--heading-color);
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.card-body {
    padding: 1.8rem;
}

/* Channel card styles */
.channel-card {
    height: 100%;
    transition: transform 0.2s;
    cursor: pointer;
}

.channel-card:hover {
    transform: translateY(-5px);
}

/* Image container */
.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 4px 4px 0 0;
    aspect-ratio: 1/1;
    background-color: var(--hero-bg);
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-container:hover img {
    transform: scale(1.05);
}

/* Status messages */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: none;
}

/* Loading spinner */
#loading-spinner {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--box-shadow);
}

/* Form elements */
.form-control, .form-select {
    border: 1px solid rgba(69, 104, 220, 0.2);
    border-radius: 8px;
    padding: 12px 15px;
    background-color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(69, 104, 220, 0.1);
}

.form-label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

/* Button styling */
.btn {
    border-radius: 50px;
    padding: 8px 20px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    color: white;
    box-shadow: 0 5px 15px rgba(69, 104, 220, 0.2);
}

.btn-primary:hover,
.btn-primary:focus {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(69, 104, 220, 0.3);
    background: linear-gradient(135deg, #3f5ccf, #a259a7);
    border: none;
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 1px solid rgba(69, 104, 220, 0.3);
    background: transparent;
}

.btn-outline-primary:hover {
    background: rgba(69, 104, 220, 0.08);
    color: var(--primary-color);
}

.btn-danger {
    background: linear-gradient(135deg, #e53e3e, #f56565);
    border: none;
    color: white;
    box-shadow: 0 5px 15px rgba(229, 62, 62, 0.2);
}

.btn-danger:hover {
    transform: translateY(-3px);
    background: linear-gradient(135deg, #c53030, #e53e3e);
    box-shadow: 0 8px 25px rgba(229, 62, 62, 0.3);
}

.btn-secondary {
    background-color: #edf2ff;
    color: var(--text-primary);
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
    background-color: #e4e9f7;
    transform: translateY(-3px);
}

/* Alert styling */
.alert {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
}

.alert-info {
    background-color: rgba(66, 133, 244, 0.1);
    border-color: rgba(66, 133, 244, 0.2);
    color: var(--primary-color);
}

.alert-danger {
    background-color: rgba(229, 62, 62, 0.1);
    border-color: rgba(229, 62, 62, 0.2);
    color: #c53030;
}

.alert-success {
    background-color: rgba(39, 174, 96, 0.1);
    border-color: rgba(39, 174, 96, 0.2);
    color: #2f855a;
}

.alert-warning {
    background-color: rgba(243, 156, 18, 0.1);
    border-color: rgba(243, 156, 18, 0.2);
    color: #c05621;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        margin-top: 70px !important;
    }
    
    .card-body {
        padding: 1.2rem;
    }
    
    .status-message {
        max-width: 90%;
        right: 5%;
        left: 5%;
    }
    
    h1.mb-4 {
        font-size: 1.75rem;
    }
} 

.channel-card {
    transition: transform 0.2s;
    cursor: pointer;
}

.channel-card:hover {
    transform: translateY(-5px);
}

.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: none;
}

/* Form styling */
.form-section {
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #333;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 500;
    color: #555;
}

.form-control {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.75rem;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Card styling */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-title {
    color: #333;
    font-size: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button styling */
.btn-primary {
    background-color: #007bff;
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
    transition: all 0.2s ease;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    color: white;
}

/* Loading spinner */
.loading-spinner {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Session status */
.session-status {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 4px;
}

/* Help text */
.help-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .form-section {
        padding: 1rem;
    }
}

/* Animation for status messages */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.status-message.show {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
} 
