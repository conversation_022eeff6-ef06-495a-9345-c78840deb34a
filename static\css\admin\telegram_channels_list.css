/* Telegram Channels List - Corporate Style */

:root {
    --corporate-primary: #0056b3;
    --corporate-secondary: #003366;
    --corporate-accent: #0072C6;
    --corporate-success: #28a745;
    --corporate-warning: #ffc107;
    --corporate-danger: #dc3545;
    --corporate-light: #f8f9fa;
    --corporate-dark: #343a40;
    --corporate-gray: #6c757d;
    --corporate-light-gray: #dee2e6;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition: all 0.25s ease;
    --card-radius: 6px;
    --button-radius: 4px;
}

body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, <PERSON>bu<PERSON>u, Cantarell, 'Helvetica Neue', sans-serif;
    color: #495057;
}

.page-container {
    max-width: 100%;
    padding: 1.5rem 2rem;
}

.content-wrapper {
    min-height: calc(100vh - 60px);
    background-color: #f5f7fa;
}

/* Section header styling */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.07);
    background: linear-gradient(to right, rgba(0, 86, 179, 0.02), rgba(0, 86, 179, 0.08), rgba(0, 86, 179, 0.02));
    padding-top: 2rem;
    border-radius: 8px;
}

.section-header h1 {
    color: var(--corporate-secondary);
    font-weight: 800;
    margin-bottom: 0;
    font-size: 2.2rem;
    letter-spacing: -0.02em;
}

.channel-count-badge {
    background-color: var(--corporate-primary);
    color: white;
    padding: 4px 12px;
    border-radius: 50px;
    font-size: 0.85rem;
    margin-left: 12px;
    font-weight: 500;
    vertical-align: middle;
    box-shadow: 0 2px 4px rgba(0, 86, 179, 0.2);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

/* Dashboard layout */
.dashboard {
    display: flex;
    gap: 30px;
    min-height: calc(100vh - 220px);
    margin-bottom: 2rem;
}

.channels-container {
    flex: 0 0 68%;
    padding-bottom: 30px;
}
.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: #ffffff;
    --bs-backdrop-opacity: 0.5;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: var(--bs-backdrop-bg);
}
.analysis-container {
    flex: 0 0 30%;
    padding-bottom: 30px;
    position: sticky;
    top: 20px;
}

/* Card styling */
.card {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    background-color: white;
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h5 {
    margin: 0;
    color: var(--corporate-secondary);
    font-weight: 600;
    font-size: 1.2rem;
}

.card-header.bg-primary {
    background: linear-gradient(to right, #0056b3, #0072C6) !important;
    color: white !important;
    border-bottom: 1px solid #0056b3;
}

.card-header.bg-primary h5 {
    color: white;
    font-weight: 700;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.05);
    padding: 1rem 1.5rem;
}

/* Channel grid */
.channel-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* Channel card styling */
.channel-card {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    background-color: white;
    height: 100%;
}

.channel-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--corporate-gradient);
    opacity: 0;
    transition: var(--transition);
}

.channel-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.channel-card:hover::before {
    opacity: 1;
}

.channel-card.active {
    border-left: none;
    background-color: rgba(13, 110, 253, 0.02);
}

.channel-card.active::before {
    opacity: 1;
    width: 6px;
}

.channel-card .card-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--corporate-secondary);
    font-weight: 600;
    font-size: 1.1rem;
}

.channel-card .card-body {
    padding: 1.5rem;
}

.card-actions {
    display: flex;
    gap: 10px;
}

/* Button styling */
.btn {
    border-radius: var(--button-radius);
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background: var(--corporate-gradient);
    border-color: transparent;
    color: white;
}

.btn-primary:hover {
    background: var(--corporate-gradient-hover);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: transparent;
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-light {
    background-color: rgba(255, 255, 255, 0.9);
    border-color: rgba(255, 255, 255, 0.3);
    color: #0056b3;
}

.btn-light:hover {
    background-color: white;
    border-color: white;
    color: var(--corporate-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.card-header .btn-light {
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.card-header .btn-light:hover {
    background-color: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #218838);
    border-color: transparent;
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Status message */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    border-radius: var(--card-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 1rem;
    max-width: 350px;
}

/* Channel icon */
.channel-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    background: var(--corporate-gradient);
    color: white;
}

/* Stats badges */
.stat-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    border-radius: 6px;
    padding: 0.7rem 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    height: 100%;
}

.stat-badge .badge {
    font-size: 1rem;
    margin-bottom: 4px;
    font-weight: 600;
    padding: 0.4rem 0.6rem;
    white-space: nowrap;
    min-width: 80px;
    text-align: center;
}

.stat-badge small {
    font-size: 0.75rem;
    color: var(--corporate-gray);
    text-align: center;
    white-space: nowrap;
}

/* Analysis container styles */
#analysis-loading {
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Loading spinner */
#loading-spinner {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: 1.8rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Table styles */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    padding: 0.85rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
    font-weight: 600;
    color: var(--corporate-secondary);
    background-color: rgba(0,0,0,0.02);
}

.table td {
    padding: 0.85rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,0.04);
}

.table-primary, .table-primary > th, .table-primary > td {
    background-color: rgba(0, 86, 179, 0.1);
}

.progress {
    height: 6px;
    border-radius: 3px;
    background-color: rgba(0,0,0,0.05);
}

.progress-bar.bg-primary {
    background: var(--corporate-gradient) !important;
}

/* Badge styling */
.badge {
    border-radius: 50rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
    font-size: 85%;
    text-align: center;
    white-space: nowrap;
}

.badge.bg-primary {
    background-color: var(--corporate-primary) !important;
}

.badge.bg-success {
    background-color: var(--corporate-success) !important;
}

.badge.bg-info {
    background-color: var(--corporate-accent) !important;
    color: white;
}

.badge.bg-warning {
    background-color: var(--corporate-warning) !important;
    color: #212529;
}

.badge.bg-secondary {
    background-color: var(--corporate-gray) !important;
}

/* Responsive design */
@media (max-width: 1199px) {
    .channel-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
}

@media (max-width: 991px) {
    .dashboard {
        flex-direction: column;
    }
    
    .channels-container, .analysis-container {
        flex: 0 0 100%;
    }
    
    .analysis-container {
        position: static;
    }
}

@media (max-width: 767px) {
    .page-container {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .section-header div {
        width: 100%;
    }
    
    .section-header div .btn {
        width: 100%;
    }
    
    .channel-grid {
        grid-template-columns: 1fr;
    }
}

/* Analysis content styling fixes */
#analysis-content {
    color: #495057;
}

#analysis-content h4 {
    color: var(--corporate-secondary);
}

#analysis-content .text-muted {
    color: #6c757d !important;
}

#analysis-content .card-header.bg-light {
    background-color: rgba(0, 86, 179, 0.05) !important;
    border-bottom: 1px solid rgba(0, 86, 179, 0.1);
}

#analysis-content .card-header.bg-light h5 {
    color: var(--corporate-secondary);
    font-weight: 600;
}

.tab-content {
    color: #495057;
}

.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: var(--corporate-primary);
    font-weight: 600;
    border-bottom-color: var(--corporate-primary);
}

.nav-tabs .nav-link:hover {
    color: var(--corporate-primary);
    border-color: #e9ecef #e9ecef #dee2e6;
}

#selected-channel-name {
    color: var(--corporate-secondary);
}

#selected-channel-details {
    color: var(--corporate-gray) !important;
}

/* Table data colors */
.table-primary th {
    color: var(--corporate-secondary);
    font-weight: 600;
    background-color: rgba(0, 86, 179, 0.1);
}

.table td {
    color: #495057;
}

.text-muted {
    color: #6c757d !important;
}

/* No channel selected state */
#no-channel-selected .channel-icon {
    background: rgba(0, 86, 179, 0.1);
    color: var(--corporate-primary);
}

#no-channel-selected h4 {
    color: var(--corporate-secondary);
} 