/* Enhanced Telegram Channels CSS */

:root {
  --corporate-primary: #0056b3;
  --corporate-secondary: #003366;
  --corporate-accent: #0072C6;
  --corporate-success: #28a745;
  --corporate-warning: #ffc107;
  --corporate-danger: #dc3545;
  --corporate-light: #f8f9fa;
  --corporate-dark: #343a40;
  --corporate-gray: #6c757d;
  --corporate-light-gray: #dee2e6;
  --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
  --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  --transition: all 0.25s ease;
  --card-radius: 8px;
  --button-radius: 4px;
}


body {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  color: #495057;
  background-color: white;
  line-height: 1.6;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Visual Elements - Static versions */
.noise-texture {
  display: none;
}

.background-grid {
  display: none;
}

.floating-shapes .shape {
  display: none;
}

/* Content */
.content-wrapper {
  position: relative;
  min-height: calc(100vh - 50px);
  z-index: 1;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem 2rem;
  background: var(--corporate-gradient);
  border-radius: var(--card-radius);
  box-shadow: var(--box-shadow);
  width: 100%;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: white;
  margin: 0;
}

/* Back button */
.back-button {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border-radius: var(--button-radius);
  background: var(--corporate-gradient);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.back-button:hover {
  background: var(--corporate-gradient-hover);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.back-button i {
  margin-right: 0.5rem;
}

/* Connection Forms Container - updated for single column */
.connection-forms-container {
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
  display: block;
}

/* Center the Connect to Telegram form */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
  width: 100%;
}

#connection-form {
  width: 100%;
  margin: 0 auto;
}

/* Remove the now unused API credentials card styles */
.connection-card.api-credentials {
  display: none;
}

/* Connection Card */
.connection-card {
  background-color: white;
  border-radius: var(--card-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
  overflow: hidden;
  width: 100%;
  border: none;
  transition: var(--transition);
}

.connection-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-3px);
}

.connection-card .card-header {
  background: var(--corporate-gradient);
  color: white;
  padding: 1.25rem 1.5rem;
  border-bottom: none;
}

.connection-card .card-title {
  color: white;
  margin: 0;
  font-weight: 600;
  font-size: 1.4rem;
  display: flex;
  align-items: center;
}

.connection-card .card-title i {
  margin-right: 0.75rem;
  font-size: 1.6rem;
}

.connection-card .card-body {
  padding: 1.5rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
  width: 100%;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--corporate-secondary);
  display: block;
  font-size: 1rem;
}

.form-control {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--button-radius);
  padding: 0.75rem 1rem;
  width: 100%;
  font-size: 1rem;
  display: block;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--corporate-primary);
  box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
  outline: none;
}

.form-text {
  font-size: 0.875rem;
  color: var(--corporate-gray);
  margin-top: 0.5rem;
}

/* Session status */
.session-status {
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  border-radius: var(--button-radius);
  margin-bottom: 1.5rem;
  background-color: rgba(0, 86, 179, 0.1);
  border-left: 4px solid var(--corporate-primary);
  width: 100%;
}

.session-status i {
  font-size: 1.3rem;
  color: var(--corporate-primary);
  margin-right: 1rem;
}

/* Status messages */
.status-message.success {
  background: linear-gradient(135deg, #28a745, #218838);
  color: white;
}

.status-message.error {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.status-message.warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.status-message.info {
  background: linear-gradient(135deg, #0056b3, #004494);
  color: white;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--button-radius);
  font-weight: 500;
  cursor: pointer;
  font-size: 1rem;
  transition: var(--transition);
  gap: 0.5rem;
}

.btn i {
  font-size: 0.9em;
}

.btn-primary {
  background: var(--corporate-gradient);
  color: white;
  border: none;
}

.btn-primary:hover {
  background: var(--corporate-gradient-hover);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
  background-color: #f8f9fa;
  color: var(--corporate-primary);
  border: 1px solid rgba(0, 86, 179, 0.2);
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: var(--corporate-secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Status Message */
.status-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: white;
  color: #212529;
  padding: 1rem 1.25rem;
  border-radius: var(--card-radius);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  z-index: 1000;
  max-width: 350px;
}

/* Loading Spinner */
.loading-spinner {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 86, 179, 0.2);
  border-radius: 50%;
  border-top-color: var(--corporate-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Card coming soon */
.card-actions-coming-soon {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: var(--button-radius);
  padding: 1.5rem;
  text-align: center;
  color: var(--corporate-gray);
  font-style: italic;
  width: 100%;
}

/* Button group spacing */
.d-flex.justify-content-between {
  gap: 1rem;
}

/* Responsive Design - simplified */
@media (max-width: 992px) {
  .connection-forms-container {
    max-width: 100%;
    padding: 0 1rem;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.25rem;
  }
  
  .page-title {
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }
  
  .connection-card .card-header {
    padding: 1rem 1.25rem;
  }
  
  .connection-card .card-body {
    padding: 1.25rem;
  }
  
  .connection-card .card-title {
    font-size: 1.25rem;
  }
  
  .connection-card .card-title i {
    font-size: 1.5rem;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn {
    width: 100%;
  }
}

/* Override any status or button colors */
.back-button {
  background: #f0f0f0;
  color: #333;
}

.back-button:hover {
  background: #e0e0e0;
  color: #333;
}

/* Alert info box */
.alert-info {
  background-color: rgba(0, 86, 179, 0.05);
  border-left: 4px solid var(--corporate-primary);
  color: #495057;
}

/* Make links blue */
a {
  color: var(--corporate-primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--corporate-secondary);
  text-decoration: underline;
}

/* Make API credentials card match header style */
.connection-card.api-credentials .card-header {
  background: var(--corporate-gradient);
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
}

.connection-card.api-credentials .card-title i {
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 0;
}
