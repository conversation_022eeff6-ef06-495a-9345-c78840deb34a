/* Telegram Images - Corporate Style */

:root {
    --corporate-primary: #0056b3;
    --corporate-secondary: #003366;
    --corporate-accent: #0072C6;
    --corporate-success: #28a745;
    --corporate-warning: #ffc107;
    --corporate-danger: #dc3545;
    --corporate-light: #f8f9fa;
    --corporate-dark: #343a40;
    --corporate-gray: #6c757d;
    --corporate-light-gray: #dee2e6;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition: all 0.25s ease;
    --card-radius: 6px;
    --button-radius: 4px;
}

body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    color: #495057;
}

.content-wrapper {
    min-height: calc(100vh - 60px);
    background-color: #f5f7fa;
}

.container-fluid {
    padding: 1.5rem 2rem;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
}

.page-header h1 {
    color: var(--corporate-secondary);
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.75rem;
}

/* Modal backdrop fix */
.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: #ffffff;
    --bs-backdrop-opacity: 0.5;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: var(--bs-backdrop-bg);
}

/* Back button */
.back-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Filter Section */
.filter-section {
    margin-bottom: 1.5rem;
    background-color: #fff;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.filter-header {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--corporate-secondary);
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-content {
    padding: 1.5rem;
}

.filter-content .form-label {
    font-weight: 500;
    color: var(--corporate-secondary);
    margin-bottom: 0.5rem;
}

.form-select {
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    padding: 0.75rem 1rem;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: var(--transition);
    color: #495057;
}

.form-select:focus {
    border-color: var(--corporate-primary);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.form-select:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
}

/* Selection Controls */
.selection-controls {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    background-color: white;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.selection-controls .card-body {
    padding: 1rem 1.5rem;
}

.selected-count {
    margin-left: 1rem;
    color: var(--corporate-gray);
    font-weight: 500;
}

/* Button styling */
.btn {
    border-radius: var(--button-radius);
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    gap: 0.5rem;
}

/* Add space between select-all and deselect-all buttons */
#select-all-btn {
    margin-right: 1rem;
}

.btn i {
    font-size: 0.9em;
}

.btn-primary {
    background: var(--corporate-gradient);
    border-color: transparent;
    color: white;
}

.btn-primary:hover {
    background: var(--corporate-gradient-hover);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

.btn-secondary {
    background-color: #eef0f2;
    border-color: #eef0f2;
    color: #495057;
}

.btn-secondary:hover {
    background-color: #dee2e6;
    border-color: #dee2e6;
    color: #212529;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #218838);
    border-color: transparent;
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-outline-primary {
    color: var(--corporate-primary);
    border-color: var(--corporate-primary);
}

.btn-outline-primary:hover {
    background-color: var(--corporate-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-outline-secondary {
    color: var(--corporate-gray);
    border-color: var(--corporate-gray);
}

.btn-outline-secondary:hover {
    background-color: var(--corporate-gray);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
}

/* Images Grid */
.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Image Card */
.image-card {
    background-color: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: none;
}

.image-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.image-card.selected {
    border: 2px solid var(--corporate-primary);
}

.image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-card:hover .image-container img {
    transform: scale(1.05);
}

.selection-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 86, 179, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-card.selected .selection-overlay {
    opacity: 1;
}

.image-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    z-index: 2;
    cursor: pointer;
}

.card-body {
    padding: 1.25rem;
}

.card-text {
    color: #495057;
    margin-bottom: 0.75rem;
}

.text-muted {
    color: var(--corporate-gray) !important;
    font-size: 0.875rem;
}

.image-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.image-actions .btn {
    flex: 1;
    min-width: 90px;
    padding: 0.4rem 0.6rem;
    font-size: 0.875rem;
}

.image-actions .btn span {
    display: inline;
}

@media (max-width: 500px) {
    .image-actions .btn span {
        display: none;
    }
    
    .image-actions .btn {
        padding: 0.4rem;
    }
}

/* Skeleton loading */
.skeleton-card {
    background-color: white;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    height: 350px;
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: pulse 1.5s ease-in-out infinite;
    border-radius: 4px;
}

.skeleton-image {
    height: 200px;
    width: 100%;
}

.skeleton-body {
    padding: 1.25rem;
}

.skeleton-text {
    height: 18px;
    margin-bottom: 0.75rem;
    width: 80%;
}

.skeleton-text-sm {
    height: 14px;
    margin-bottom: 1rem;
    width: 60%;
}

.skeleton-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.skeleton-button {
    height: 34px;
    flex: 1;
    border-radius: var(--button-radius);
}

@keyframes pulse {
    0% {
        background-position: 0% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Alert messages */
.alert-message {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
}

.alert-message i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.alert-message .alert-heading {
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 1.25rem;
    color: #212529;
}

.alert-message p {
    margin-bottom: 0;
    color: #495057;
}

.alert-info {
    background-color: #0056b3;
    color: white;
    border-left: none;
    border-radius: 0;
    margin: 20px 0;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    text-align: left;
}

.alert-info i {
    color: white;
    display: none;
}

.alert-info .alert-heading {
    display: none;
}

.alert-info p {
    color: white;
    margin: 0;
    font-weight: normal;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.05);
    border-left: 4px solid var(--corporate-warning);
}

.alert-warning i {
    color: var(--corporate-warning);
}

/* Loading spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 86, 179, 0.2);
    border-radius: 50%;
    border-top-color: var(--corporate-primary);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Status message */
.status-message {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 1rem 1.25rem;
    border-radius: var(--card-radius);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    min-width: 280px;
    max-width: 450px;
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #28a745, #218838);
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.alert-info {
    background: linear-gradient(135deg, #0056b3, #004494);
}

/* Modal styling */
.modal-content {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: var(--corporate-primary);
    color: white;
    padding: 15px 20px;
}

.modal-title {
    font-size: 16px;
    font-weight: 500;
}

.modal-title i {
    font-size: 18px;
}

.btn-close {
    background: none;
    border: none;
    color: white;
    opacity: 1;
    padding: 0;
    font-size: 20px;
}

.btn-close:hover {
    opacity: 0.8;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0,0,0,0.05);
    padding: 1rem 1.5rem;
}

.modal-lg {
    max-width: 800px;
}

.modal-xl {
    max-width: 95%;
}

.modal-image {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Image editor styling */
.img-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.crop-preview {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 10px;
    border: 1px solid #dee2e6;
}

.list-group-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid rgba(0,0,0,0.1);
    margin-bottom: 0.5rem;
    border-radius: 4px;
}

.crop-info {
    flex: 1;
}

.crop-actions .btn-remove {
    background-color: transparent;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    line-height: 1;
    border-radius: 4px;
}

.crop-actions .btn-remove:hover {
    background-color: rgba(220, 53, 69, 0.1);
}

.bg-light {
    background-color: rgba(0, 86, 179, 0.05) !important;
}

/* Card header styling */
.card-header {
    background-color: rgba(0, 86, 179, 0.05);
    border-bottom: 1px solid rgba(0, 86, 179, 0.1);
    padding: 1rem 1.5rem;
}

.card-header h6 {
    font-weight: 600;
    color: var(--corporate-secondary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Upload modal specifics */
#upload-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: rgba(0, 86, 179, 0.05);
    border-left: 4px solid var(--corporate-primary);
    padding: 1rem;
    border-radius: 4px;
}

#upload-error {
    background-color: rgba(220, 53, 69, 0.05);
    border-left: 4px solid var(--corporate-danger);
    padding: 1rem;
    border-radius: 4px;
}

.list-group-flush .list-group-item {
    border-radius: 0;
    border-left: none;
    border-right: none;
    margin-bottom: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: none;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: none;
}

/* Fix for small screens */
@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .date-filters {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-group {
        width: 100%;
    }
    
    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .selection-controls .d-flex {
        flex-direction: column;
        gap: 1rem;
    }
    
    .selected-count {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

/* Image editor fix */
.cropper-container {
    z-index: 1050 !important;
}

.img-container img {
    max-width: 100% !important;
    max-height: 100% !important;
}

/* Empty channel message */
#empty-channel-message {
    background: var(--corporate-gradient);
    color: white;
    max-width: 800px;
    margin: 40px auto;
    border-radius: 8px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.15);
    padding: 30px 20px;
    text-align: center;
    border: none;
}

#empty-channel-message i {
    color: white;
    opacity: 0.9;
    margin-bottom: 15px;
    display: inline-block;
    font-size: 2.5rem;
}

#empty-channel-message h3 {
    color: white;
    font-weight: 600;
    margin-bottom: 12px;
    font-size: 1.6rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

#empty-channel-message p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    font-size: 1.1rem;
}

/* Dropdown items styling */
.dropdown-item.disabled,
.dropdown-item.coming-soon {
    color: #6c757d;
    cursor: not-allowed;
}

.dropdown-item.disabled {
    opacity: 0.65;
}

.dropdown-item.disabled:hover {
    background-color: transparent;
}

/* Fix dropdown positioning */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    z-index: 1000;
    top: 100%;
    right: 0;
    min-width: 10rem;
    margin-top: 0.125rem;
}

/* Action buttons container */
.action-buttons-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
}

.action-buttons-right {
    display: flex;
    gap: 0.5rem;
}

/* Upload Results Modal Styling */
.upload-status {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: #E6E6E6;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    width: 40%;
    height: 100%;
    background-color: var(--corporate-primary);
    border-radius: 3px;
    animation: progress-animation 2s infinite;
    transform-origin: left;
}

@keyframes progress-animation {
    0% {
        transform: scaleX(0.1);
    }
    50% {
        transform: scaleX(0.5);
    }
    100% {
        transform: scaleX(0.1);
    }
}

.upload-text {
    color: #666;
    font-size: 14px;
    text-align: left;
}

/* Results Sections */
.result-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 1.5rem;
    border: 1px solid rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.section-header {
    background: rgba(0, 86, 179, 0.05);
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid rgba(0, 86, 179, 0.1);
}

.section-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--corporate-secondary);
    font-size: 1rem;
}

.section-header i {
    color: var(--corporate-primary);
    font-size: 1.1rem;
}

.section-content {
    padding: 1.25rem;
}

/* Info Rows */
.info-row {
    display: flex;
    align-items: baseline;
    margin-bottom: 1rem;
    gap: 0.75rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    color: var(--corporate-gray);
    font-weight: 500;
    min-width: 100px;
}

.info-value {
    color: var(--corporate-dark);
    flex-grow: 1;
    word-break: break-all;
}

/* Files List */
.files-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.files-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;
}

.files-list li:last-child {
    border-bottom: none;
}

.files-list li:hover {
    background-color: rgba(0, 86, 179, 0.02);
}

.files-list .btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}

/* Error State */
.upload-error {
    background-color: rgba(220, 53, 69, 0.05);
    border-radius: 8px;
    padding: 1.25rem;
    margin-top: 1rem;
}

.error-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.error-content i {
    color: var(--corporate-danger);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.error-message h6 {
    color: var(--corporate-danger);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.error-message p {
    color: var(--corporate-dark);
    margin-bottom: 0;
}

/* Link Styling */
.link-primary {
    color: var(--corporate-primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

.link-primary:hover {
    color: var(--corporate-secondary);
    text-decoration: underline;
}

/* Spinner Animation */
.upload-status .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Modal Footer */
.modal-footer {
    background-color: rgba(0, 0, 0, 0.02);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

#view-folder-btn {
    background: var(--corporate-gradient);
    border: none;
    transition: all 0.3s ease;
}

#view-folder-btn:hover {
    background: var(--corporate-gradient-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .upload-progress {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .info-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .info-label {
        min-width: auto;
    }

    .files-list li {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }
}
  