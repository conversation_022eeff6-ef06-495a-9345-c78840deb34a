body {
    background-color: #f8f9fa;
    overflow-x: hidden;
    height: 100vh;
}
.navbar-brand {
    font-weight: 700;
    letter-spacing: 1px;
}
/* Main container */
.container.py-4 {
    height: calc(100vh - 70px); /* Adjust for navbar height */
    display: flex;
    flex-direction: column;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    max-width: 1600px; /* Limit max width for better layout control */
    margin: 0 auto;
}
/* Main layout with sidebar - 3-column design */
.annotation-layout {
    display: flex;
    gap: 20px;
    position: relative;
    align-items: flex-start;
    justify-content: space-between;
    flex: 1;
    min-height: 0; /* Important for flex child to not overflow */
    margin-bottom: 10px;
    overflow: visible;
}

/* Guidelines sidebar - left side */
.guidelines-sidebar {
    width: 260px; /* Increased from 220px */
    height: 100%;
    position: sticky;
    top: 20px;
    z-index: 10;
    flex-shrink: 0; /* Prevent shrinking */
}

.guidelines-card {
    height: 100%;
    width: 100%; /* Full width of parent */
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 0;
}

.guidelines-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 12px 15px;
}

.guidelines-card .card-body,
.shortcuts-sidebar .card-body {
    padding: 0;
    overflow-y: auto;
    max-height: calc(100vh - 180px);
    scrollbar-width: thin;
    scrollbar-color: #d1e1f0 #fff;
}

.guidelines-card .card-body::-webkit-scrollbar,
.shortcuts-sidebar .card-body::-webkit-scrollbar {
    width: 6px;
}

.guidelines-card .card-body::-webkit-scrollbar-track,
.shortcuts-sidebar .card-body::-webkit-scrollbar-track {
    background: #fff;
}

.guidelines-card .card-body::-webkit-scrollbar-thumb,
.shortcuts-sidebar .card-body::-webkit-scrollbar-thumb {
    background-color: #d1e1f0;
    border-radius: 6px;
}

.best-practices {
    padding: 15px;
    border-top: 1px solid rgba(0,0,0,0.05);
}

.best-practices li {
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.instructions-content {
    padding: 15px;
}

/* Shortcuts sidebar - right side */
.shortcuts-sidebar {
    width: 260px; /* Increased from 220px */
    height: 100%;
    position: sticky;
    top: 20px;
    z-index: 10;
    flex-shrink: 0; /* Prevent shrinking */
}

.shortcuts-sidebar .card {
    width: 100%; /* Full width of parent */
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 0;
}

/* Common card styling for both sidebars */
.guidelines-sidebar .card, 
.shortcuts-sidebar .card {
    border-radius: 10px;
    background-color: #fff;
    overflow: hidden;
}

.guidelines-sidebar .card-header, 
.shortcuts-sidebar .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 12px 15px;
    font-weight: 500;
}

.shortcut-list {
    padding: 0;
}

.shortcut-item {
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.shortcut-item:last-child {
    border-bottom: none;
}

.shortcut-text {
    font-size: 0.95rem;
}

.key {
    background-color: #f8f9fa;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.annotation-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 5px;
    position: relative;
    flex: 1; /* Take up available space */
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: 0; /* Allow shrinking */
    overflow: hidden;
    max-width: calc(100% - 540px); /* Limit width to account for wider sidebars */
    margin: 0 auto;
}

.annotation-main {
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.image-display {
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    position: relative;
    overflow: hidden; /* For zoomed images */
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e9ecef;
    box-shadow: inset 0 0 10px rgba(0,0,0,0.03);
    margin-bottom: 0px;
    min-height: 50vh; /* Ensure minimum height for the image display area */
}

.image-container {
    position: relative;
    display: inline-block;
    max-width: 100%;
    max-height: 100%;
    overflow: visible;
    margin: 0 auto; /* Center the image container */
}

.image-display img {
    max-width: 100%;
    max-height: calc(70vh - 60px); /* Limit image height for better viewing */
    border: 1px solid #dee2e6;
    border-radius: 5px;
    transition: transform 0.3s ease; /* Smooth zoom transition */
    transform-origin: center center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    object-fit: contain; /* Maintain aspect ratio */
}

/* Crop mode indicator */
.crop-mode {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 10;
    display: none;
}

/* Zoom controls */
.zoom-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 8px;
    display: flex;
    gap: 5px;
    z-index: 5;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.zoom-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.zoom-btn:hover {
    background-color: #e9ecef;
    transform: translateY(-1px);
}

.zoom-level {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    font-size: 0.85rem;
    font-weight: 500;
    color: #495057;
}

/* Draggable image when zoomed */
.draggable {
    cursor: grab;
}

.draggable:active {
    cursor: grabbing;
}

.image-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin: 15px 0 !important;
    border-top: 1px solid rgba(0,0,0,0.05);
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.input-label {
    font-size: 1rem;
    margin-bottom: 5px;
    font-weight: 500;
    color: #0d6efd;
}

.progress {
    height: 6px;
    margin-bottom: 10px;
    border-radius: 10px;
    width: 100%;
}

.image-counter {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
    text-align: center;
}

#imageCounter {
    font-weight: 500;
    color: #495057;
}

.completed-badge {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: #198754;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 5;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.save-indicator {
    display: none;
    margin-left: 10px;
    font-size: 0.9rem;
}

.nav-btn {
    min-width: 120px;
    border-radius: 20px;
    transition: all 0.2s ease;
    font-weight: 500;
    padding: 8px 16px;
}

.nav-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.save-all-container {
    margin-top: 10px;
    padding: 10px;
    background-color: transparent;
    border-radius: 10px;
    text-align: center;
}

.d-flex.justify-content-center.align-items-center.mb-2 {
    margin-bottom: 1rem !important;
}

.d-flex.justify-content-center.align-items-center.mb-4 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;        /* space between buttons */
}

.save-all-btn {
    border-radius: 40px;
    padding: 6px 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
    margin-bottom: 0px;
}

.save-all-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.save-all-progress {
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: #6c757d;
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(25, 135, 84, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
    }
}

/* Make the selector more specific to avoid affecting other buttons */
#saveAllBtn:not(:disabled) {
    animation: pulse-green 2s infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

#completionMessage {
    animation: fadeInUp 0.5s ease-out;
    padding: 5px;
    margin-bottom: 5px;
}

/* Form container */
.form-container {
    width: 100%;
    background-color: #fff;
    border-radius: 10px;
    padding: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    border: 1px solid #f0f0f0;
    margin-top: 0px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group .input-label {
    font-size: 1rem;
    margin-bottom: 5px;
    font-weight: 500;
    color: #0d6efd;
    padding-bottom: 3px;
}

/* Zoom indicator and pan hint */
.zoom-indicator {
    position: absolute;
    bottom: 50px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.zoom-indicator.show {
    opacity: 1;
}

/* Add a pan hint that appears when zoomed in */
.pan-hint {
    position: absolute;
    bottom: 50px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 10;
    display: none;
}

/* Button style overrides for template inheritance */
.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
    border-radius: 50px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-primary-1 {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
    border-radius: 4px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-outline-primary {
    color: #0d6efd !important;
    border-color: #0d6efd !important;
    background-color: transparent !important;
    border-radius: 4px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.btn-outline-primary:hover {
    background-color: #0d6efd !important;
    color: white !important;
}

.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
    border-radius: 50px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Fix for image navigation buttons */
.image-navigation {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0px 0 !important;
    padding: 15px 0;
}

/* Form inputs and labels */
.form-label {
    font-weight: 500;
    color: #495057;
    font-size: 1rem;
}

.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    font-size: 1rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 4px;
}

/* Verification related styles */
.verification-label {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 12px 15px;
    margin-bottom: 0px;
    display: flex;
    align-items: center;
    width: 100%;
}

.verification-label .label-text {
    font-weight: 500;
    margin-right: 10px;
    flex-grow: 1;
    font-size: 1.05rem;
}

.verification-label .label-actions {
    display: flex;
    gap: 10px;
}

.verification-label .btn-verify {
    background-color: #28a745;
    color: white;
    border-radius: 4px;
    padding: 6px 15px;
    font-size: 0.95rem;
}

.verification-label .btn-edit {
    background-color: #ffc107;
    color: #212529;
    border-radius: 4px;
    padding: 6px 15px;
    font-size: 0.95rem;
}

.verification-mode-indicator {
    background-color: #17a2b8;
    color: white;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.9rem;
    margin-bottom: 15px;
    display: inline-block;
}

/* Navigation and save buttons */
.image-navigation {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 15px 0 !important;
    padding: 15px 0;
}

.nav-btn {
    min-width: 120px;
    border-radius: 20px;
    transition: all 0.2s ease;
    font-weight: 500;
    padding: 8px 16px;
}

/* Save all button container */
.save-all-container {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: transparent;
    border-radius: 10px;
    text-align: center;
}

.d-flex.justify-content-center.align-items-center.mb-4 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.save-all-btn {
    border-radius: 40px;
    padding: 10px 15px;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
    font-size: 1rem;
}

/* Progress bar */
.progress {
    height: 6px;
    border-radius: 10px;
    width: 100%;
    background-color: rgba(0,0,0,0.05);
}

.progress-bar {
    background-color: #0d6efd;
    border-radius: 10px;
}

/* Image counter display */
.image-counter {
    font-size: 0.95rem;
    color: #6c757d;
    margin: 10px 0;
    text-align: center;
    font-weight: 500;
}

/* Responsive adjustments */
@media (min-width: 1800px) {
    .container.py-4 {
        max-width: 1800px;
    }
    
    .guidelines-sidebar, .shortcuts-sidebar {
        width: 320px; /* Increased from 280px */
    }
    
    .annotation-container {
        max-width: calc(100% - 660px);
    }
}

@media (min-width: 1600px) {
    .guidelines-sidebar, .shortcuts-sidebar {
        width: 290px; /* Increased from 250px */
    }
    
    .annotation-container {
        max-width: calc(100% - 600px);
    }
}

@media (max-width: 1400px) {
    .guidelines-sidebar, .shortcuts-sidebar {
        width: 240px; /* Increased from 200px */
    }
    
    .annotation-container {
        max-width: calc(100% - 500px);
    }
    
    .annotation-layout {
        gap: 15px;
    }
}

@media (max-width: 1200px) {
    .guidelines-sidebar, .shortcuts-sidebar {
        width: 220px; /* Increased from 180px */
    }
    
    .annotation-container {
        max-width: calc(100% - 460px);
    }
    
    .annotation-layout {
        gap: 10px;
    }
}

/* Mobile layout */
@media (max-width: 992px) {
    .annotation-layout {
        flex-direction: column;
        gap: 15px;
    }
    
    .guidelines-sidebar, .shortcuts-sidebar {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .guidelines-card, .shortcuts-sidebar .card {
        width: 100%;
    }
    
    .guidelines-card .card-body,
    .shortcuts-sidebar .card-body {
        max-height: 200px;
    }
    
    .image-display {
        min-height: 40vh;
    }
    
    .image-display img {
        max-height: 50vh;
    }
    
    .nav-btn {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 6px 12px;
    }
    
    .container.py-4 {
        height: auto;
        min-height: calc(100vh - 70px);
    }
    
    .form-container {
        padding: 10px;
    }
    
    .image-navigation {
        padding: 10px 0;
    }
}

/* Ensure proper vertical scrolling on very small screens */
@media (max-height: 700px) {
    .image-display {
        min-height: 40vh;
    }
    
    .image-display img {
        max-height: calc(60vh - 60px);
    }
    
    .guidelines-card .card-body {
        max-height: 180px;
    }
}

/* Spinning animation for loading indicators */
.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}