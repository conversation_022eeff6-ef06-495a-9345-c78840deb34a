:root {
    --corporate-primary: #0056b3;
    --corporate-secondary: #003366;
    --corporate-accent: #4a90e2;
    --corporate-success: #28a745;
    --corporate-warning: #ffc107;
    --corporate-danger: #dc3545;
    --corporate-light: #f8f9fa;
    --corporate-dark: #343a40;
    --corporate-gray: #6c757d;
    --corporate-light-gray: #dee2e6;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --text-color: #495057;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition: all 0.25s ease;
    --card-radius: 12px;
    --button-radius: 4px;
}

body {
    background-color: #f5f7fa;
    color: var(--text-color);
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, sans-serif;
}

.dashboard-header {
    background-color: var(--corporate-light);
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-toggle, .options-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--corporate-primary);
    cursor: pointer;
}

.user-dropdown {
    background: none;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    color: var(--corporate-primary);
    font-weight: 500;
}

.user-icon {
    font-size: 0.9rem;
}

.dashboard-container {
    width: 100%;
    max-width: 80%;
    padding-right: 0;
    margin: 0 auto;
    min-height: 50vh;
}

.dashboard-title {
    color: #1a365d;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 20px 0;
}

/* Welcome Card */
.welcome-card {
    background-color:  #d5e5f7;
    border-radius: var(--card-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    border: 1px solid #e9ecef;
}

.avatar-circle {
    width: 80px;
    height: 80px;
    background-color: #ffcc5c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.initials {
    font-size: 2rem;
    color: #444;
    font-weight: 700;
}

.welcome-card h2 {
    font-size: 1.6rem;
    color: var(--corporate-dark);
    margin-bottom: 8px;
    font-weight: 600;
}

.welcome-subtitle {
    color: var(--corporate-gray);
    margin-bottom: 0;
}

.emoji {
    font-size: 1.5rem;
}

.annotator-tag {
    background-color: #4a90e2;
    color: white;
    padding: 5px 12px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Guidelines Card */
.guidelines-card {
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    height: 100%;
    border: 1px solid #e9ecef;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-link {
    color: var(--corporate-gray);
    border: none;
    padding: 12px 20px;
    font-weight: 500;
    border-radius: 0;
}

.nav-tabs .nav-link.active {
    color: var(--corporate-primary);
    background-color: transparent;
    border-bottom: 2px solid var(--corporate-primary);
}

.tab-content {
    max-height: 300px;
    overflow-y: auto;
}

.guidelines-list {
    padding-left: 1.5rem;
}

.guidelines-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.shortcuts-table {
    margin-bottom: 0;
}

.shortcuts-table td {
    padding: 8px 12px;
    vertical-align: middle;
}

/* Action Cards */
.action-card {
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid #e9ecef;
    height: 100%;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.card-icon {
    font-size: 2.2rem;
    color: var(--corporate-primary);
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--corporate-dark);
    margin-bottom: 10px;
}

.card-text {
    color: var(--corporate-gray);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.action-button {
    background-color: var(--corporate-primary);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 8px 20px;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
    width: 80%;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
}

.col-md-4.mb-4 {
    padding-bottom: 0;
    margin-bottom: 0;
    width: 50%;
}

.action-button:hover {
    background-color: var(--corporate-secondary);
}

.disabled-option {
    opacity: 0.6;
    pointer-events: none;
}

/* Modal */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: none;
    background-color: transparent;
}

.modal-header {
    background: var(--corporate-gradient);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-body {
    background-color: white;
    padding: 20px;
}

.modal-footer {
    background-color: white;
    border-radius: 0 0 12px 12px;
}

.modal-dialog {
    box-shadow: none;
    background-color: transparent;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    color: white;
    opacity: 0.8;
}

.instructions-content {
    white-space: pre-line;
    font-size: 1.4rem;
    text-align: justify;
}

.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: transparent;
    --bs-backdrop-opacity: 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: transparent !important;
    opacity: 0 !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .dashboard-container {
        max-width: 100%;
        padding: 15px;
    }
    
    .avatar-circle {
        margin-bottom: 15px;
    }
    
    .welcome-card h2 {
        font-size: 1.4rem;
    }
}

.info-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    color: var(--corporate-primary);
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 1;
    transition: var(--transition);
}

.info-button:hover {
    transform: scale(1.1);
    color: var(--corporate-secondary);
}

.typewriter-container {
    min-height: 1.8rem;
    margin-top: 5px;
    font-size: 1.1rem;
    color: var(--corporate-primary);
}

.typewriter-text {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    color: var(--corporate-primary);
    font-weight: 500;
}

.hidden {
    display: none;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

@keyframes blink-caret {
    from, to { border-color: transparent }
    50% { border-color: var(--corporate-primary) }
}

.typing-animation {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    animation: 
        typing 2s steps(30, end) forwards;
}

.blinking-cursor {
    border-right: 2px solid var(--corporate-primary);
    animation: blink-caret .75s step-end infinite;
}

body, html {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    height: 100vh;
}

.info-panel {
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #eee;
    border-radius: 8px;
    height: 100%;
    font-size: 0.9rem;
}

.main-panel {
    padding: 10px;
}

.row {
    margin: 0;
    width: 100%;
}

.content-container {
    display: flex;
    flex: 1;
    margin-top: 10px;
    margin-bottom: -15px;
}

.guidelines-container {
    width: 40%;
    padding-right: 15px;
    padding-bottom: 0;
    margin-bottom: 0;   
}

.cards-container {
    width: 60%;
    padding-bottom: 0;
    margin-bottom: 0;
}

.list-group-item {
    padding: 8px 12px;
}

.option-card {
    height: 100%;
}

h4, h6 {
    margin-bottom: 0.5rem;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

.header-section {
    margin-bottom: -10px;
}