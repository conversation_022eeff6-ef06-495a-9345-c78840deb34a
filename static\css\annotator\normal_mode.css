/* Normal Mode Styles */
.normal-mode-title {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

/* Upload Area Styles */
.upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    margin: 1.5rem 0;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.upload-area.drag-over {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-prompt {
    color: #6c757d;
}

.upload-prompt i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #adb5bd;
}

.upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Result Card Styles */
.result-card {
    height: 100%;
}

.processing-indicator {
    text-align: center;
    padding: 2rem;
}

.empty-result-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #adb5bd;
}

.empty-result-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.result-text-area {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

/* Editable text area styling */
.result-text-area[contenteditable="true"] {
    background-color: #fff;
    border: 1px solid #007bff;
    box-shadow: 0 0 0 0.2rem rgba(91, 155, 224, 0.25);
    outline: none;
}

.result-text-area:focus {
    outline: none;
}

/* Edit controls */
.edit-controls {
    margin-top: 0.5rem;
}

/* History Table Styles */
.history-card {
    margin-bottom: 2rem;
}

#historyTable img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
}

.empty-history {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Modal Styles */
.modal-image-container {
    margin-bottom: 1rem;
    text-align: center;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 4px;
}

.modal-text-container {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.modal-text-container[contenteditable="true"] {
    background-color: #fff;
    border: 1px solid #007bff;
}

/* Button groups */
.btn-group .btn {
    margin-left: 0.25rem;
}
.btn-sm.btn-outline-primary{
    margin-left: 0.25rem;
    color: blue;
    border-color: transparent;
    background-color: transparent;
}
.btn-sm.btn-outline-primary:hover{
    color: blue;
    border-color: blue;
    background-color: transparent;
}
.btn-sm.btn-outline-secondary{
    margin-left: 0.25rem;
    color: blue;
    border-color: transparent;
    background-color: transparent;
}


/* Responsive Adjustments */
@media (max-width: 768px) {
    .upload-area {
        min-height: 150px;
    }
    
    .result-text-area {
        min-height: 150px;
    }
} 

.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: #ffffff;
    --bs-backdrop-opacity: 0.5;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: var(--bs-backdrop-bg);
}
 