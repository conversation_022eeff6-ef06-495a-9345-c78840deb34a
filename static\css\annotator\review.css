/* Review Page Styles */
:root {
    --corporate-primary: #0056b3;
    --corporate-secondary: #003366;
    --corporate-accent: #4a90e2;
    --corporate-success: #28a745;
    --corporate-warning: #ffc107;
    --corporate-danger: #dc3545;
    --corporate-light: #f8f9fa;
    --corporate-dark: #343a40;
    --corporate-gray: #6c757d;
    --corporate-light-gray: #dee2e6;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --text-color: #495057;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --transition: all 0.25s ease;
    --card-radius: 6px;
    --button-radius: 4px;
    --header-gradient: linear-gradient(135deg, #f7f9ff, #eef2ff);
    --section-gradient: linear-gradient(135deg, rgba(244, 247, 255, 0.95), rgba(232, 240, 255, 0.9));
    --card-bg: rgba(255, 255, 255, 0.9);
    --heading-color: var(--corporate-secondary);
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
}
 
body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    background-color: #f5f7fa;
    color: var(--text-color);
}
 
.noise-texture {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.03'/%3E%3C/svg%3E");
    opacity: 0.15;
    z-index: -1;
    pointer-events: none;
}
 
.background-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(to right, rgba(0, 86, 179, 0.03) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0, 86, 179, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
    opacity: 0.8;
}
 
/* Sidebar styling */
.position-fixed.start-0.top-0.bottom-0.pt-5 {
    padding-top: 70px !important;
    top: 0 !important;
    z-index: 99;
    width: 250px !important;
}
 
.position-fixed.start-0.top-0.bottom-0.pt-5 .card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 86, 179, 0.1);
    box-shadow: var(--box-shadow);
    border-radius: var(--card-radius);
}
 
.position-fixed.start-0.top-0.bottom-0.pt-5 .card-header {
    background: var(--corporate-gradient) !important;
    color: white !important;
    border-bottom: none;
    padding: 15px 20px;
    border-radius: var(--card-radius) var(--card-radius) 0 0;
}
 
.position-fixed.start-0.top-0.bottom-0.pt-5 .card-header h5 {
    font-weight: 600;
    font-size: 1.2rem;
    margin: 0;
}
 
/* List group styling */
.list-group-item {
    background-color: transparent;
    border-left: none;
    border-right: none;
    border-color: rgba(0, 86, 179, 0.1);
    padding: 12px 15px;
    transition: all 0.2s ease;
}
 
.list-group-item:hover {
    background-color: rgba(0, 86, 179, 0.05);
}
 
.list-group-item.active {
    background-color: rgba(0, 86, 179, 0.1);
    color: var(--corporate-primary);
    border-left: 3px solid var(--corporate-primary);
    font-weight: 500;
}
 
/* Main content area */
.ms-auto.w-100 {
    padding-left: 260px !important;
    transition: padding-left 0.3s ease;
}
 
/* Card styling */
.card {
    border-radius: var(--card-radius);
    border: 1px solid rgba(0, 86, 179, 0.1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    margin-bottom: 20px;
}
 
.card-header {
    /* background: var(--header-gradient); */
    border-bottom: 1px solid rgba(0, 86, 179, 0.1);
    padding: 15px 20px;
}
 
.card-header.bg-primary {
    background: var(--corporate-gradient) !important;
    color: white !important;
    border-bottom: none;
}
 
.card-header.bg-light {
    background: var(--light-color);
    border-bottom: 1px solid rgba(0, 86, 179, 0.1);
}
 
.card-body {
    padding: 15px 20px;
}
 
/* Image container styles */
.image-container {
    position: relative;
    width: 100%;
    height: calc(100vh - 300px);
    background: rgba(248, 249, 250, 0.6);
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}
 
.image-container.grabbing {
    cursor: grabbing;
}
 
.image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transform-origin: center;
    transition: transform 0.1s ease-out;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}
 
/* Zoom controls */
.zoom-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 50px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
}
 
.zoom-level {
    display: inline-block;
    min-width: 60px;
    text-align: center;
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
    margin: 0 5px;
}
 
.zoom-controls .btn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    color: var(--corporate-primary);
    border: 1px solid rgba(0, 86, 179, 0.2);
    font-size: 16px;
    font-weight: bold;
    border-radius: 50%;
}
 
.zoom-controls .btn:hover {
    background: rgba(0, 86, 179, 0.08);
    transform: translateY(-2px);
}
 
/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.4em 0.8em;
    border-radius: 4px;
    text-transform: capitalize;
}
 
.badge.bg-secondary {
    background-color: var(--corporate-gray) !important;
}
 
.badge.bg-success {
    background-color: var(--corporate-success) !important;
}
 
.badge.bg-danger {
    background-color: var(--corporate-danger) !important;
}
 
.badge.bg-light {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: var(--text-primary) !important;
    border: 1px solid rgba(0, 86, 179, 0.1);
}
 
/* Form elements */
.form-control {
    border: 1px solid rgba(0, 86, 179, 0.2);
    border-radius: var(--button-radius);
    padding: 12px 15px;
    transition: var(--transition);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    background-color: rgba(255, 255, 255, 0.9);
}
 
.form-control:focus {
    border-color: var(--corporate-primary);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}
 
.form-label {
    color: var(--corporate-secondary);
    font-weight: 500;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}
 
/* Button styling */
.btn {
    border-radius: var(--button-radius);
    padding: 8px 20px;
    transition: var(--transition);
    font-weight: 500;
}
 
.btn-primary {
    background: var(--corporate-gradient);
    border: none;
    color: white;
    box-shadow: 0 2px 6px rgba(0, 86, 179, 0.2);
}
 
.btn-primary:hover,
.btn-primary:focus {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 86, 179, 0.3);
    background: var(--corporate-gradient-hover);
    border: none;
}
 
.btn-success {
    background: linear-gradient(135deg, #219a52, #27ae60);
    border: none;
    color: white;
    box-shadow: 0 2px 6px rgba(39, 174, 96, 0.2);
}
 
.btn-success:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #1e8a49, #219a52);
    box-shadow: 0 4px 10px rgba(39, 174, 96, 0.3);
}
 
/* Save All button styling */
.btn-warning {
    background: linear-gradient(135deg, #ffc107, #ff9800);
    border: none;
    color: #212529;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
}
 
.btn-warning:hover {
    transform: translateY(-2px);
    background: linear-gradient(135deg, #f7b900, #f08f00);
    box-shadow: 0 4px 10px rgba(255, 193, 7, 0.4);
}
 
/* Pulse animation for Save All button when all docs are ready */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}
 
.btn-warning.pulse {
    animation: pulse 1.5s infinite;
}
 
.btn-outline-primary {
    color: var(--corporate-primary);
    border: 1px solid rgba(0, 86, 179, 0.3);
    background: transparent;
}
 
.btn-outline-primary:hover {
    background: rgba(0, 86, 179, 0.08);
    color: var(--corporate-primary);
}
 
.btn-outline-secondary {
    color: var(--corporate-gray);
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.7);
}
 
.btn-outline-secondary:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--corporate-dark);
}
 
.btn-outline-success {
    color: var(--corporate-success);
    border: 1px solid var(--corporate-success);
    background: transparent;
}
 
.btn-outline-success:hover {
    background: rgba(40, 167, 69, 0.08);
    color: var(--corporate-success);
}
 
.btn-outline-danger {
    color: var(--corporate-danger);
    border: 1px solid var(--corporate-danger);
    background: transparent;
}
 
.btn-outline-danger:hover {
    background: rgba(220, 53, 69, 0.08);
    color: var(--corporate-danger);
}
 
/* Dropdown styling */
.dropdown-menu {
    border-radius: var(--card-radius);
    border: 1px solid rgba(0, 86, 179, 0.1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 8px 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(5px);
}
 
.dropdown-item {
    padding: 8px 20px;
    color: var(--text-primary);
    transition: all 0.2s ease;
}
 
.dropdown-item:hover {
    background: rgba(0, 86, 179, 0.08);
    color: var(--corporate-primary);
}
 
/* Completion message and spinner */
#completionMessage {
    padding: 3rem 2rem;
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 70vh;
    background-color: #f5f7fa;
    z-index: 1050;
    border-radius: var(--card-radius);
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(0, 86, 179, 0.1);
}
 
#completionMessage h3 {
    color: var(--heading-color);
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}
 
#completionMessage p {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    text-align: center;
}
 
#completionMessage .bi-check-circle-fill {
    filter: drop-shadow(0 4px 8px rgba(40, 167, 69, 0.3));
}
 
.spinner-border.text-primary {
    color: var(--corporate-primary) !important;
}
 
/* Centered layout for completion screen */
.centered-completion-layout {
    padding-left: 0 !important;
    width: 100% !important;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}
 
/* Center the card when showing completion message */
.centered-completion-layout .card {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}
 
/* Media queries for responsiveness */
@media (max-width: 991px) {
    .position-fixed.start-0.top-0.bottom-0.pt-5 {
        width: 200px !important;
    }
   
    .ms-auto.w-100 {
        padding-left: 210px !important;
    }
}
 
@media (max-width: 767px) {
    .position-fixed.start-0.top-0.bottom-0.pt-5 {
        width: 100% !important;
        position: relative !important;
        height: auto !important;
        padding-top: 0 !important;
    }
   
    .ms-auto.w-100 {
        padding-left: 15px !important;
        width: 100% !important;
    }
   
    .row {
        flex-direction: column;
    }
   
    .col-md-7, .col-md-5 {
        width: 100%;
    }
}
 
/* Model type badge styling */
.model-type-badge {
    font-size: 0.75rem;
    color: var(--corporate-primary);
    padding: 0.15rem 0;
    margin-top: 0.25rem;
    opacity: 0.8;
}
 
/* Model type colors */
.model-type-badge:has([data-model-type="standard"]) {
    color: #0072C6;
}
 
.model-type-badge:has([data-model-type="enhanced"]) {
    color: #00A36C;
}
 
.model-type-badge:has([data-model-type="premium"]) {
    color: #D4AF37;
}
 
/* Current model type display */
#current-model-type {
    display: block;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}
 
/* Standard badge */
#current-model-type.standard {
    color: #0072C6;
}
 
/* Enhanced badge */
#current-model-type.enhanced {
    color: #00A36C;
}
 
/* Premium badge */
#current-model-type.premium {
    color: #D4AF37;
}
 
/* Datepicker styling */
.datepicker {
    border-radius: var(--card-radius);
    padding: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
 
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
    background: var(--corporate-gradient) !important;
    color: white !important;
    border-radius: 50%;
}
 
.datepicker table tr td.today {
    background-color: rgba(0, 86, 179, 0.1) !important;
    color: var(--corporate-primary) !important;
}
 
.datepicker table tr td,
.datepicker table tr th {
    width: 30px;
    height: 30px;
    border-radius: 50%;
}
 
.input-group-text {
    background: transparent;
    border-color: rgba(0, 86, 179, 0.2);
    color: var(--corporate-primary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}
 
.input-group-text:hover {
    background: rgba(0, 86, 179, 0.08);
}
 
.input-group .form-control {
    border-right: none;
}
 
.input-group .input-group-text {
    border-left: none;
}
 
.input-group:focus-within {
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}
 
.input-group:focus-within .form-control,
.input-group:focus-within .input-group-text {
    border-color: var(--corporate-primary);
}
 
/* Ensure icons are visible in input groups */
.input-group-text i,
.input-group-text .fa,
.input-group-text .fas,
.input-group-text .far,
.input-group-text .fab {
    color: var(--corporate-primary);
    font-size: 16px;
}
 
/* Ensure proper contrast */
.zoom-controls .btn i,
.zoom-controls .btn .fa,
.zoom-controls .btn .fas,
.zoom-controls .btn .far {
    color: var(--corporate-primary);
}
 
/* Additional styles for form editing */
.editable-field {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    padding: 8px;
    transition: all 0.2s ease;
}
.editable-field:hover {
    border-color: #aaa;
    background-color: #fafafa;
}
.editable-field:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.25);
    background-color: #fff;
    outline: none;
}
.form-field-changed {
    background-color: #e8f4fd;
}
.edit-status {
    font-size: 0.8rem;
    color: #2196F3;
    display: none;
    margin-left: 8px;
}
.download-options {
    position: absolute;
    right: 15px;
    bottom: 15px;
    z-index: 10;
}
.form-action-btns {
    display: flex;
    gap: 10px;
}
 
/* Modal styling for Save All confirmation */
.modal-save-all {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}
 
.modal-save-all .modal-content {
    border-radius: var(--card-radius);
    border: none;
    box-shadow: var(--card-shadow);
}
 
.modal-save-all .modal-header {
    background: var(--corporate-gradient);
    color: white;
    border-bottom: none;
    border-radius: var(--card-radius) var(--card-radius) 0 0;
}
 
.modal-save-all .modal-footer {
    border-top: 1px solid rgba(0, 86, 179, 0.1);
}
 
.save-progress {
    height: 10px;
    border-radius: 5px;
    background-color: #f0f0f0;
    margin: 15px 0;
}
 
.save-progress-bar {
    height: 100%;
    border-radius: 5px;
    background: var(--corporate-gradient);
    width: 0%;
    transition: width 0.3s ease;
}
 
/* Modal backdrop light */
.modal-backdrop-light {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(3px);
    z-index: 1040;
}
 
/* Make modal content stand out better against light backdrop */
#saveAllModal .modal-content {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.1);
}
 
/* Ensure modal properly positioned */
#saveAllModal.show {
    z-index: 1050;
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: none;
}
 
/* Style the status messages */
#saveStatus {
    font-size: 0.9rem;
    line-height: 1.4;
}
 
#saveStatus p {
    margin-bottom: 0.4rem;
    padding: 0.3rem 0;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}
 
#saveStatus .text-success {
    color: var(--corporate-success) !important;
}
 
#saveStatus .text-danger {
    color: var(--corporate-danger) !important;
}
 
#saveStatus .alert {
    margin-top: 0.5rem;
    margin-bottom: 0;
}
 