/* Process Data Page Styles */

/* File list styles */
.list-group-item {
    transition: background-color 0.2s;
}

.file-preview img {
    transition: transform 0.2s;
}

.list-group {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

/* Card styles */
.card {
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border-radius: 0.5rem;
    border: 1px solid rgba(0,0,0,0.125);
    background: transparent;
}

.card-header {
    background-color: rgba(0,0,0,0.03);
    border-bottom: 1px solid rgba(0,0,0,0.125);
    padding: 0.75rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* Processing power selector styling */
select[name="model_type"] {
    border-color: #007bff;
}

select[name="model_type"] option[value="standard"] {
    background-color: transparent;
}

select[name="model_type"] option[value="enhanced"] {
    background-color: transparent;
}

select[name="model_type"] option[value="premium"] {
    background-color: transparent;
}

/* Processing power card styles */
.power-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.standard-card {
    border-color: #4285F4;
}

.standard-card .card-title {
    color: #4285F4;
}

.enhanced-card {
    border-color: #34A853;
}

.enhanced-card .card-title {
    color: #34A853;
}

.premium-card {
    border-color: #FBBC05;
}

.premium-card .card-title {
    color: #FBBC05;
}

/* File preview container */
.file-preview {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 4px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
}

/* Progress bar styles */
.progress {
    height: 1.5rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Status message styles */
#statusMessage.text-danger {
    font-weight: bold;
}

/* Button styles - all same color */
.btn {
    border-radius: 50px;
    padding: 8px 20px;
    transition: all 0.3s ease;
    font-weight: 500;
    margin-right: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    background: linear-gradient(135deg, #4568dc, #b06ab3);
    border: none;
    color: white;
}

.btn:last-child {
    margin-right: 0;
}

.btn:hover,
.btn:focus {
    background: linear-gradient(135deg, #3f5ccf, #a259a7);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    color: white;
}

/* Button states */
.btn:disabled,
.btn.disabled {
    background: linear-gradient(135deg, #8e9eab, #eef2f3);
    color: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Button container for alignment */
.button-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

@media (max-width: 576px) {
    .button-container {
        flex-direction: column;
    }
    
    .button-container .btn {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* Corporate-style Process Data Page Styles */

:root {
    --corporate-primary: #0056b3;
    --corporate-secondary: #003366;
    --corporate-accent: #4a90e2;
    --corporate-success: #28a745;
    --corporate-warning: #ffc107;
    --corporate-danger: #dc3545;
    --corporate-light: #f8f9fa;
    --corporate-dark: #343a40;
    --corporate-gray: #6c757d;
    --corporate-light-gray: #dee2e6;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --transition: all 0.25s ease;
    --card-radius: 6px;
    --button-radius: 4px;
}

body {
    background-color: #f5f7fa;
    color: #495057;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

/* Page container */
.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0rem 1rem;
}

/* Page header */
.page-header {
    text-align: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.07);
    background: linear-gradient(to right, rgba(0, 86, 179, 0.02), rgba(0, 86, 179, 0.08), rgba(0, 86, 179, 0.02));
    padding-top: 2rem;
    border-radius: 8px;
}

.page-header h2 {
    color: var(--corporate-secondary);
    font-weight: 800;
    margin-bottom: 0.75rem;
    font-size: 2.5rem;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    display: inline-block;
}

.page-header h2::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background: var(--corporate-gradient);
    margin: 0.5rem auto 0;
    border-radius: 2px;
}

.lead {
    color: var(--corporate-gray);
    font-size: 1.25rem;
    font-weight: 500;
    max-width: 800px;
    margin: 1rem auto 0;
    line-height: 1.5;
}

/* Main card styling */
.main-card {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    background-color: white;
    margin-bottom: 2rem;
}

.main-card .card-header {
    background: white;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
}

.header-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--corporate-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 1rem;
}

.main-card .card-header h4 {
    margin: 0;
    color: var(--corporate-secondary);
    font-weight: 600;
}

.main-card .card-body {
    padding: 1.5rem;
}

/* Source cards styling */
.source-card {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
    background-color: white;
}

.source-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

.source-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--corporate-gradient);
}

.card-icon {
    width: 46px;
    height: 46px;
    border-radius: 8px;
    background: rgba(0, 86, 179, 0.1);
    color: var(--corporate-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-bottom: 1.25rem;
}

.source-card .card-title {
    color: var(--corporate-secondary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.source-card .card-text {
    color: var(--corporate-gray);
    margin-bottom: 1.5rem;
}

/* Form elements styling */
.form-label {
    font-weight: 500;
    color: var(--corporate-secondary);
    margin-bottom: 0.5rem;
}

.custom-select {
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    padding: 0.75rem 1rem;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.custom-select:focus {
    border-color: var(--corporate-primary);
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.file-upload-wrapper {
    position: relative;
    border: 2px dashed rgba(0, 86, 179, 0.3);
    border-radius: 4px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 86, 179, 0.02);
    transition: var(--transition);
}

.file-upload-wrapper:hover {
    border-color: var(--corporate-primary);
    background-color: rgba(0, 86, 179, 0.05);
}

.file-upload-wrapper.files-selected {
    border-color: var(--corporate-success);
    background-color: rgba(40, 167, 69, 0.05);
}

.file-upload-wrapper.files-selected .file-upload-message i {
    color: var(--corporate-success);
}

.custom-file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.file-upload-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--corporate-gray);
    padding: 15px;
    text-align: center;
}

.file-upload-message i {
    font-size: 2rem;
    color: var(--corporate-primary);
    margin-bottom: 0.5rem;
}

/* File count indicator */
.file-count {
    margin-top: 12px;
    text-align: center;
    opacity: 0;
    height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.files-selected .file-count {
    opacity: 1;
    height: auto;
    overflow: visible;
}

.file-count .badge {
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.4rem 1rem;
    border-radius: 50px;
    background: var(--corporate-gradient);
    box-shadow: 0 2px 6px rgba(0, 86, 179, 0.2);
    transition: all 0.25s ease;
    border: none;
    letter-spacing: 0.02em;
    color: white;
    display: inline-flex;
    align-items: center;
}

.file-count .badge::before {
    content: '\f15b';  /* file icon */
    font-family: 'Font Awesome 5 Free';
    margin-right: 8px;
    font-size: 0.85rem;
}

.file-count .badge.bg-success {
    background: linear-gradient(135deg, #219a52, #27ae60);
    box-shadow: 0 2px 6px rgba(39, 174, 96, 0.2);
}

.file-count .badge.bg-success::before {
    content: '\f15c';  /* file check icon */
}

/* Button styling */
.button-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 1.5rem;
    flex-wrap: wrap;
    gap: 10px;
}

.btn-corporate {
    background: var(--corporate-gradient);
    color: white;
    border: none;
    border-radius: var(--button-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(0, 86, 179, 0.2);
    display: inline-flex;
    align-items: center;
    transition: var(--transition);
}

.btn-corporate:hover,
.btn-corporate:focus {
    background: var(--corporate-gradient-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 86, 179, 0.3);
    color: white;
}

.btn-corporate:disabled {
    background: #e9ecef;
    color: #868e96;
    box-shadow: none;
    transform: none;
}

.btn-corporate.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Progress bar styling */
.processing-status {
    margin-top: 1.25rem;
    padding: 1rem;
    background-color: rgba(248, 249, 250, 0.7);
    border-radius: 4px;
    border: 1px solid rgba(0,0,0,0.05);
}

.progress-corporate {
    height: 8px;
    border-radius: 50px;
    background-color: rgba(0,0,0,0.05);
    overflow: hidden;
}

.progress-corporate .progress-bar {
    background: var(--corporate-gradient);
    border-radius: 50px;
}

.status-message {
    font-size: 0.9rem;
    color: var(--corporate-gray);
}

/* Files list styling */
.files-list {
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 4px;
    padding: 1rem;
    background-color: rgba(255,255,255,0.6);
}

.files-header {
    color: var(--corporate-secondary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.custom-list-group {
    max-height: 300px;
    overflow-y: auto;
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
}

.custom-list-group .list-group-item {
    border-left: none;
    border-right: none;
    padding: 0.75rem 1rem;
    transition: background-color 0.2s;
}

.custom-list-group .list-group-item:first-child {
    border-top: none;
}

.custom-list-group .list-group-item:last-child {
    border-bottom: none;
}

.custom-list-group .list-group-item:hover {
    background-color: rgba(0, 86, 179, 0.05);
}

.custom-list-group .list-group-item.active {
    background-color: rgba(0, 86, 179, 0.1);
    color: var(--corporate-primary);
    border-color: rgba(0, 86, 179, 0.2);
}

.selection-buttons {
    justify-content: flex-end;
    margin-top: 1rem;
}

/* Power guide styling */
.power-guide {
    padding: 2rem 1.5rem;
}

.power-card {
    border: none;
    border-radius: var(--card-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: var(--transition);
    overflow: hidden;
    background-color: white;
    height: 100%;
    position: relative;
    cursor: pointer;
}

.power-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
}

/* Selection effect for power cards */
.power-card.selected-power {
    box-shadow: 0 0 0 2px var(--corporate-primary), 0 8px 20px rgba(0,0,0,0.15);
    transform: translateY(-5px);
}

.standard-card.selected-power {
    box-shadow: 0 0 0 2px #0072C6, 0 8px 20px rgba(0,0,0,0.15);
}

.enhanced-card.selected-power {
    box-shadow: 0 0 0 2px #00A36C, 0 8px 20px rgba(0,0,0,0.15);
}

.premium-card.selected-power {
    box-shadow: 0 0 0 2px #D4AF37, 0 8px 20px rgba(0,0,0,0.15);
}

/* Selection confirmation message */
.power-select-confirmation {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    text-align: center;
    font-size: 0.875rem;
    animation: fadeIn 0.3s ease;
}

/* Toggle button styling */
#toggleGuideBtn {
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
}

/* Animation for confirmation message */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Power guide collapse transition */
#powerGuideContent {
    transition: all 0.3s ease;
}

#powerGuideContent.collapsing {
    opacity: 0;
}

#powerGuideContent.show {
    opacity: 1;
}

.power-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.standard-card {
    border-top: 3px solid #0072C6;
}

.standard-card .power-icon {
    background-color: rgba(0, 114, 198, 0.1);
    color: #0072C6;
}

.enhanced-card {
    border-top: 3px solid #00A36C;
}

.enhanced-card .power-icon {
    background-color: rgba(0, 163, 108, 0.1);
    color: #00A36C;
}

.premium-card {
    border-top: 3px solid #D4AF37;
}

.premium-card .power-icon {
    background-color: rgba(212, 175, 55, 0.1);
    color: #D4AF37;
}

.power-card .card-title {
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.standard-card .card-title {
    color: #0072C6;
}

.enhanced-card .card-title {
    color: #00A36C;
}

.premium-card .card-title {
    color: #D4AF37;
}

.power-meter {
    display: flex;
    gap: 4px;
    margin-bottom: 1rem;
}

.power-bar {
    height: 6px;
    flex: 1;
    background-color: rgba(0,0,0,0.05);
    border-radius: 3px;
}

.standard-card .power-bar.active {
    background: linear-gradient(135deg, #0072C6, #0086E8);
}

.enhanced-card .power-bar.active {
    background: linear-gradient(135deg, #00A36C, #00BF80);
}

.premium-card .power-bar.active {
    background: linear-gradient(135deg, #D4AF37, #FFC93D);
}

/* File preview container */
.file-preview {
    width: 50px;
    height: 50px;
    overflow: hidden;
    border-radius: 4px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    margin-right: 12px;
}

.file-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

/* List group item with hover effect */
.list-group-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,0.125);
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 86, 179, 0.05);
}

/* Media queries for responsiveness */
@media (max-width: 991px) {
    .page-container {
        padding: 1.5rem 1rem;
    }
}

@media (max-width: 767px) {
    .button-container {
        justify-content: center;
    }
    
    .power-guide .row > div {
        margin-bottom: 1.5rem;
    }
    
    .power-guide .row > div:last-child {
        margin-bottom: 0;
    }
}

/* Corporate table styles */
.corporate-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.corporate-table thead th {
    background-color: #f8f9fa;
    color: var(--corporate-secondary);
    font-weight: 600;
    text-align: left;
    padding: 0.75rem 1rem;
    border-bottom: 2px solid rgba(0,0,0,0.05);
}

.corporate-table tbody td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    color: var(--corporate-dark);
}

.corporate-table tbody tr:last-child td {
    border-bottom: none;
}

.corporate-table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Badge styles for corporate look */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: 3px;
    text-transform: capitalize;
}

.badge-primary {
    background-color: var(--corporate-primary);
}

.badge-success {
    background-color: var(--corporate-success);
}

.badge-warning {
    background-color: var(--corporate-warning);
    color: #212529;
}

.badge-danger {
    background-color: var(--corporate-danger);
}

/* Box shadow for elements */
.shadow-corporate {
    box-shadow: var(--box-shadow);
}

/* Source options styling */
.source-options {
    margin-top: 2rem;
}

.source-options .source-card {
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-top: 4px solid var(--corporate-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.source-options .source-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.source-options .source-card .card-icon {
    background-color: transparent;
    width: 54px;
    height: 54px;
    border-radius: 0;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--corporate-primary);
    margin-right: 15px;
    flex-shrink: 0;
}

/* New style for icon+title container */
.source-options .source-card .icon-title-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.source-options .source-card .icon-content {
    flex: 1;
}

.source-options .source-card .icon-title-container .card-title {
    margin-bottom: 0.25rem;
    margin-left: 0;
    display: block;
}

.source-options .source-card .icon-title-container .card-text {
    margin-bottom: 0;
    margin-left: 0;
    display: block;
}

/* Adjust card spacing and responsive behavior */
@media (max-width: 767px) {
    .source-options .col-md-6:first-child {
        margin-bottom: 1.5rem;
    }
}

/* Improve button appearance */
.source-options .btn-corporate {
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    width: 100%;
    max-width: none;
    justify-content: center;
    margin-top: 0.5rem;
} 