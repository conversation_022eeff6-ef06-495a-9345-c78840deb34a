/* Header Styling */
.page-header {
    background: linear-gradient(135deg, #4361ee, #4895ef);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: white;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.8);
}

/* Selection Form Card */
.selection-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    border: none;
}

.selection-card.collapsed {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 8px;
    border-color: #e5e7eb;
    padding: 0.625rem 1rem;
    font-size: 0.95rem;
}

.form-select:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Load Tasks Button */
.btn-load-tasks {
    background: linear-gradient(135deg, #4361ee, #4895ef);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-load-tasks:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.btn-load-tasks:disabled {
    background: #e5e7eb;
    cursor: not-allowed;
}

/* Tasks Container */
.tasks-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.tasks-header {
    background: linear-gradient(135deg, #4361ee, #4895ef);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tasks-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.task-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

.tasks-body {
    padding: 1.5rem;
}

/* Loading State */
.loading-state {
    text-align: center;
    padding: 3rem 1.5rem;
}

.loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #4361ee;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
}

.empty-state i {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6b7280;
    margin: 0;
    font-size: 1rem;
}

/* Task Table */
.task-table {
    margin: 0;
}

.task-table th {
    font-weight: 600;
    color: #374151;
    border-bottom-width: 2px;
    background-color: #f8f9fa; /* Light header for table */
}

/* Style overrides for theme consistency */
.selection-card .card-header,
.tasks-container .card-header {
    background: #0052CC !important; /* Solid blue color */
    color: #fff;
    border-bottom: none;
    font-weight: 600;
    padding: 0.75rem 1.25rem;
}

.task-table td {
    vertical-align: middle;
}

/* Image Preview */
.task-image {
    max-width: 100px;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.task-image:hover {
    transform: scale(1.05);
}

/* Toggle Form Button */
.toggle-form-btn {
    background: none;
    border: none;
    color: #4361ee;
    padding: 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    display: none;
}

.toggle-form-btn.visible {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

/* Add toggle form button styling */
#toggleFormBtn {
    display: none;
    margin-top: 10px;
    text-align: center;
}

#toggleFormBtn.visible {
    display: block;
}

/* Collapse Animation */
.selection-card {
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.selection-card.collapsed {
    max-height: 60px;
}

/* Editable Labels */
.editable-label {
    min-height: 100px;
    padding: 8px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
    font-family: monospace;
    white-space: pre-wrap;
    cursor: text;
    width: 100%;
    transition: border-color 0.2s;
}

.editable-label:focus {
    outline: none;
    border-color: #4361ee;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.25);
}

.task-thumbnail {
    max-width: 100%;
    width: 100%;
    height: auto;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease;
    object-fit: contain;
}

.task-thumbnail:hover {
    transform: scale(1.05);
}

/* Image container with zoom controls */
.image-container {
    position: relative;
    width: 100%;
    height: 150px; /* Fixed height */
    overflow: hidden;
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.task-thumbnail {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    border-radius: 6px;
    cursor: pointer;
    transition: transform 0.2s ease;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

/* Status badge styling */
.status-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 2;
}

/* Next Task button styling */
#next-task-btn {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#next-task-btn:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}