/* Variables */
:root {
    --primary-blue: #0052CC;
    --primary-dark: #0747A6;
    --primary-light: #4C9AFF;
    --neutral-100: #ffffff;
    --neutral-200: #f8f9fa;
    --neutral-700: #495057;
    --sidebar-width: 260px;
    --sidebar-collapsed: 70px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --radius-sm: 4px;
    --radius-md: 8px;
}

/* Base Layout */
body {
    min-height: 100vh;
    background-color: #f8f9fa;
    font-family: 'Poppins', sans-serif;
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 2rem;
    transition: all 0.3s ease;
}

body.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed);
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    background-color: var(--primary-blue);
    color: white;
    transition: width 0.3s ease;
    z-index: 1030;
}

body.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.8rem 1rem;
    display: flex;
    align-items: center;
}

.sidebar .nav-link i {
    font-size: 1.25rem;
    width: 35px;
}

.sidebar .nav-link span {
    margin-left: 0.5rem;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--primary-dark);
    color: white;
}

body.sidebar-collapsed .sidebar-brand span,
body.sidebar-collapsed .nav-link span {
    display: none;
}

/* Dashboard Title */
.dashboard-title {
    margin-bottom: 2rem;
    text-align: center;
    padding: 1rem 0;
}

.dashboard-title h4 {
    color: var(--primary-blue);
    font-weight: 800;
    font-size: 3.00rem;
    margin-bottom: 0.5rem;
    letter-spacing: -0.02em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    display: inline-block;
}

.dashboard-title h4::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark));
    margin: 0.5rem auto 0;
    border-radius: 2px;
}

.dashboard-title p {
    font-size: 1rem;
    color: #6c757d;
    margin: 1rem 0 0;
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    border-radius: var(--radius-md);
    background: var(--neutral-100);
}

.card-header {
    background-color: var(--primary-blue);
    color: white;
    border-bottom: none;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    padding: 1rem 2rem;
}

.card-header h5 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

/* Quick Actions */
.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    color: white;
    transform: translateY(-1px);
}

/* Quick Action Cards */
.quick-action-card {
    background: var(--neutral-100);
    border-radius: 8px;
    padding: 2.5rem 1.5rem;
    height: 100%;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background-color: #f8f9ff;
    margin: 0 1.5rem;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-blue);
}

.quick-action-icon {
    background-color: #ffffff;
    color: var(--primary-blue);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.quick-action-icon i {
    font-size: 2.5rem;
}

.quick-action-content {
    text-align: center;
    width: 100%;
}

.quick-action-content h5 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.quick-action-content p {
    color: #6c757d;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
}

.quick-action-content .btn {
    padding: 0.75rem 2rem;
    font-weight: 500;
    border-radius: 25px;
    background-color: var(--primary-blue);
    border: none;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.quick-action-content .btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 82, 204, 0.2);
}

/* Row spacing for quick action cards */
.row.g-4 {
    margin: 2rem -1.5rem;
    gap: 2rem !important;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: var(--sidebar-collapsed);
    }
    
    .main-content {
        margin-left: var(--sidebar-collapsed);
    }
    
    .sidebar-brand span,
    .nav-link span {
        display: none;
    }

    .quick-action-card {
        min-height: 280px;
        padding: 2rem 1rem;
    }

    .quick-action-icon {
        width: 60px;
        height: 60px;
    }

    .quick-action-icon i {
        font-size: 2rem;
    }

    .quick-action-content h5 {
        font-size: 1.25rem;
    }
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(135deg, #4361ee, #235185);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
} 

.dashboard-header h2 {
    margin: 0;
    font-weight: 600;
}

.role-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-left: 5px solid #4361ee;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #4361ee;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

.stats-icon {
    font-size: 2rem;
    color: #4361ee;
    opacity: 0.2;
    position: absolute;
    top: 1rem;
    right: 1rem;
}

/* Action Buttons */
.action-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn-primary {
    background: linear-gradient(135deg, #4361ee, #4895ef);
    border: none;
    color: white;
}

.action-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.action-btn-secondary {
    background: white;
    border: 2px solid #4361ee;
    color: #4361ee;
}

.action-btn-secondary:hover {
    background: #4361ee;
    color: white;
    transform: translateY(-2px);
}

/* Activity Card */
.activity-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.activity-header {
    background: linear-gradient(135deg, #4361ee, #4895ef);
    color: white;
    padding: 1rem 1.5rem;
}

.activity-header h5 {
    margin: 0;
    font-weight: 600;
}

.activity-body {
    padding: 1.5rem;
}

.activity-table th {
    font-weight: 600;
    color: #374151;
}

.activity-empty {
    text-align: center;
    padding: 3rem 1.5rem;
}

.activity-empty i {
    font-size: 3rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}

.activity-empty p {
    color: #6b7280;
    margin: 0;
}

/* Status Badges */
.status-badge {
    padding: 0.35rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

