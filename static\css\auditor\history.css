.status-badge {
    padding: 0.3em 0.6em;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
}
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    position: absolute;
    top: 50%;
    left: 55%;
    transform: translate(-50%, -50%);
}

.empty-state .i {
    font-size: 9rem;
    color: #d1d5db;
    margin-bottom: 1rem;
}


.empty-state .p {
    color: #6b7280;
    margin: 0;
    font-size: 8rem;
}

.search-box {
    position: relative;
    border: 0.5px solid #ced4da; /* Optional: Add a border */
    border-radius: 0.375rem; /* Match form-control border-radius */
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem; /* Match form-control padding */
}

.search-icon {
    position: absolute;
    left: 0.75rem; /* Adjust as needed */
    color: #6c757d; /* Icon color */
}

.search-input {
    border: none;
    outline: none;
    box-shadow: none; /* Remove default focus shadow */
    padding-left: 2rem; /* Make space for the icon */
    flex-grow: 1;
}

.search-input:focus {
    outline: none;
    box-shadow: none;
}

