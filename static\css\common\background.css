/* Common background styles extracted from landing_page.css */

/* Background gradient */
.background-gradient {
  position: fixed;
  inset: 0;
  background: #ffffff;
  z-index: -3;
}

/* Noise texture */
.noise-texture {
  display: none;
}

/* Background grid */
.background-grid {
  display: none;
}

/* Blob effects */
.blob-1,
.blob-2 {
  display: none;
}

@keyframes floatBlob {
  0% { transform: translateY(0px) scale(1); }
  100% { transform: translateY(-50px) scale(1.1); }
} 