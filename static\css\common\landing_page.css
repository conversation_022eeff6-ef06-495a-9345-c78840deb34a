/* Landing Page Styles */
:root {
    --primary-color: #3566c5;
    --primary-gradient: linear-gradient(135deg, #3566c5, #2a539b);
    --secondary-color: #1a3b5d;
    --accent-color: #3566c5;
    --text-color: #333;
    --light-color: #f5f7fa;
    --dark-color: #1a3b5d;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --hero-bg: #f5f7fa;
    --header-gradient: linear-gradient(135deg, #f7f9ff, #e4e9f2);
    --section-gradient: linear-gradient(135deg, rgba(245, 247, 250, 0.95), rgba(228, 233, 242, 0.9));
    --card-bg: rgba(255, 255, 255, 0.9);
    --heading-color: #1a3b5d;
    --text-primary: #1a3b5d;
    --text-secondary: #4a6385;
    --accent-light: #3566c5;
    --accent-gradient: linear-gradient(135deg, #3566c5, #2a539b);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.section-title {
    font-size: 2.8rem;
    text-align: center;
    margin-bottom: 40px;
    color: var(--secondary-color);
    position: relative;
    font-weight: 700;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    margin: 12px auto 0;
    border-radius: 2px;
}

/* Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.03);
}

.main-header.scrolled {
    padding: 12px 0;
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 20px rgba(82, 183, 136, 0.08);
}

.main-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(82, 183, 136, 0.15), transparent);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo img {
    height: 100px;
    width: auto;
    transition: var(--transition);
    margin-right: 25px;
    margin-left: -50px;
    flex-shrink: 0;
}

.logo-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1.3;
}
.logo-text {
  font-size: 1rem;
}
.logo-line-1 {
    white-space: nowrap;
    font-weight: 800;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-family: 'Times New Roman', Times, serif;
}

.logo-line-2 {
  white-space: nowrap;
  font-weight: 800;
  color: var(--text-secondary);
  font-size: 1.2rem;
  font-family: 'Times New Roman', Times, serif;
}

.d2d-highlight {
    font-weight: 700;
    color: var(--primary-color);
}

.beta-tag {
    display: inline;
    color: rgb(31, 31, 32);
    font-size: 0.95rem;
    font-weight: 500;
    font-family: 'Times New Roman', Times, serif;
    padding: 4px 4px;
    border-radius: 5px;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
    background: linear-gradient(to right, #90cf90, #82a5f1);
    width: fit-content;
}

.logo-text .subtitle {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 2px;
}

.logo-text .subtitle:last-of-type {
    font-size: 0.85rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 3px;
}

.logo-text .subtitle:last-of-type::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(69, 104, 220, 0.08);
    border-radius: 4px;
    z-index: -1;
}

.main-header.scrolled .logo img {
    height: 90px; /* Adjust if needed */
}

.main-nav {
    display: flex;
    align-items: center;
    justify-content: space-between; /* Changed from flex-end */
    gap: 25px;
}

.nav-container {
    background: transparent;
    border-radius: 40px;
    padding: 5px 15px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px rgba(69, 104, 220, 0.08);
    position: relative;
    overflow: visible; /* Allow dropdown */
    font-family: 'Times New Roman', Times, serif;
}

.nav-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(69, 104, 220, 0.03), transparent);
    z-index: -1;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
    align-items: center;
}

.nav-links li {
    position: relative; /* Needed for dropdown */
}

.nav-links li a,
.nav-links li .dropdown-toggle {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 10px 12px;
    position: relative;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);
    display: block; /* Ensure block display */
    border: none;
    cursor: pointer;
}

.nav-links li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.nav-links li a:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-links li a:hover::after {
    width: 100%;
}

/* Dropdown Styles */
.dropdown .dropdown-toggle::after {
    display: inline-block;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
}

.dropdown-content {
    display: none; /* Hide by default */
    position: absolute;
    top: 100%;
    left: 0;
    background-color: transparent; /* Solid background */
    min-width: 200px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.1);
    z-index: 1;
    border-radius: 8px;
    padding: 8px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-content {
    display: block; /* Show on hover */
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-content a {
    color: var(--text-primary);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    font-size: 0.95rem;
    font-weight: 500;
}

.dropdown-content a:hover {
    background-color: rgba(69, 104, 220, 0.08);
    color: var(--primary-color);
    transform: none; /* Override parent hover */
}

.dropdown-content a::after {
    display: none; /* No underline effect inside dropdown */
}

.nav-cta {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, rgb(91, 124, 190), rgb(144, 174, 226));
    color: rgb(255, 255, 255);
    border-radius: 50px;
    text-decoration: none;
    font-family: 'Times New Roman', Times, serif;
    font-weight: 900;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.3);
}

.nav-cta:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.4);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    width: 35px;
    height: 35px;
    position: relative;
}

.mobile-menu-toggle span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
    top: 8px; /* Adjusted */
}

.mobile-menu-toggle span:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
    bottom: 8px; /* Adjusted */
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg);
    top: 50%;
    margin-top: -1.5px; /* Center rotated */
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg);
    top: 50%;
    margin-top: -1.5px; /* Center rotated */
}

/* Hero Section */
.hero-section {
    min-height: 80vh;
    background: var(--light-color);
    position: relative;
    overflow: hidden;
    padding: 150px 0 80px 0; /* Adjusted padding */
    margin-top: 0;
    display: flex; /* Center content vertically */
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 70% 30%, rgba(53, 102, 197, 0.06), transparent 60%), 
        radial-gradient(circle at 30% 70%, rgba(42, 83, 155, 0.06), transparent 60%),
        radial-gradient(circle at 90% 90%, rgba(74, 99, 133, 0.04), transparent 40%),
        radial-gradient(circle at 10% 10%, rgba(26, 59, 93, 0.04), transparent 40%);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 150px;
    background: linear-gradient(to top, rgba(245, 247, 250, 0.9), transparent);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

/* Style for the new left content wrapper */
.hero-left-content {
    flex: 1 1 50%;
    min-width: 300px;

}

/* Ensure hero title respects alignment within its container */
.hero-title {
    padding-top:75px;
    font-size: 7.2rem;
    font-weight: 800;
    margin-bottom: 25px;
    line-height: 1.2;
    background: linear-gradient(to right, var(--heading-color), var(--primary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 1px 1px 2px rgba(21, 93, 134, 0.3);  
    text-align: center;
    position: relative;
    display: block;
}


.huai-tagline {
    padding-top: 125px;
    font-size: 2rem;
    color: var(--primary-color); /* Fallback color */
    font-weight: 700;
    letter-spacing: 0.5px;
    position: relative;
    display: flex; /* Use flex for vertical layout */
    flex-direction: column; /* Stack lines vertically */
    align-items: flex-end; /* Align lines to the right */
    min-height: 60px; /* Ensure space for two lines */
    text-transform: uppercase;
    flex-shrink: 0; 
    padding-right: 250px; 
}

.tagline-line {
    display: block; /* Each line takes full width of container */
    /* opacity: 0; */ /* Start hidden - No longer needed, text content controls visibility */
    /* transition: opacity 0.5s ease-in-out; */ /* Smooth fade transition - Removed */
    line-height: 1.2; /* Adjust line height */
    /* Apply gradient to individual lines */
    background: linear-gradient(to right, var(--primary-color), #2a539b);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    /* Add fallback color for browsers that don't support background-clip: text */
    color: var(--primary-color);
}

/* Remove visibility class as it's no longer used */
/* 
.tagline-line.visible {
    opacity: 1; 
}
*/

/* Remove old typewriter styles if they exist and conflict */
.tagline-typing, .tagline-cursor {
    display: none; /* Hide old elements */
}

/* Floating shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.1;
    filter: blur(20px);
}

.shape-1 {
    width: 400px;
    height: 400px;
    top: -100px;
    right: -200px;
    animation: floatAnimation 25s infinite linear;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -50px;
    left: -150px;
    animation: floatAnimation 20s infinite linear reverse;
    background: linear-gradient(135deg, #3566c5, #1a3b5d);
}

.shape-3 {
    width: 200px;
    height: 200px;
    top: 40%;
    right: 20%;
    animation: floatAnimation 15s infinite linear;
    opacity: 0.08;
}

.shape-4 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 25%;
    animation: floatAnimation 18s infinite linear reverse;
    opacity: 0.05;
    background: linear-gradient(135deg, #3566c5, #2a539b);
}

.shape-5 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation: floatAnimation 12s infinite linear;
    opacity: 0.07;
}

@keyframes floatAnimation {
    0% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
        transform: translate(10px, 10px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translate(0, 20px) rotate(10deg) scale(1);
    }
    75% {
        transform: translate(-10px, 10px) rotate(5deg) scale(0.95);
    }
    100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    width: 100%;
}

.btn-primary, .btn-secondary, .btn-sample {
    display: inline-block;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 1px solid transparent; /* Base border */
}

.btn-primary {
    background: linear-gradient(135deg, rgba(69, 104, 220, 0.9), rgba(176, 106, 179, 0.9));
    color: white;
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.15);
    border-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(3px);
}

.btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(53, 102, 197, 0.2);
    background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
    border-color: rgba(69, 104, 220, 0.15);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(3px);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(69, 104, 220, 0.3);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 30px rgba(69, 104, 220, 0.08);
    color: var(--primary-color);
}

.btn-sample {
    background: linear-gradient(135deg, rgba(69, 104, 220, 0.9), rgba(176, 106, 179, 0.9));
    color: white;
    box-shadow: 0 8px 25px rgba(69, 104, 220, 0.15);
    border-color: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(3px);
}

.btn-sample:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.2);
    background: linear-gradient(135deg, rgba(69, 104, 220, 1), rgba(176, 106, 179, 1));
}

/* Features Section */
.features-section {
    background-color: white; /* Changed background */
    position: relative;
    color: var(--text-primary);
    padding: 80px 0;
    margin-top: 150px; /* Added margin to push the section down */
}

.features-section::before {
    content: none; /* Removed gradient overlay */
}

.features-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
}

.features-section .section-title:after {
    background: var(--primary-gradient);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); /* Responsive grid */
    gap: 30px;
    margin-top: 50px;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    position: relative;
    background: var(--light-color); /* Lighter card background */
    border-radius: 12px;
    padding: 30px 25px; /* Adjusted padding */
    transition: var(--transition);
    text-align: center;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid rgba(0,0,0,0.05); /* Subtle border */
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 12px 12px 0 0; /* Match border radius */
    opacity: 0.8;
    transition: height 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px); /* More lift */
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1); /* Stronger shadow */
    background: white;
}

.feature-card:hover::before {
    height: 6px;
}

.feature-icon {
    width: 60px; /* Slightly smaller icon circle */
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px; /* Increased bottom margin */
    position: relative;
    box-shadow: 0 8px 15px rgba(53, 102, 197, 0.25);
}

.feature-icon i {
    font-size: 26px; /* Adjusted icon size */
    color: white;
    position: relative;
    z-index: 2;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon::before {
    opacity: 1;
    transform: scale(1.1);
}

.feature-title {
    font-size: 1.4rem; /* Increased title size */
    font-weight: 600;
    margin-bottom: 15px; /* Increased margin */
    color: var(--heading-color);
    position: relative;
    display: inline-block;
    padding-bottom: 8px;
}

.feature-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 1.5px;
}

.feature-desc {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.6; /* Improved line height */
    margin-top: 8px;
    flex-grow: 1;
    /* Removed flex centering to allow natural text flow */
}

/* About Section */
.about-section {
    background-color: var(--light-color); /* Consistent background */
    position: relative;
    padding: 100px 0 120px;
    color: var(--text-primary);
    overflow: visible; /* Allow potential overflow from cards */
}

.about-section::before {
     content: none; /* Removed gradient overlay */
}

.about-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
    margin-bottom: 20px; /* Reduced margin */
}

.about-section .section-title:after {
    background: var(--primary-gradient);
}

/* Style for the container holding the main about content */
.about-text {
    max-width: 1100px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    background: white; /* White background for the card */
    padding: 50px 60px; /* Adjusted padding */
    border-radius: 20px;
    border: 1px solid rgba(0,0,0,0.07); /* Subtle border */
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.06); /* Adjusted shadow */
    text-align: center; /* Center text within this container */
}

.about-subtitle {
    font-size: 1.5rem; /* Adjusted size */
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--primary-color);
    text-align: center;
    background: none; /* Removed gradient */
    -webkit-background-clip: unset;
    background-clip: unset;
    -webkit-text-fill-color: unset;
    text-shadow: none; /* Removed shadow */
    position: relative;
}

.about-text > p {
    margin-bottom: 40px; /* Spacing */
    font-size: 1.15rem;
    line-height: 1.8;
    color: var(--text-secondary); /* Softer text color */
    text-shadow: none; /* Removed shadow */
    font-weight: 400; /* Regular weight */
    text-align: center;
    max-width: 800px; /* Constrain paragraph width */
    margin-left: auto;
    margin-right: auto;
}

/* Expertise and Compliance Sections Styling */
.expertise-section, 
.compliance-section {
    margin-top: 50px; /* Increased spacing */
    text-align: center;
}

.expertise-section h4,
.compliance-section h4 {
    font-size: 1.6rem; /* Adjusted size */
    font-weight: 600;
    margin-bottom: 30px; /* Increased spacing */
    color: var(--heading-color);
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
    text-shadow: none; /* Removed shadow */
}

.expertise-section h4::after,
.compliance-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%; /* Center underline */
    transform: translateX(-50%);
    width: 50px; /* Wider underline */
    height: 3px;
    background: var(--primary-gradient); /* Use primary gradient */
    border-radius: 1.5px;
}

.expertise-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Responsive grid */
    gap: 30px;
    margin-top: 20px;
}

.expertise-card {
    background: var(--light-color); /* Match section background */
    border-radius: 15px;
    padding: 30px; /* Adjusted padding */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06); /* Softer shadow */
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-align: left; /* Align text left */
}

.expertise-card::before {
    content: none; /* Removed top border gradient */
}

.expertise-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(53, 102, 197, 0.12); /* Primary color shadow on hover */
    background: white;
}

.expertise-card h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--primary-color); /* Primary color for title */
    text-shadow: none;
}

.expertise-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 0;
    text-shadow: none;
}

.compliance-section p {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-secondary);
    font-weight: 400;
    text-shadow: none;
    font-size: 1.1rem;
}

.compliance-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    justify-content: center;
}

.badge {
    background: var(--primary-gradient);
    color: white;
    font-weight: 500; /* Adjusted weight */
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 0.9rem; /* Slightly smaller */
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.2);
    transition: var(--transition);
}

.badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(53, 102, 197, 0.3);
    background: linear-gradient(135deg, #2a539b, #3566c5); /* Darker gradient on hover */
}

/* Contact Section */
.contact-section {
    background: linear-gradient(to bottom, #f7f9ff, #edf1ff);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(69, 104, 220, 0.1);
}

.contact-section::before {
     content: none; /* Removed gradient overlay */
}

.contact-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
    margin-bottom: 40px; /* Reduced margin */
}

.contact-section .section-title:after {
    background: var(--primary-gradient);
}

/* Social Buttons specific styling */
.contact-social {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
}

.social-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 50px;
    background: white; /* White background */
    backdrop-filter: none; /* Removed blur */
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.social-btn i {
    margin-right: 8px;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.social-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    background: var(--light-color);
}

.website-btn i { color: var(--primary-color); }
.linkedin-btn i { color: #0077b5; }
.twitter-btn i { color: #1da1f2; }

.website-btn:hover { border-color: var(--primary-color); color: var(--primary-color); }
.linkedin-btn:hover { border-color: #0077b5; color: #0077b5; }
.twitter-btn:hover { border-color: #1da1f2; color: #1da1f2; }

/* Contact Info Grid */
.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px; /* Reduced gap */
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 20px;
}

.contact-item {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07); /* Adjusted shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(69, 104, 220, 0.1);
    display: flex;
    align-items: flex-start;
    height: 100%;
}

.contact-item:hover {
    transform: translateY(-8px); /* Increased lift */
    box-shadow: 0 15px 40px rgba(53, 102, 197, 0.15); /* Primary color shadow */
}

.contact-icon {
    background: var(--primary-gradient);
    width: 55px; /* Slightly larger */
    height: 55px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.25); /* Adjusted shadow */
}

.contact-icon i {
    font-size: 22px; /* Adjusted size */
    color: white;
}

.contact-details {
    flex: 1;
}

.contact-details h3 {
    font-size: 1.3rem; /* Adjusted size */
    margin-bottom: 12px; /* Adjusted spacing */
    color: var(--heading-color);
    font-weight: 600;
}

.contact-details p {
    margin-bottom: 8px; /* Reduced spacing */
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1rem; /* Adjusted size */
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
    font-weight: 500;
}

.contact-details a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.phone-numbers {
    display: flex;
    flex-direction: column;
    gap: 10px; /* Spacing between numbers */
}

.phone-group {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Align left */
}

.country {
    font-size: 0.85rem; /* Smaller country label */
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 3px;
    display: inline-block;
    background: rgba(69, 104, 220, 0.06); /* Lighter background */
    padding: 3px 8px;
    border-radius: 4px;
}

.phone-group p {
    margin-bottom: 0; /* Remove extra margin */
}

/* Footer */
.main-footer {
    background-color: var(--dark-color);
    color: rgba(255, 255, 255, 0.8); /* Slightly transparent white */
    padding: 30px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
}

.main-footer p {
    font-size: 0.9rem;
}






.background-gradient { /* Ensure gradient is at the very back */
    position: fixed;
    inset: 0;
    background: linear-gradient(135deg, #f5f7fa, #e4e9f2);
    z-index: -3;
    display: none; /* Hide the gradient */
}

/* Animations (Keep existing) */
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
@keyframes fadeInDown { from { opacity: 0; transform: translateY(-30px); } to { opacity: 1; transform: translateY(0); } }
@keyframes fadeInUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
@keyframes pulseOpacity { 0% { opacity: 0.3; } 50% { opacity: 0.8; } 100% { opacity: 0.3; } }
@keyframes blink { 0%, 100% { opacity: 1; } 50% { opacity: 0; } }

/* Responsive adjustments */
@media (max-width: 992px) {
    .container {
        padding: 0 20px;
    }
    .hero-title {
        font-size: 3.2rem;
    }
    .section-title {
        font-size: 2.4rem;
    }
    .main-nav { /* Stack nav items below toggle */
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.98); /* Background for mobile menu */
        backdrop-filter: blur(5px);
        flex-direction: column;
        align-items: stretch;
        padding: 10px 0;
        box-shadow: 0 10px 20px rgba(0,0,0,0.05);
        border-top: 1px solid rgba(0,0,0,0.05);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.5s ease-out;
    }
    .main-nav.show { /* Class added by JS */
        max-height: 500px; /* Or large enough height */
    }
    .nav-container { /* Remove container styles for mobile */
        background: none;
        border: none;
        box-shadow: none;
        padding: 0;
        width: 100%;
    }
    .nav-links {
        flex-direction: column;
        align-items: center; /* Center links */
        width: 100%;
        gap: 0;
    }
    .nav-links li {
        width: 100%;
        text-align: center;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }
    .nav-links li:last-child {
        border-bottom: none;
    }
    .nav-links a, .nav-links .dropdown-toggle {
        padding: 15px 20px;
        width: 100%;
    }
    .nav-links a:hover {
        transform: none;
        background-color: rgba(69, 104, 220, 0.05);
    }
    .nav-links a::after {
        display: none; /* No underline on mobile */
    }
    .dropdown-content {
        position: static; /* Static position */
        box-shadow: none;
        border: none;
        border-radius: 0;
        background: rgba(69, 104, 220, 0.03);
        width: 100%;
        max-height: 0;
        overflow: hidden;
        opacity: 1;
        visibility: visible;
        transform: none;
        transition: max-height 0.4s ease-out;
        padding: 0;
    }
    .dropdown:hover .dropdown-content {
        /* Control visibility via JS/class toggle recommended */
        /* For pure CSS hover (less ideal for touch): */
        max-height: 300px; 
    }
     .dropdown-content a {
        padding: 12px 20px;
        text-align: center;
    }
    .nav-cta {
        margin: 15px auto; /* Center button */
        display: block; /* Make block */
        width: fit-content; /* Adjust width */
    }
    .mobile-menu-toggle {
        display: block; /* Show toggle */
        z-index: 1001; /* Above header */
    }
    .hero-content {
        justify-content: center;
        text-align: center;
    }
    .hero-left-content {
        flex-basis: 100%;
        text-align: center;
    }
    .hero-title {
        text-align: center;
    }
    .hero-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    .cta-buttons {
        justify-content: center;
    }
    .huai-tagline {
        align-items: center; /* Center lines when centered */
        flex-basis: 100%; 
        margin-top: 20px; 
        padding-right: 0; /* Remove padding when centered */
    }
}

@media (max-width: 768px) {
    section {
        padding: 80px 0;
    }
    .hero-section {
        padding: 120px 0 60px 0;
        min-height: auto;
    }
    .hero-title {
        font-size: 2.8rem;
    }
    .hero-title::after {
        left: 0;
        transform: none;
    }
    .huai-tagline {
        text-align: left;
        font-size: 1.4rem;
    }
    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    .btn-primary, .btn-secondary, .btn-sample {
        width: 80%;
        max-width: 300px;
    }
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    .about-text {
        padding: 40px 30px;
    }
    .expertise-cards {
        grid-template-columns: 1fr;
    }
    .contact-info {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }
    .section-title {
        font-size: 2rem;
    }
    .btn-primary, .btn-secondary, .btn-sample {
        width: 90%;
        padding: 14px 28px;
        font-size: 1rem;
    }
    .feature-card {
        padding: 25px 20px;
    }
    .feature-title {
        font-size: 1.2rem;
    }
    .about-text {
        padding: 30px 20px;
    }
    .about-text > p {
        font-size: 1rem;
    }
    .expertise-section h4, .compliance-section h4 {
        font-size: 1.4rem;
    }
     .contact-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    .contact-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
    .contact-details {
        width: 100%;
    }
    .phone-group {
        align-items: center;
    }
}

/* Custom Scrollbar Styles (WebKit Browsers) */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: #e4e9f2;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
    border: 2px solid #e4e9f2; 
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2a539b, #3566c5);
}

/* Basic Firefox Scrollbar Styles (Optional - Limited Customization) */
/* Uncomment if you want basic color styling for Firefox */
/* 
html {
    scrollbar-width: thin; 
    scrollbar-color: var(--primary-color) #e4e9f2;
/* } 
*/

/* New styles for animated taglines */
.tagline-animation-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 0;
}

.tagline-column {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.08);; /* Light background color */
    border-color: rgba(69, 104, 220, 0.15);
    border-radius: 80px; /* Rounded corners for pill shape */
    padding: 15px 200px; /* Keep large horizontal padding */
    margin-bottom: 0; /* No space needed between columns now */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Subtle shadow */
    width: auto; /* Let width adapt to content + padding */
    min-width: 300px; /* Minimum width */
}

/* Specific styles for the left tagline container */
.tagline-left {
    background-color: rgba(202, 218, 240, 0.918); /* Same blue shade or customize */
    /* Add any unique styles for the left container here */
}

/* Specific styles for the right tagline container */
.tagline-right {
    background-color:  rgba(202, 218, 240, 0.918);; /* Light green shade, different from left */
    /* Add any unique styles for the right container here */
    padding: 15px 200px;
    margin-left: 200px;
}

.tagline-column span {
    display: inline-block; /* Needed for consistent height/transition */
    font-size: 1.5rem; /* Adjust size as needed */
    font-weight: 900;
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--primary-color); /* Use primary color - blue */
    opacity: 1; /* Start visible */
    transition: opacity 0.5s ease-in-out; /* Smooth fade transition (0.5s) */
    min-height: 1.5em; /* Reserve space to prevent layout shifts */
    vertical-align: top;
    white-space: nowrap; /* Prevent text from wrapping */
    text-align: center; /* Ensure text itself is centered */
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 900;
    font-family: 'Times New Roman', Times, serif;
    background: transparent;
    color: var(--primary-color);
    padding-left: 10px;
    padding-top: 0px;
    padding-bottom: 15px;
    text-align: center;
}

/* Button Section Redesign */
.button-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #f7f9ff, #e4e9f2);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.button-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 70% 30%, rgba(53, 102, 197, 0.04), transparent 60%), 
        radial-gradient(circle at 30% 70%, rgba(42, 83, 155, 0.04), transparent 60%);
    z-index: 1;
}

.pillar-buttons {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
}

.pillar-btn {
    background: transparent;
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
    padding: 18px 40px;
    border-radius: 12px;
    min-width: 250px;
    box-shadow: 0 8px 20px rgba(53, 102, 197, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(53, 102, 197, 0.1);
    position: relative;
    overflow: hidden;
    text-align: left;
    display: flex;
    align-items: center;
    border-left: 4px solid var(--primary-color);
}

.pillar-btn::after {
    position: absolute;
    right: 30px;
    opacity: 0;
    transition: all 0.3s ease;
}

.pillar-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(53, 102, 197, 0.15);
    color: var(--primary-color);
    border-left-width: 8px;
}

.pillar-btn:hover::after {
    opacity: 1;
    right: 20px;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
}

.action-btn {
    padding: 12px 25px;
    border-radius: 50px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.btn-primary-action {
  background: linear-gradient(135deg, rgb(91, 124, 190), rgb(144, 174, 226));
  color: rgb(255, 255, 255);
  font-weight: 500;
  font-family: 'Times New Roman', Times, serif;
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(53, 102, 197, 0.2);
}

.btn-secondary-action {
  background: linear-gradient(135deg, rgb(91, 124, 190), rgb(144, 174, 226));
  color: rgb(255, 255, 255);
  font-weight: 500;
  font-family: 'Times New Roman', Times, serif;
  font-size: 1.2rem;
  border: 1px solid rgba(53, 102, 197, 0.15);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.btn-tertiary-action {
    background: linear-gradient(135deg, rgb(91, 124, 190), rgb(144, 174, 226));
    color: rgb(255, 255, 255);
    font-weight: 500;
    font-family: 'Times New Roman', Times, serif;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.1);
}
.huai-agent {
  font-size: 90%;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Times New Roman', Times, serif;
}
.huai-agent-1 {
  font-size: 110%;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-family: 'Times New Roman', Times, serif;
}
.huai-agent .smaller {
  font-size: 70%;
  vertical-align: baseline;
  font-family: 'Times New Roman', Times, serif;
}
.action-btn:hover {
    transform: translateY(-3px);
}

.btn-primary-action:hover {
    background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
    box-shadow: 0 8px 20px rgba(53, 102, 197, 0.3);
}

.btn-secondary-action:hover {
  background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
  box-shadow: 0 8px 20px rgba(53, 102, 197, 0.3);
}

.btn-tertiary-action:hover {
  background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
  box-shadow: 0 8px 20px rgba(53, 102, 197, 0.3);
}

@media (max-width: 768px) {
    .pillar-buttons {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }
    
    .pillar-btn {
        width: 80%;
        max-width: 300px;
    }
    
    .action-buttons {
        gap: 10px;
    }
    
    .action-btn {
        padding: 10px 20px;
        font-size: 0.95rem;
    }
}

/* Adjustments for the tagline-column when combined with pillar-btn */
.tagline-column.pillar-btn {
    background: transparent;
    border-radius: 30px;
    border: 4px solid rgba(7, 7, 7, 0.1);
    box-shadow: 0 8px 20px rgba(15, 56, 136, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    padding: 15px 30px;
    margin: 15px;
    width: auto;
    min-width: 200px;
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: transparent;
    border-left-color: var(--primary-color);
    animation: border-traverse 4s linear infinite;
}

.tagline-column.pillar-btn:hover::after {
    opacity: 1;
    right: 15px;
}

.tagline-column.pillar-btn span {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Reset the tagline-animation-container to work with the new styles */
.tagline-animation-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 0;
}

@media (max-width: 768px) {
    .tagline-animation-container {
        flex-direction: column;
        align-items: center;
    }
    
    .tagline-column.pillar-btn {
        width: 80%;
        max-width: 300px;
        margin: 10px 0;
    }
}

/* Add the keyframe animation */
@keyframes border-traverse {
    0% {
        border-left-color: var(--primary-color);
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: transparent;
    }
    25% {
        border-left-color: transparent;
        border-top-color: var(--primary-color);
        border-right-color: transparent;
        border-bottom-color: transparent;
    }
    50% {
        border-left-color: transparent;
        border-top-color: transparent;
        border-right-color: var(--primary-color);
        border-bottom-color: transparent;
    }
    75% {
        border-left-color: transparent;
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: var(--primary-color);
    }
    100% {
        border-left-color: var(--primary-color);
        border-top-color: transparent;
        border-right-color: transparent;
        border-bottom-color: transparent;
    }
}
