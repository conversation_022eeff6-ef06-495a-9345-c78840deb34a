/* Modern login page styles */
:root {
    --primary-color: #0056b3;
    --secondary-color: #003366;
    --accent-color: #4a90e2;
    --text-light: #E5E5E5;
    --text-white: #FFFFFF;
    --text-color: #495057;
    --bg-color: #ffffff;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #dee2e6;
    --input-focus: var(--accent-color);
    --error-color: #dc3545;
    --success-color: #28a745;
    --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.25s ease;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --card-radius: 8px;
    --button-radius: 4px;
}

[data-theme="dark"] {
    --text-color: #e2e8f0;
    --bg-color: #1a202c;
    --card-bg: #2d3748;
    --input-bg: #2d3748;
    --input-border: #4a5568;
}

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap');

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: auto;
    color: var(--text-color);
    transition: all 0.3s ease;
    background-color: var(--bg-color);
}

/* Remove any background textures or patterns */
.background-grid,
.noise-texture,
.floating-shapes,
.shape {
    display: none !important;
}

/* Animated light background */
.animated-light-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(-45deg, rgba(0, 86, 179, 0.15), rgba(0, 52, 102, 0.15), rgba(74, 144, 226, 0.15), rgba(0, 68, 148, 0.15));
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
    pointer-events: none;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Light particles animation */
.light-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.light-particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(135deg, transparent 95%, rgba(0, 86, 179, 0.15) 95%),
        linear-gradient(45deg, transparent 95%, rgba(0, 86, 179, 0.1) 95%),
        linear-gradient(0deg, transparent 97%, rgba(74, 144, 226, 0.1) 97%),
        linear-gradient(90deg, transparent 97%, rgba(74, 144, 226, 0.08) 97%);
    background-size: 100px 100px, 80px 80px, 60px 60px, 120px 120px;
    opacity: 0.25;
}

@keyframes line-shift {
    0% {
        background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px;
    }
    100% {
        background-position: 100px 100px, 80px 80px, 60px 0px, 0px 120px;
    }
}

/* Ensure container takes full height and centers content */
.container.mt-5 {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0 !important;
    padding: 1rem;
    background-color: var(--bg-color);
}

/* Row and column adjustments */
.row.justify-content-center {
    width: 100%;
    margin-top: -3rem; /* Add negative margin to pull card up */
}

/* Hide the navbar on login page */
.navbar.navbar-expand-lg.navbar-dark.bg-primary,
body > .navbar {
    display: none !important;
}

/* Hide the footer on login page */
body > footer {
    display: none !important;
}

/* Card indicator line at top */
.card-indicator {
    height: 4px;
    background: var(--corporate-gradient);
    width: 100%;
    border-top-left-radius: var(--card-radius);
    border-top-right-radius: var(--card-radius);
}

.login-card {
    background-color: var(--card-bg);
    border-radius: var(--card-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: var(--transition-smooth);
    border: none;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    top: 10%;
}

.login-card:hover {
    box-shadow: var(--shadow-md);
    transform: translate(-50%, calc(-50% - 5px));
}

/* Header container with 2-column layout */
.header-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    width: 100%;
    padding: 1.5rem 1rem 0.5rem;
}

.logo-column {
    flex: 0 0 40%;
    text-align: right;
    padding-right: 15px;
}

.title-column {
    flex: 0 0 60%;
    text-align: left;
    padding-left: 15px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.title-column p {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-top: 5px;
}

.login-logo {
    max-width: 70px;
    height: auto;
    margin: 0;
    transform: scale(1);
    transition: transform 0.3s ease;
}

.login-logo:hover {
    transform: scale(1.05);
}

/* Title styling with gradient */
.login-title {
    font-family: 'Poppins', 'Segoe UI', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    margin: 0;
    text-align: left;
}

/* Title link styling */
.title-link {
    background: var(--corporate-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    text-decoration: none;
    transition: var(--transition-smooth);
}

.title-link:hover {
    filter: brightness(1.2);
    text-decoration: none;
}

/* Subtitle styling */
.subtitle {
    font-size: 1rem;
    color: var(--accent-color);
    margin: 0 0 1.5rem 0;
    font-weight: 400;
    text-align: center;
}

/* Sign In text styling */
.signin-text {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 1.5rem 0 0.5rem;
    text-align: center;
    font-family: 'Poppins', 'Segoe UI', sans-serif;
}

/* Sign In underline */
.signin-underline {
    height: 3px;
    width: 60px;
    background: var(--accent-color);
    margin: 0 auto 1.5rem;
    border-radius: 3px;
}

/* Input field styling */
.form-group {
    margin-bottom: 1.5rem;
}

.input-label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

/* Input field container styling */
.input-field, .password-field {
    position: relative;
    border: 1px solid rgba(0, 86, 179, 0.2);
    border-radius: var(--button-radius);
    transition: var(--transition-smooth);
}

.input-field:focus-within, .password-field:focus-within {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 0.25rem rgba(0, 86, 179, 0.1);
    transform: translateY(-2px);
}

/* Override form-control border inside input containers */
.input-field .form-control, .password-field .form-control {
    border: none;
    border-radius: var(--button-radius);
}

.form-control {
    background-color: var(--input-bg);
    color: var(--text-color);
    transition: var(--transition-smooth);
    padding: 0.85rem 1.2rem;
    width: 100%;
    font-size: 1rem;
}

.form-control:focus {
    box-shadow: none;
    outline: none;
}

.form-control::placeholder {
    color: #a0aec0;
}

/* Password field with toggle */
.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--accent-color);
    z-index: 10;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--secondary-color);
}

/* Styled login button with gradient */
.btn-login {
    display: block;
    width: 100%;
    color: var(--text-white);
    transition: var(--transition-smooth);
    border-radius: var(--button-radius);
    padding: 12px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    background: var(--corporate-gradient);
    border: none;
    cursor: pointer;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
    border-radius: 2rem;
    padding: 0.5rem 1.5rem;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 86, 179, 0.3);
    background: var(--corporate-gradient-hover);
}

.btn-login:active {
    transform: translateY(-1px);
}

/* Theme switch slider */
.theme-switch {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background: var(--corporate-gradient);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider-icons {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 7px;
    box-sizing: border-box;
    color: var(--text-white);
    font-size: 12px;
}

.spinner-border {
    display: none;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

.invalid-feedback {
    display: none;
    font-size: 0.875rem;
    color: var(--error-color);
    margin-top: 0.25rem;
}

.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-control.is-invalid {
    border-color: var(--error-color);
    background-image: none;
}

/* Flash message styles */
#loginFlashMessage {
    transition: opacity 0.5s ease;
    border-left: 4px solid var(--accent-color);
    background-color: rgba(0, 86, 179, 0.1);
    color: var(--text-color);
    border-radius: var(--button-radius);
    margin-top: 1rem;
    padding: 0.75rem 1rem;
}

[data-theme="dark"] #loginFlashMessage {
    background-color: rgba(0, 86, 179, 0.2);
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

#flashMessageText {
    animation: pulse 2s infinite;
    display: inline-block;
}

/* Create account button */
.create-account-btn {
    text-decoration: none;
    display: block;
    width: 100%;
    color: var(--accent-color);
    transition: var(--transition-smooth);
    border-radius: var(--button-radius);
    padding: 8px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    background: transparent;
    border: 1px solid var(--accent-color);
    cursor: pointer;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
    letter-spacing: 0.5px;
    border-radius: 2rem;
    padding: 0.4rem 1.5rem;
}

.create-account-btn:hover {
    color: var(--text-white);
    background: var(--corporate-gradient);
    border-color: transparent;
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
}

/* Divider with OR text */
.divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 1.5rem 0;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid var(--input-border);
}

.divider-text {
    padding: 0 1rem;
    font-size: 0.875rem;
    color: #718096;
}

[data-theme="dark"] .form-control {
    border-color: #4a5568;
    background-color: #3a4756;
}

[data-theme="dark"] .form-control:focus {
    background-color: rgba(45, 55, 72, 0.6);
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.25rem rgba(74, 144, 226, 0.2);
}

/* Dark mode for animated backgrounds */
[data-theme="dark"] .animated-light-background {
    background: linear-gradient(-45deg, rgba(74, 144, 226, 0.15), rgba(45, 55, 72, 0.15), rgba(30, 40, 55, 0.15), rgba(20, 30, 45, 0.15));
}

[data-theme="dark"] .light-particles::before {
    background-image: 
        linear-gradient(135deg, transparent 95%, rgba(74, 144, 226, 0.15) 95%),
        linear-gradient(45deg, transparent 95%, rgba(60, 120, 215, 0.1) 95%),
        linear-gradient(0deg, transparent 97%, rgba(80, 140, 235, 0.1) 97%),
        linear-gradient(90deg, transparent 97%, rgba(100, 160, 255, 0.08) 97%);
    opacity: 0.15;
}

/* Form group input fields */
.form-group input.form-control {
    border-radius: var(--button-radius);
}

.back-arrow {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 10;
    color: #6c757d;
    transition: color 0.3s ease;
    font-size: 1.2rem;
}

.back-arrow:hover {
    color: var(--primary-color);
}

/* Add responsive adjustments */
@media (max-width: 576px) {
    .login-card {
        max-width: 90%;
        width: 90%;
    }
    
    .header-container {
        flex-direction: column;
        align-items: center;
    }
    
    .logo-column, .title-column {
        flex: 0 0 100%;
        text-align: center;
        padding: 0;
    }
    
    .title-column {
        align-items: center;
        margin-top: 1rem;
    }
    
    .login-title {
        font-size: 1.75rem;
        text-align: center;
    }
}