html, body {
    margin: 0;
    padding: 0;
    height: 100%;
}

.feature-nav {
    background-color: #E5E5E5;
    padding: 12px 0;
    font-family: 'Poppins', sans-serif;
    z-index: 999;
    position: relative;
    margin-top: 0%;
    /* Ensuring no margin is added */
    top: 0; /* Ensure the navbar stays at the top */
}

.feature-nav .container {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
}

.feature-nav-item {
    position: relative;
    padding: 10px 16px;
    color: #141010;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.3s ease;
    text-align: center;
}

.feature-nav-item span:hover {
    color: #0dcaf0;
}

.feature-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    min-width: 220px;
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 10px 0;
    animation: fadeIn 0.3s ease-in-out;
    text-align: left;
}

.feature-dropdown a {
    display: block;
    padding: 10px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    transition: background 0.2s ease;
}

.feature-dropdown a:hover {
    background-color: #f1f1f1;
}

.feature-nav-item:hover .feature-dropdown {
    display: block;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .feature-nav .container {
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }

    .feature-nav-item {
        width: 100%;
        padding: 12px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .feature-dropdown {
        position: static;
        transform: none;
        box-shadow: none;
        margin-top: 8px;
        background-color: #f9f9f9;
        border: 1px solid #ddd;
    }

    .feature-dropdown a {
        font-size: 15px;
        padding: 8px 12px;
    }

    .feature-nav-item:hover .feature-dropdown {
        display: block;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
