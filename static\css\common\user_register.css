/* Modern registration page styles */
:root {
    --primary-color: #0056b3;
    --secondary-color: #003366;
    --accent-color: #4a90e2;
    --text-light: #E5E5E5;
    --text-white: #FFFFFF;
    --text-color: #495057;
    --bg-color: #ffffff;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --input-border: #dee2e6;
    --input-focus: var(--accent-color);
    --error-color: #dc3545;
    --success-color: #28a745;
    --shadow-sm: 0 4px 8px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition-smooth: all 0.25s ease;
    --corporate-gradient: linear-gradient(135deg, #0056b3, #004494);
    --corporate-gradient-hover: linear-gradient(135deg, #004494, #003366);
    --card-radius: 8px;
    --button-radius: 4px;
}

[data-theme="dark"] {
    --text-color: #e2e8f0;
    --bg-color: #1a202c;
    --card-bg: #2d3748;
    --input-bg: #2d3748;
    --input-border: #4a5568;
}

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap');

/* Hide the navbar and footer on register page */
.navbar.navbar-expand-lg.navbar-dark.bg-primary,
body.auth-page .navbar,
body > footer,
body.auth-page footer {
    display: none !important;
}

body {
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: auto;
    color: var(--text-color);
    transition: all 0.3s ease;
    background-color: var(--bg-color);
}

/* Remove any background textures or patterns */
.background-grid,
.noise-texture,
.floating-shapes {
    display: none !important;
}

/* Animated light background */
.animated-light-background {
    position: fixed;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: linear-gradient(-45deg, rgba(0, 86, 179, 0.15), rgba(0, 52, 102, 0.15), rgba(74, 144, 226, 0.15), rgba(0, 68, 148, 0.15));
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
    pointer-events: none;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Light particles animation */
.light-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.light-particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(135deg, transparent 95%, rgba(0, 86, 179, 0.15) 95%),
        linear-gradient(45deg, transparent 95%, rgba(0, 86, 179, 0.1) 95%),
        linear-gradient(0deg, transparent 97%, rgba(74, 144, 226, 0.1) 97%),
        linear-gradient(90deg, transparent 97%, rgba(74, 144, 226, 0.08) 97%);
    background-size: 100px 100px, 80px 80px, 60px 60px, 120px 120px;
    opacity: 0.25;
}

.auth-container {
    min-height: 100vh;
    padding: 2rem 1rem;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-color);
}

.auth-card {
    background-color: var(--card-bg);
    border-radius: var(--card-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: var(--transition-smooth);
    border: none;
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    position: relative;
    top: 5%;
    z-index: 1;
}

.auth-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.card-indicator {
    height: 4px;
    background: var(--corporate-gradient);
    width: 100%;
    border-top-left-radius: var(--card-radius);
    border-top-right-radius: var(--card-radius);
}

.card-indicator.bottom {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: var(--card-radius);
    border-bottom-right-radius: var(--card-radius);
    background: var(--corporate-gradient);
    position: relative;
    overflow: hidden;
}

.card-indicator.bottom::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.auth-header {
    background: var(--corporate-gradient);
    padding: 1.5rem 2rem;
    color: white;
    position: relative;
}

.auth-header h1 {
    font-family: 'Poppins', 'Segoe UI', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-align: center; 
}

.back-to-login {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}

.back-to-login:hover {
    color: white;
    border-bottom-color: white;
}

/* Button row styles */
.btn-outline-secondary {
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
    background-color: transparent;
    transition: var(--transition-smooth);
    font-weight: 500;
    justify-content: center;
    border-radius: var(--button-radius);
}

.btn-outline-secondary:hover {
    background-color: rgba(74, 144, 226, 0.1);
    color: var(--accent-color);
    border-color: var(--accent-color);
    transform: translateY(-2px);
}

.auth-body {
    padding: 2rem;
}

.form-floating {
    margin-bottom: 1.25rem;
}

.form-floating > .form-control,
.form-select {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--text-color);
    transition: var(--transition-smooth);
    border-radius: var(--button-radius);
    padding: 0.85rem 0.75rem;
    height: calc(3.5rem + 2px);
}

.form-floating > .form-control:focus,
.form-select:focus {
    border-color: var(--input-focus);
    box-shadow: 0 0 0 0.25rem rgba(0, 86, 179, 0.1);
    outline: none;
    transform: translateY(-2px);
}

.form-floating > label {
    padding: 1rem 0.75rem;
    color: #6c757d;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--button-radius);
    font-weight: 500;
    transition: var(--transition-smooth);
    border-radius: 2rem;
}

.btn-primary {
    background: var(--corporate-gradient);
    border: none;
    color: var(--text-white);
    font-weight: 500;
    font-size: 1.2rem;
    box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
    justify-content: center;
    border-radius: 2rem;
}

.btn-primary:hover {
    background: var(--corporate-gradient-hover);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 86, 179, 0.3);
}

.invalid-feedback {
    font-size: 0.875rem;
    margin-top: 0.25rem;
    color: var(--error-color);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.is-invalid {
    animation: shake 0.2s ease-in-out 0s 2;
    border-color: var(--error-color) !important;
}

/* Form control with dark theme support */
.theme-switch {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
}
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background: var(--corporate-gradient);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider-icons {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 7px;
    box-sizing: border-box;
    color: var(--text-white);
    font-size: 12px;
}

.spinner-border {
    display: none;
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

[data-theme="dark"] .form-control {
    border-color: #000000;
    background-color: #3a4756;
}

[data-theme="dark"] .form-control:focus {
    border-color: var(--input-focus);
    background-color: #3a4756;
}

/* Dark mode for animated backgrounds */
[data-theme="dark"] .animated-light-background {
    background: linear-gradient(-45deg, rgba(74, 144, 226, 0.15), rgba(45, 55, 72, 0.15), rgba(30, 40, 55, 0.15), rgba(20, 30, 45, 0.15));
}

[data-theme="dark"] .light-particles::before {
    background-image: 
        linear-gradient(135deg, transparent 95%, rgba(74, 144, 226, 0.15) 95%),
        linear-gradient(45deg, transparent 95%, rgba(60, 120, 215, 0.1) 95%),
        linear-gradient(0deg, transparent 97%, rgba(80, 140, 235, 0.1) 97%),
        linear-gradient(90deg, transparent 97%, rgba(100, 160, 255, 0.08) 97%);
    opacity: 0.15;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .auth-card {
        max-width: 90%;
        width: 90%;
    }
}