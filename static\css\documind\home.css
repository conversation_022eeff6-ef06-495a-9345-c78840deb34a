/* Landing Page Styles */
:root {
    --primary-color: #3566c5;
    --primary-gradient: linear-gradient(135deg, #3566c5, #2a539b);
    --secondary-color: #1a3b5d;
    --accent-color: #3566c5;
    --text-color: #333;
    --light-color: #f5f7fa;
    --dark-color: #1a3b5d;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --hero-bg: #f5f7fa;
    --header-gradient: linear-gradient(135deg, #f7f9ff, #e4e9f2);
    --section-gradient: linear-gradient(135deg, rgba(245, 247, 250, 0.95), rgba(228, 233, 242, 0.9));
    --card-bg: rgba(255, 255, 255, 0.9);
    --heading-color: #1a3b5d;
    --text-primary: #1a3b5d;
    --text-secondary: #4a6385;
    --accent-light: #3566c5;
    --accent-gradient: linear-gradient(135deg, #3566c5, #2a539b);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    overflow-x: hidden;
}

.container {
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 40px;
    position: relative;
    z-index: 2;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.section-title {
    font-size: 2.8rem;
    text-align: center;
    margin-bottom: 40px;
    color: var(--secondary-color);
    position: relative;
    font-weight: 700;
}

.section-title:after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    margin: 12px auto 0;
    border-radius: 2px;
}

/* Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.03);
}

.main-header.scrolled {
    padding: 12px 0;
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 20px rgba(82, 183, 136, 0.08);
}

.main-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(82, 183, 136, 0.15), transparent);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo img {
    height: 60px;
    width: auto;
    transition: var(--transition);
    margin-right: 15px;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.3;
}



.logo-text .subtitle {
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 2px;
}

.logo-text .subtitle:last-of-type {
    font-size: 0.85rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 3px;
}

.logo-text .subtitle:last-of-type::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(69, 104, 220, 0.08);
    border-radius: 4px;
    z-index: -1;
}

.main-header.scrolled .logo img {
    height: 70px;
}

.main-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 25px;
}

.nav-container {
    background-color: rgba(255, 255, 255, 0.08);
    border-radius: 50px;
    padding: 5px 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px rgba(69, 104, 220, 0.08);
    position: relative;
    overflow: hidden;
}

.nav-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(69, 104, 220, 0.03), transparent);
    z-index: -1;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
    align-items: center;
}

.nav-links li a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.05rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 10px 12px;
    position: relative;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);
}

.nav-links li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width 0.3s ease;
    border-radius: 1px;
}

.nav-links li a:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-links li a:hover::after {
    width: 100%;
}

.nav-cta {
    display: inline-block;
    padding: 12px 30px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.05rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.3);
}

.nav-cta:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.4);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    width: 35px;
    height: 35px;
    position: relative;
}

.mobile-menu-toggle span {
    display: block;
    width: 100%;
    height: 3px;
    background: var(--text-primary);
    position: absolute;
    left: 0;
    transition: var(--transition);
}

.mobile-menu-toggle span:nth-child(1) {
    top: 0;
}

.mobile-menu-toggle span:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
}

.mobile-menu-toggle span:nth-child(3) {
    bottom: 0;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg);
    top: 50%;
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg);
    top: 50%;
}

/* Hero Section */
.hero-section {
    min-height: 80vh;
    background: linear-gradient(135deg, #f5f7fa, #e4e9f2);
    position: relative;
    overflow: hidden;
    padding: 100px 0 50px 0;
    margin-top: 0;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 70% 30%, rgba(53, 102, 197, 0.06), transparent 60%), 
        radial-gradient(circle at 30% 70%, rgba(42, 83, 155, 0.06), transparent 60%),
        radial-gradient(circle at 90% 90%, rgba(74, 99, 133, 0.04), transparent 40%),
        radial-gradient(circle at 10% 10%, rgba(26, 59, 93, 0.04), transparent 40%);
    z-index: 1;
}

.hero-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 150px;
    background: linear-gradient(to top, rgba(245, 249, 255, 0.8), transparent);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    padding: 20px 0;
}

.hero-main-content {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    gap: 60px;
    margin-top: 40px;
}
.hero-main-content .hero-main-content-p {
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.6;
    text-align: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 0px;
    line-height: 1.2;
    background: linear-gradient(to right, var(--heading-color), var(--primary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 5px 30px rgba(0, 0, 0, 0.03);
    text-align: left;
    position: relative;
    display: inline-block;
}
.hero-title-2 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0px;
    line-height: 1.2;
    text-align: left;
}   

.hero-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Updated Tagline and Hero Content Styling */
.tagline-container {
    width: 60%;
    padding: 0;
    max-width: 100%;
}

.tagline-row {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    margin-bottom: 30px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.05);
    flex-wrap: nowrap;
    width: 100%;
}

.tagline-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px 15px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.1);
    animation: fadeIn 0.8s ease forwards;
    position: relative;
    overflow: hidden;
    flex: 1;
    min-width: 0;
    transition: all 0.3s ease;
    width: 33.33%;
    height: 100%;
    min-height: 150px;
}

.tagline-column > div {
    width: 100%;
}

.tagline-column:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(69, 104, 220, 0.18);
    background: rgba(255, 255, 255, 0.4);
}

.tagline-column:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
}

.tagline-column:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
    z-index: -1;
}

.tagline-column:nth-child(1) {
    animation-delay: 0.2s;
}

.tagline-column:nth-child(3) {
    animation-delay: 0.4s;
}

.tagline-divider {
    width: 1px;
    background: rgba(69, 104, 220, 0.2);
    margin: 0 5px;
    position: relative;
    overflow: hidden;
    animation: pulseOpacity 2s infinite;
}

.tagline-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 30px;
    background: var(--primary-gradient);
    opacity: 0.7;
    transform: translateY(-50%);
    border-radius: 3px;
}

.hero-tagline {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--primary-color);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    white-space: nowrap;
}

.hero-subtagline {
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 0 5px;
    line-height: 1.4;
}

/* Typing Animation Container */
.typing-container {
    margin: 50px auto 0;
    text-align: center;
    max-width: 850px;
    position: relative;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(3px);
    border-radius: 50px;
    padding: 20px 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    margin-bottom: 40px;
}

.typing-animation {
    display: inline-block;
    font-size: 1.5rem;
    color: var(--primary-color);
    font-weight: 500;
    font-family: var(--font-mono);
    letter-spacing: 0.5px;
    opacity: 1;
    position: relative;
    background: linear-gradient(to right, var(--primary-color), #2a539b);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.typing-message {
    display: inline-block;
    transition: opacity 0.4s ease-in-out;
}

.typing-animation::after {
    content: '|';
    position: absolute;
    right: -15px;
    top: 0px;
    color: var(--primary-color);
    font-weight: 600;
    animation: blink 1.5s infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03);
        background: rgba(255, 255, 255, 0.08);
    }
    50% {
        box-shadow: 0 10px 30px rgba(69, 104, 220, 0.08);
        background: rgba(255, 255, 255, 0.1);
    }
    100% {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.03);
        background: rgba(255, 255, 255, 0.08);
    }
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* Processing steps styling */
.processing-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    font-size: 1.1rem;
    color: var(--text-primary);
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
    animation-delay: 1.2s;
    flex-wrap: wrap;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 30px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    max-width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.processing-step {
    padding: 8px 18px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.processing-step:hover {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(69, 104, 220, 0.15);
    color: var(--primary-color);
}

.processing-step:nth-child(1) {
    animation-delay: 1.4s;
}

.processing-step:nth-child(3) {
    animation-delay: 1.8s;
}

.processing-step:nth-child(5) {
    animation-delay: 2.2s;
}

.step-separator {
    margin: 0 10px;
    color: var(--primary-color);
    font-weight: 700;
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 1.6s;
}

.step-separator:nth-child(4) {
    animation-delay: 2.0s;
}

/* Hero Description */
.hero-description {
    width: 100%;
    font-size: 1.3rem;
    line-height: 1.7;
    padding: 40px;
    /*background: rgba(255, 255, 255, 0.08);*/
    background: transparent;
    backdrop-filter: blur(3px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    /*box-shadow: 0 15px 35px rgba(69, 104, 220, 0.05);*/
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-self: stretch;
    min-height: 300px;
}

.hero-description p {
    color: var(--text-primary);
    margin-bottom: 40px;
    font-weight: 500;
    font-size: 1.3rem;
    line-height: 1.7;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

.process-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0px;
    gap: 25px;
    flex-wrap: wrap;
}

.process-step {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(3px);
    border-radius: 15px;
    padding: 25px;
    flex: 1;
    min-width: 180px;
    box-shadow: 0 10px 30px rgba(69, 104, 220, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.process-step:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, rgba(69, 104, 220, 0.5), rgba(176, 106, 179, 0.5));
    opacity: 0.6;
}

.process-step:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.08));
    z-index: -1;
}

.process-step:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.1);
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.15);
}

.process-step h3 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    background: linear-gradient(to right, var(--primary-color), #2a539b);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.process-step p {
    color: var(--text-primary);
    font-size: 1.05rem;
    margin-bottom: 0;
    line-height: 1.5;
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

/* Floating shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.1;
    filter: blur(20px);
}

.shape-1 {
    width: 400px;
    height: 400px;
    top: -100px;
    right: -200px;
    animation: floatAnimation 25s infinite linear;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -50px;
    left: -150px;
    animation: floatAnimation 20s infinite linear reverse;
    background: linear-gradient(135deg, #3566c5, #1a3b5d);
}

.shape-3 {
    width: 200px;
    height: 200px;
    top: 40%;
    right: 20%;
    animation: floatAnimation 15s infinite linear;
    opacity: 0.08;
}

.shape-4 {
    width: 150px;
    height: 150px;
    bottom: 30%;
    left: 25%;
    animation: floatAnimation 18s infinite linear reverse;
    opacity: 0.05;
    background: linear-gradient(135deg, #3566c5, #2a539b);
}

.shape-5 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation: floatAnimation 12s infinite linear;
    opacity: 0.07;
}

@keyframes floatAnimation {
    0% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
        transform: translate(10px, 10px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translate(0, 20px) rotate(10deg) scale(1);
    }
    75% {
        transform: translate(-10px, 10px) rotate(5deg) scale(0.95);
    }
    100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
}

.cta-buttons {
    display: flex;
    gap: 20px;
    margin-top: auto;
    justify-content: center;
}

.btn-primary, .btn-secondary, .btn-sample {
    display: inline-block;
    padding: 16px 32px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-primary {
    background: linear-gradient(135deg, rgba(53, 102, 197, 0.9), rgba(42, 83, 155, 0.9));
    color: white;
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(3px);
}

.btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(53, 102, 197, 0.2);
    background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.08);
    color: var(--text-primary);
    border: 1px solid rgba(69, 104, 220, 0.15);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
    backdrop-filter: blur(3px);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(69, 104, 220, 0.3);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 10px 30px rgba(69, 104, 220, 0.08);
    color: var(--primary-color);
}

.btn-sample {
    background: linear-gradient(135deg, rgba(69, 104, 220, 0.9), rgba(176, 106, 179, 0.9));
    color: white;
    box-shadow: 0 8px 25px rgba(69, 104, 220, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(3px);
}

.btn-sample:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.2);
    background: linear-gradient(135deg, rgba(69, 104, 220, 1), rgba(176, 106, 179, 1));
}

@media (max-width: 768px) {
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .btn-primary, .btn-secondary, .btn-sample {
        width: 100%;
    }
}

/* Features Section */
.features-section {
    background-color: var(--hero-bg);
    position: relative;
    color: var(--text-primary);
    padding: 80px 0;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--section-gradient);
    z-index: 0;
}

.features-section::after {
    content: '';
    position: absolute;
    bottom: -150px;
    right: -150px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(rgba(66, 133, 244, 0.2), transparent);
    z-index: 0;
}

.features-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
}

.features-section .section-title:after {
    background: var(--primary-gradient);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    position: relative;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 25px 20px;
    transition: var(--transition);
    text-align: center;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 260px;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px 2px 0 0;
    opacity: 0.8;
    transition: height 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: white;
}

.feature-card:hover::before {
    height: 8px;
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    position: relative;
    box-shadow: 0 8px 15px rgba(53, 102, 197, 0.3);
}

.feature-icon i {
    font-size: 28px;
    color: white;
    position: relative;
    z-index: 2;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    z-index: 1;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon::before {
    opacity: 1;
    transform: scale(1.1);
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--heading-color);
    position: relative;
    display: inline-block;
    padding-bottom: 8px;
}

.feature-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 1.5px;
}

.feature-desc {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
    margin-top: 8px;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (min-width: 1600px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .feature-card {
        aspect-ratio: auto;
    }
}

@media (max-width: 700px) {
    .features-grid {
        grid-template-columns: 1fr;
        max-width: 500px;
    }
}

/* About Section */
.about-section {
    background-color: var(--hero-bg);
    position: relative;
    padding: 100px 0 120px;
    text-align: center;
    color: var(--text-primary);
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(135deg, #f0f4ff, #e8eeff),
        radial-gradient(circle at 20% 30%, rgba(82, 183, 136, 0.07) 0%, transparent 35%),
        radial-gradient(circle at 80% 70%, rgba(69, 104, 220, 0.05) 0%, transparent 35%);
    z-index: 0;
}

.about-section .shape {
    opacity: 0.07;
    filter: blur(30px);
}

.about-section .shape-2 {
    background: linear-gradient(135deg, #52b788, #74c69d);
}

.about-section .shape-4 {
    background: linear-gradient(135deg, #40916c, #52b788);
    opacity: 0.05;
}

.about-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
    margin-bottom: 50px;
}

.about-section .section-title:after {
    background: var(--primary-gradient);
}

.about-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 150px;
    background: linear-gradient(to top, rgba(245, 249, 255, 0.8), transparent);
    z-index: 1;
}

.about-text {
    max-width: 1100px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(3px);
    padding: 50px;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.05);
    text-align: left;
}

.about-text::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.05));
    z-index: -1;
}

.about-text > p {
    margin-bottom: 25px;
    font-size: 1.15rem;
    line-height: 1.8;
    color: var(--text-primary);
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
    font-weight: 500;
}

/* About Section Enhancements */
.about-subtitle {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: var(--primary-color);
    text-align: center;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
    position: relative;
}

.expertise-section, 
.compliance-section {
    margin-top: 40px;
}

.expertise-section h4,
.compliance-section h4 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--heading-color);
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
}

.expertise-section h4::after,
.compliance-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(135deg, #52b788, #74c69d);
    border-radius: 1.5px;
}

.expertise-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 20px;
}

.expertise-card {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(3px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(69, 104, 220, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.expertise-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #52b788, #74c69d);
    opacity: 0.8;
}

.expertise-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(69, 104, 220, 0.1);
    background: rgba(255, 255, 255, 0.12);
}

.expertise-card h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--heading-color);
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

.expertise-card p {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    margin-bottom: 0;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3);
}

.compliance-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    justify-content: center;
}

.badge {
    background: linear-gradient(135deg, #52b788, #74c69d);
    color: white;
    font-weight: 600;
    padding: 8px 18px;
    border-radius: 50px;
    font-size: 1rem;
    box-shadow: 0 5px 15px rgba(82, 183, 136, 0.2);
    transition: var(--transition);
}

.badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(82, 183, 136, 0.3);
    background: linear-gradient(135deg, #40916c, #52b788);
}

.compliance-section p {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
    .expertise-cards {
        grid-template-columns: 1fr;
    }
    
    .expertise-section h4,
    .compliance-section h4 {
        display: block;
        text-align: center;
    }
    
    .expertise-section h4::after,
    .compliance-section h4::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .badge {
        font-size: 0.9rem;
        padding: 6px 14px;
    }
}

/* Contact Section */
.contact-section {
    background: linear-gradient(to bottom, #f7f9ff, #edf1ff);
    padding: 100px 0;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(69, 104, 220, 0.1);
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--section-gradient);
    z-index: 0;
}

.contact-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
    margin-bottom: 60px;
}

.contact-section .section-title:after {
    background: var(--primary-gradient);
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 20px;
}

.contact-item {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(69, 104, 220, 0.1);
    display: flex;
    align-items: flex-start;
    height: 100%;
}

.contact-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(69, 104, 220, 0.15);
}

.contact-icon {
    background: var(--primary-gradient);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(69, 104, 220, 0.2);
}

.contact-icon i {
    font-size: 24px;
    color: white;
}

.contact-details {
    flex: 1;
}

.contact-details h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--heading-color);
    font-weight: 600;
}

.contact-details p {
    margin-bottom: 10px;
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 1.05rem;
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
    font-weight: 500;
}

.contact-details a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.phone-numbers {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.phone-group {
    display: flex;
    flex-direction: column;
}

.country {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 5px;
    display: inline-block;
    background: rgba(69, 104, 220, 0.08);
    padding: 3px 10px;
    border-radius: 4px;
}

/* Footer */
.main-footer {
    background-color: var(--dark-color);
    color: white;
    padding: 30px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.main-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulseOpacity {
    0% { opacity: 0.3; }
    50% { opacity: 0.8; }
    100% { opacity: 0.3; }
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .hero-main-content {
        gap: 30px;
    }
    
    .hero-title {
        text-align: center;
        font-size: 3.5rem;
    }
    
    .hero-description {
        min-height: auto;
    }
}

@media (max-width: 992px) {
    .hero-title {
        font-size: 3.2rem;
    }
    
    .huai-tagline {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
        font-size: 1.5rem;
    }
    
    .process-steps {
        gap: 15px;
    }
    
    .process-step {
        padding: 20px 15px;
    }
    
    .process-step h3 {
        font-size: 1.4rem;
    }
    
    .cta-buttons {
        justify-content: center;
    }
    
    .typing-container {
        max-width: 90%;
        padding: 18px 30px;
    }
    
    .typing-animation {
        font-size: 1.4rem;
    }
}

@media (max-width: 768px) {
    .hero-section {
        min-height: auto;
        padding: 100px 0 60px 0;
    }
    
    .hero-title {
        font-size: 2.8rem;
        text-align: center;
        display: block;
        margin-left: auto;
        margin-right: auto;
    }
    
    .huai-tagline {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
        font-size: 1.3rem;
        margin-bottom: 15px;
    }
    
    .hero-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .hero-description {
        padding: 25px;
    }
    
    .process-steps {
        flex-direction: column;
        gap: 15px;
    }
    
    .process-step {
        width: 100%;
        padding: 20px;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .btn-primary, .btn-secondary, .btn-sample {
        width: 100%;
    }
    
    .typing-container {
        max-width: 95%;
        padding: 15px 25px;
        height: auto;
        min-height: 70px;
        margin-top: 30px;
    }
    
    .typing-animation {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 90px 0 40px 0;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .huai-agent {
        letter-spacing: 0.5px;
    }
    
    .huai-tagline {
        font-size: 1.1rem;
    }
    
    .hero-description {
        padding: 20px;
    }
    
    .process-step {
        padding: 15px;
    }
    
    .process-step h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
    }
    
    .process-step p {
        font-size: 0.95rem;
    }
    
    .hero-description p {
        font-size: 1rem;
        margin-bottom: 25px;
    }
    
    .typing-container {
        padding: 12px 20px;
        height: auto;
        min-height: 80px;
    }
    
    .typing-animation {
        font-size: 1.1rem;
    }
}

/* Section connectors */
.features-section::before,
.about-section::before,
.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--section-gradient);
    z-index: 0;
}

.features-section::after,
.about-section::after,
.contact-section::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 3px;
    height: 100px;
    background: linear-gradient(to bottom, rgba(66, 133, 244, 0.3), transparent);
    z-index: 1;
}

.contact-section::after {
    display: none;
}

/* Floating background elements for all sections */
.features-section .floating-bg,
.about-section .floating-bg,
.contact-section .floating-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
        left: 0;
    overflow: hidden;
    z-index: 1;
}

.floating-shape {
    position: absolute;
    opacity: 0.1;
    border-radius: 50%;
}

.features-shape-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(var(--primary-color), transparent);
    top: 20%;
    right: 10%;
    animation: floatingShape 25s infinite alternate;
}

.features-shape-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(#34a0a4, transparent);
    bottom: 10%;
    left: 15%;
    animation: floatingShape 18s infinite alternate-reverse;
}

.about-shape-1 {
    width: 180px;
    height: 180px;
    background: radial-gradient(#5e60ce, transparent);
    top: 15%;
    left: 10%;
    animation: floatingShape 20s infinite alternate-reverse;
}

.about-shape-2 {
    width: 120px;
    height: 120px;
    background: radial-gradient(var(--accent-color), transparent);
    bottom: 20%;
    right: 15%;
    animation: floatingShape 22s infinite alternate;
}

.contact-shape-1 {
    width: 160px;
    height: 160px;
    background: radial-gradient(var(--primary-color), transparent);
    top: 25%;
    right: 20%;
    animation: floatingShape 19s infinite alternate-reverse;
}

.contact-shape-2 {
    width: 130px;
    height: 130px;
    background: radial-gradient(#34a0a4, transparent);
    bottom: 15%;
    left: 20%;
    animation: floatingShape 24s infinite alternate;
}

/* Add more subtle background elements */
.background-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(69, 104, 220, 0.015) 1px, transparent 1px),
        linear-gradient(90deg, rgba(69, 104, 220, 0.015) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: 1;
    opacity: 0.8;
}

.background-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
        width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 15% 15%, rgba(69, 104, 220, 0.03) 0%, transparent 25%),
        radial-gradient(circle at 85% 85%, rgba(176, 106, 179, 0.03) 0%, transparent 25%);
    z-index: -1;
}

.typing-prefix {
    display: none;
}

.typing-text {
    color: var(--primary-color);
    font-weight: 600;
    min-width: 180px;
    display: inline-block;
}

.typing-cursor {
    display: inline-block;
    color: var(--primary-color);
    font-weight: 600;
    animation: blink 1s infinite;
    margin-left: 5px;
}

.huai-agent {
    font-size: 85%;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.smaller {
    font-size: 75%;
    vertical-align: baseline;
    padding-right: 0px;
}

.huai-tagline {
    font-size: 1.7rem;
    color: var(--primary-color);
    font-weight: 700;
    margin: 0 0 20px 0;
    text-align: left;
    letter-spacing: 0.5px;
    background: linear-gradient(to right, var(--primary-color), #2a539b);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    display: inline-block;
    min-height: 30px;
    min-width: 250px;
    text-transform: uppercase;
}

.tagline-typing {
    display: inline-block;
    background: linear-gradient(to right, var(--primary-color), #2a539b);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.tagline-typing .smaller {
    font-size: 75%;
    font-weight: 700;
    vertical-align: baseline;
    text-transform: uppercase;
}

.tagline-cursor {
    display: inline-block;
    color: var(--primary-color);
    -webkit-text-fill-color: var(--primary-color);
    font-weight: 700;
    animation: blink 1s infinite;
    margin-left: 1px;
    position: relative;
    vertical-align: baseline;
}

@media (max-width: 768px) {
    .huai-tagline {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
        min-width: 280px;
        font-size: 1.4rem;
    }
    
    .tagline-typing .smaller {
        font-size: 70%;
    }
}

#tagline-first, #tagline-second {
    transition: opacity 0.5s ease;
    display: inline-block;
}



@keyframes subtlePulse {
    0%, 100% {
        opacity: 0.1;
    }
    50% {
        opacity: 0.2;
    }
}


/* Feature card hover enhancement */
.feature-card:hover .feature-title {
    transform: translateY(-3px);
}

.feature-card:hover .feature-icon {
    transform: scale(1.05);
}

.contact-social {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
    position: relative;
    z-index: 2;
    flex-wrap: wrap;
}

.social-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(5px);
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.social-btn i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.website-btn {
    color: var(--primary-color);
    border-color: rgba(69, 104, 220, 0.2);
}

.logo-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1;
}
.logo-text {
  font-size: 1rem;
}
.logo-line-1 {
    white-space: nowrap;
    font-weight: 800;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-family: 'Times New Roman', Times, serif;
}

.logo-line-2 {
  white-space: nowrap;
  font-weight: 800;
  color: var(--text-secondary);
  font-size: 1.2rem;
  font-family: 'Times New Roman', Times, serif;
}


.beta-tag {
    display: inline;
    color: rgb(31, 31, 32);
    font-size: 14px;
    font-weight: 500;
    font-family: 'Times New Roman', Times, serif;
    padding: 4px 4px;
    border-radius: 5px;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
    background: linear-gradient(to right, #90cf90, #82a5f1);
    width: fit-content;
}
.website-btn:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(69, 104, 220, 0.2);
}

.linkedin-btn {
    color: #0077b5;
    border-color: rgba(0, 119, 181, 0.2);
}

.linkedin-btn:hover {
    background: linear-gradient(135deg, #0077b5, #0a66c2);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 119, 181, 0.2);
}

.twitter-btn {
    color: #1da1f2;
    border-color: rgba(29, 161, 242, 0.2);
}

.twitter-btn:hover {
    background: linear-gradient(135deg, #1da1f2, #0c85d0);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(29, 161, 242, 0.2);
}

/* New styles for How It Works, Data Storage, and Use Cases sections */
.info-section {
    padding: 80px 0;
    background-color: var(--hero-bg);
    position: relative;
    color: var(--text-primary);
}

.info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--section-gradient);
    z-index: 0;
}

.info-section .section-title {
    color: var(--heading-color);
    position: relative;
    z-index: 2;
    margin-bottom: 50px;
}

.info-section .section-title:after {
    background: var(--primary-gradient);
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.step-item {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.step-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--primary-gradient);
}

.step-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 25px rgba(53, 102, 197, 0.15);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    font-size: 1.3rem;
    font-weight: 700;
    margin-right: 20px;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.2);
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--heading-color);
}

.step-content p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

/* Use Cases Section Styles */
.features-container {
    max-width: 1100px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

#use-cases .feature-card {
    min-height: 240px;
    text-align: center;
    padding: 25px 20px;
}

#use-cases .feature-icon {
    margin-bottom: 15px;
}

#use-cases .feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 12px;
}

#use-cases .feature-card p {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

/* Responsive styles for new sections */
@media (max-width: 768px) {
    .steps-container {
        gap: 20px;
    }
    
    .step-item {
        flex-direction: column;
        padding: 25px;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    #use-cases .feature-card {
        min-height: auto;
    }
}

@media (max-width: 480px) {
    .step-item {
        padding: 20px;
    }
    
    .step-content h3 {
        font-size: 1.2rem;
    }
    
    .step-content p {
        font-size: 0.95rem;
    }
} 