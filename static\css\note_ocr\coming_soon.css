:root {
    --primary-color: #3566c5;
    --primary-gradient: linear-gradient(135deg, #3566c5, #2a539b);
    --secondary-color: #1a3b5d;
    --accent-color: #3566c5;
    --text-color: #333;
    --light-color: #f5f7fa;
    --dark-color: #1a3b5d;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --hero-bg: #f5f7fa;
    --header-gradient: linear-gradient(135deg, #f7f9ff, #e4e9f2);
    --section-gradient: linear-gradient(135deg, rgba(245, 247, 250, 0.95), rgba(228, 233, 242, 0.9));
    --card-bg: rgba(255, 255, 255, 0.9);
    --heading-color: #1a3b5d;
    --text-primary: #1a3b5d;
    --text-secondary: #4a6385;
    --accent-light: #3566c5;
    --accent-gradient: linear-gradient(135deg, #3566c5, #2a539b);
}

/* Reset and Base Styles */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #f5f7fa, #e4e9f2);
    color: var(--text-primary);
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    line-height: 1.6;
}

/* Floating Particles */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(53, 102, 197, 0.1);
    animation: float 15s infinite ease-in-out;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) translateX(0);
    }
    25% {
        transform: translateY(-20px) translateX(10px);
    }
    50% {
        transform: translateY(-10px) translateX(-15px);
    }
    75% {
        transform: translateY(15px) translateX(5px);
    }
}

/* Background Grid */
.background-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(69, 104, 220, 0.015) 1px, transparent 1px),
        linear-gradient(90deg, rgba(69, 104, 220, 0.015) 1px, transparent 1px);
    background-size: 30px 30px;
    z-index: 0;
    opacity: 0.8;
}

/* Main Content */
.container {
    position: relative;
    z-index: 2;
    text-align: center;
    padding: 2.5rem;
    max-width: 800px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
}

h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    letter-spacing: 2px;
    background: linear-gradient(to right, var(--heading-color), var(--primary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 5px 30px rgba(0, 0, 0, 0.03);
    position: relative;
    display: inline-block;
}

h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
    color: var(--text-secondary);
    font-weight: 500;
}

.divider {
    width: 50%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(53, 102, 197, 0.3), transparent);
    margin: 2rem auto;
}

/* New Services Style */
.services-container {
    display: flex;
    justify-content: center;
    flex-wrap: nowrap;
    gap: 1.5rem;
    margin: 2rem 0;
    width: 100%;
}

.service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 32%;
    text-decoration: none;
}

.service-icon-container {
    width: 100%;
    margin-bottom: 0.5rem;
    position: relative;
}

.service-icon-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px 2px 0 0;
    opacity: 0.8;
}

.service-icon-frame {
    background-color: var(--primary-color);
    border-radius: 10px 10px 0 0;
    width: 100%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.2);
    transition: var(--transition);
}

.service-icon {
    font-size: 3rem;
    color: white;
}

.service-title-container {
    background-color: white;
    width: 100%;
    padding: 1rem;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
    position: relative;
}

.service-title {
    color: var(--heading-color);
    font-size: 1.2rem;
    font-weight: 600;
    position: relative;
}

.service-title::after {
    content: '';
    display: block;
    width: 40px;
    height: 3px;
    background: var(--primary-gradient);
    margin: 0.5rem auto 0;
    border-radius: 1.5px;
}

.service-item:hover .service-icon-frame {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.3);
}

.service-item:hover .service-title-container {
    transform: translateY(5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.service-desc {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    z-index: 1;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, rgba(53, 102, 197, 0.9), rgba(42, 83, 155, 0.9));
    color: white;
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(3px);
}

.btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 35px rgba(53, 102, 197, 0.2);
    background: linear-gradient(135deg, rgba(53, 102, 197, 1), rgba(42, 83, 155, 1));
}

.get-started {
    margin-top: 2rem;
}

.social-icons {
    margin-top: 2rem;
}

.social-icons a {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: 0 0.5rem;
    transition: var(--transition);
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    background: rgba(53, 102, 197, 0.1);
}

.social-icons a:hover {
    color: white;
    transform: translateY(-3px);
    background: var(--primary-gradient);
}

/* Floating shapes */
.floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--primary-gradient);
    opacity: 0.1;
    filter: blur(20px);
}

.shape-1 {
    width: 400px;
    height: 400px;
    top: -100px;
    right: -200px;
    animation: floatAnimation 25s infinite linear;
}

.shape-2 {
    width: 300px;
    height: 300px;
    bottom: -50px;
    left: -150px;
    animation: floatAnimation 20s infinite linear reverse;
    background: linear-gradient(135deg, #3566c5, #1a3b5d);
}

.shape-3 {
    width: 200px;
    height: 200px;
    top: 40%;
    right: 20%;
    animation: floatAnimation 15s infinite linear;
    opacity: 0.08;
}

@keyframes floatAnimation {
    0% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
        transform: translate(10px, 10px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translate(0, 20px) rotate(10deg) scale(1);
    }
    75% {
        transform: translate(-10px, 10px) rotate(5deg) scale(0.95);
    }
    100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
}

/* Back button styles */
.back-button {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.back-button a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(53, 102, 197, 0.2);
    transition: var(--transition);
}

.back-button a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(53, 102, 197, 0.4);
}

.back-button i {
    margin-right: 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    h1 {
        font-size: 2.8rem;
    }

    p {
        font-size: 1rem;
    }

    .services-container {
        flex-direction: column;
        align-items: center;
        flex-wrap: wrap;
    }

    .service-item {
        width: 100%;
        max-width: 300px;
    }
}

@media (max-width: 480px) {
    h1 {
        font-size: 2.2rem;
    }

    .container {
        padding: 1.5rem;
        margin: 0 15px;
    }
}
