/* Extractor Page Main Styles */
:root {
    --primary-color: #4361ee;
    --primary-light: #4361ee15;
    --primary-dark: #3a56d4;
    --secondary-color: #4cc9f0;
    --accent-color: #f72585;
    --text-color: #2b2d42;
    --text-light: #8d99ae;
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #e9ecef;
    --success-color: #06d6a0;
    --warning-color: #ffd166;
    --danger-color: #ef476f;
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --purple: #9c27b0;
    --purple-light: #e1bee7;
}

/* Body styles for navbar-less page */
body {
    padding-top: 0px;
    background-color: #f8f9fa;
}

/* Back button */
.back-button-container {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background-color: white;
    color: #3a56d4;
    border-radius: 8px;
    border: 1px solid rgb(180, 197, 218);
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.back-button:hover {
    background-color: #3c97f1;
    color: #e8eaf1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

/* Container Layout */
.extractor-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1.5rem;
    position: relative;
}

.extractor-header {
    text-align: center;
    margin-bottom: 3rem;
}

.extractor-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: rgb(5, 41, 80);
    margin-bottom: 0.5rem;
    position: relative;
    display: inline-block;
}

.extractor-header h1:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.extractor-header p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 1rem auto 0;
}

.extractor-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

/* Card Styling */
.upload-card, .result-card {
    background-color: var(--card-bg);
    border-radius: 20px;
    border: 1px solid rgb(180, 197, 218);
    overflow: hidden;
    height: 100%;
    transition: var(--transition);
}

.upload-card:hover, .result-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    transform: translateY(-5px);
}

.card-header {
    padding: 1.25rem 1.5rem;
    background: transparent;
    color: rgb(1, 8, 20);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.card-content {
    padding: 1.5rem;
}

/* Upload Form Styling - Updated */
.upload-area {
    background-color: #f2f2f2;
    border-radius: 10px;
    padding: 2rem 1rem;
    text-align: center;
    transition: var(--transition);
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
    cursor: pointer;
    border: 1px dashed #ccc;
}

.upload-area.drag-over {
    border-color: var(--purple);
    background-color: var(--purple-light);
}

.upload-icon {
    width: 60px;
    height: 60px;
    background-color: var(--purple);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.upload-icon i {
    font-size: 1.5rem;
    color: white;
}

.upload-area p {
    margin: 0.5rem 0;
    color: #666;
    font-size: 1rem;
}

.or-text {
    margin: 0.5rem 0;
    color: #888;
    font-size: 0.9rem;
}

.browse-button {
    background-color: var(--purple);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    cursor: pointer;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

.browse-button:hover {
    background-color: #7b1fa2;
    transform: translateY(-2px);
}

.hidden-input {
    /* Hide visually but keep accessible to JavaScript */
    opacity: 0;
    position: absolute;
    width: 1px;
    height: 1px;
    overflow: hidden;
    z-index: -1;
}

.preview-section {
    background-color: #f2f2f2;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    color: #888;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.extract-data-button {
    width: 100%;
    padding: 0.875rem;
    background-color: var(--purple);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.extract-data-button:hover:not(:disabled) {
    background-color: #7b1fa2;
    transform: translateY(-2px);
}

.extract-data-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Processing button */
.processing-button {
    width: 100%;
    padding: 0.875rem;
    background-color: #ba68c8;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: not-allowed;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.spinner-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.spinning {
    animation: spin 1.5s linear infinite;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background-color: transparent;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 500;
    color: rgb(15, 1, 1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: var(--transition);
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Processing Indicator */
.processing-indicator {
    text-align: center;
    padding: 3rem 2rem;
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-left-color: var(--primary-color);
    margin: 0 auto 1.5rem;
    animation: spin 1s linear infinite;
}

.processing-indicator p {
    color: var(--text-color);
    font-size: 1rem;
}

/* Empty State */
.empty-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    color: var(--text-light);
    text-align: center;
}

.empty-result i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.empty-result p {
    font-size: 1.1rem;
}

/* OCR Results */
.ocr-result {
    width: 100%;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.result-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.copy-btn {
    background-color: var(--bg-color);
    color: var(--text-color);
    border: none;
    border-radius: 6px;
    padding: 0.4rem 0.75rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: var(--transition);
}

.copy-btn:hover {
    background-color: var(--border-color);
}

.result-text-area {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    min-height: 200px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
    transition: var(--transition);
}

.result-text-area[contenteditable="true"] {
    background-color: white;
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 0 4px var(--primary-light);
}

/* Edit Controls */
.edit-controls {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
}

.cancel-btn, .save-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    transition: var(--transition);
}

.cancel-btn {
    background-color: transparent;
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.cancel-btn:hover {
    background-color: var(--danger-color);
    color: white;
}

.save-btn {
    background-color: var(--success-color);
    color: white;
    border: none;
}

.save-btn:hover {
    background-color: #05b889;
}

/* Modal Styling */
.modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: var(--primary-color);
}

.modal-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 1.5rem;
    padding: 0.5rem;
}

.modal-image-container {
    background-color: var(--bg-color);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 320px;
    overflow: hidden;
}

.modal-image-container img {
    max-width: 100%;
    max-height: 280px;
    object-fit: contain;
    border-radius: 4px;
}

.modal-text-section h6 {
    margin-bottom: 0.75rem;
    color: var(--text-color);
    font-weight: 600;
}

.modal-text-container {
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    height: 280px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-color);
}

.modal-text-container[contenteditable="true"] {
    background-color: white;
    border: 2px solid var(--primary-color);
}

.modal-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    transition: var(--transition);
}

.modal-btn.copy-btn {
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.modal-btn.copy-btn:hover {
    background-color: var(--border-color);
}

.modal-btn.download-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
}

.modal-btn.download-btn:hover {
    background-color: var(--primary-dark);
}

.modal-btn.close-btn {
    background-color: var(--text-light);
    color: white;
    border: none;
}

.modal-btn.close-btn:hover {
    background-color: var(--text-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .extractor-content {
        grid-template-columns: 1fr;
    }
    
    .modal-content-wrapper {
        grid-template-columns: 1fr;
    }
    
    .upload-area {
        min-height: 180px;
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .action-buttons {
        width: 100%;
        justify-content: space-between;
    }
    
    .extractor-header h1 {
        font-size: 2rem;
    }
} 

.modal-backdrop {
    --bs-backdrop-zindex: 1050;
    --bs-backdrop-bg: #ffffff;
    --bs-backdrop-opacity: 0.5;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100vw;
    height: 100vh;
    background-color: var(--bs-backdrop-bg);
}

.upload-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    z-index: 10;
}

.upload-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

/* Chat Section Styles */
.chat-section {
    margin-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    padding-top: 1.5rem;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.chat-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--purple);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggle-chat-btn {
    background: transparent;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    font-size: 1.2rem;
    transition: var(--transition);
}

.toggle-chat-btn:hover {
    color: var(--primary-color);
}

.chat-container {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    background: white;
}

.chat-messages {
    height: 250px;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.chat-message {
    display: flex;
    flex-direction: column;
    max-width: 80%;
}

.chat-message.user-message {
    align-self: flex-end;
}

.chat-message.bot-message {
    align-self: flex-start;
}

.chat-message.system-message {
    align-self: center;
    max-width: 90%;
    text-align: center;
    color: var(--text-light);
    font-style: italic;
    font-size: 0.9rem;
}

.message-content {
    padding: 0.75rem 1rem;
    border-radius: 18px;
    position: relative;
}

.user-message .message-content {
    background-color: var(--purple);
    color: white;
    border-bottom-right-radius: 4px;
}

.bot-message .message-content {
    background-color: var(--bg-color);
    color: var(--text-color);
    border-bottom-left-radius: 4px;
}

.message-content p {
    margin: 0;
    line-height: 1.4;
}

.message-content p + p {
    margin-top: 0.5rem;
}

.chat-input-container {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

.chat-input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 0.6rem;
    border-radius: 20px;
    font-size: 0.95rem;
    color: var(--text-color);
    outline: none;
}

.chat-input:focus {
    background-color: white;
}

.send-chat-btn {
    background-color: var(--purple);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.send-chat-btn:hover {
    background-color: #7b1fa2;
    transform: scale(1.1);
}

.chat-processing {
    padding: 0.5rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color);
}

.chat-spinner {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(156, 39, 176, 0.2);
    border-radius: 50%;
    border-left-color: var(--purple);
    animation: spin 1s linear infinite;
}

/* Fallback button styles */
.fallback-browse-container {
    text-align: center;
    margin-bottom: 1rem;
}

.fallback-browse-button {
    background-color: #4361ee;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.fallback-browse-button:hover {
    background-color: #3a56d4;
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

/* Code block styling in chat messages */
.message-content pre {
    background-color: #1e1e1e;
    color: #e6e6e6;
    border-radius: 8px;
    padding: 0.75rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.message-content code {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.message-content p {
    margin: 0;
    line-height: 1.4;
}

.message-content p + p,
.message-content p + pre,
.message-content pre + p {
    margin-top: 0.5rem;
}

/* Chat info banner */
.chat-info-banner {
    background-color: rgba(67, 97, 238, 0.1);
    border-left: 3px solid var(--primary-color);
    padding: 0.75rem 1rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chat-info-banner i {
    color: var(--primary-color);
    font-size: 1.1rem;
}

/* Mode Selection Styles */
.mode-selection-container {
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.25rem;
    background-color: #f8f9fa;
}

.mode-header {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
    text-align: center;
}

.mode-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.mode-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background-color: white;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    flex: 1;
    min-width: 150px;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    color: var(--text-color);
}

.mode-button:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.mode-button i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.mode-button:first-child i {
    color: var(--purple);
}

.mode-button:last-child i {
    color: #4cc9f0;
}

.mode-button-text {
    font-weight: 500;
    text-align: center;
    line-height: 1.4;
}

.mode-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #f0f0f0;
}

/* Custom Prompt Input */
.custom-prompt-input {
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.25rem;
}

.custom-prompt-input label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

.prompt-textarea {
    width: 100%;
    height: 120px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    resize: vertical;
    font-family: inherit;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.prompt-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

/* Result header styles */
#resultHeaderText {
    transition: all 0.3s ease;
}

/* Active mode highlighting */
.mode-button.active {
    border-color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.mode-button.active:first-child {
    border-color: var(--purple);
    background-color: rgba(156, 39, 176, 0.05);
}

.mode-button.active:last-child {
    border-color: #4cc9f0;
    background-color: rgba(76, 201, 240, 0.05);
}


