/* Base styles */
:root {
    --primary-color: #4a6fa5;
    --secondary-color: #6d8cc7;
    --accent-color: #ffa500;
    --text-color: #333;
    --light-bg: #f5f7fa;
    --dark-bg: #2d3748;
    --border-color: #e2e8f0;
    --error-color: #e53e3e;
    --success-color: #38a169;
    --warning-color: #ed8936;
    --info-color: #4299e1;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    height: 100%;
    font-family: 'Poppins', sans-serif;
    line-height: 1.1;
    color: var(--text-color);
    background-color: transparent;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Login page */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-box {
    width: 400px;
    padding: 40px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.login-box h1 {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.logo {
    text-align: center;
    margin-bottom: 20px;
}

.logo img {
    max-width: 150px;
    height: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 16px;
}

.btn-login {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-login:hover {
    background-color: var(--secondary-color);
}

/* Flash messages */
.flash-message {
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 5px;
    font-weight: 500;
}

.error {
    background-color: rgba(229,62,62,0.1);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.success {
    background-color: rgba(56,161,105,0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.warning {
    background-color: rgba(237,137,54,0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.info {
    background-color: rgba(66,153,225,0.1);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}


.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--primary-color);
    color: white;
    padding: 15px 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    cursor: pointer;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-logout, .btn-back {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: rgba(255,255,255,0.2);
    color: white;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.btn-logout:hover, .btn-back:hover {
    background-color: rgba(255,255,255,0.3);
}

/* Breadcrumb */
.breadcrumb {
    background-color: white;
    padding: 12px 30px;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb ol {
    display: flex;
    list-style: none;
    flex-wrap: wrap;
}

.breadcrumb li {
    display: flex;
    align-items: center;
}

.breadcrumb li:not(:last-child)::after {
    content: '/';
    margin: 0 8px;
    color: var(--border-color);
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 30px;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: var(--secondary-color);
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 8px 15px 8px 40px;
    width: 300px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 14px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--border-color);
}

/* File browser */
.file-browser {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
}

.file-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

.file-item {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 15px;
    transition: box-shadow 0.3s;
}

.file-item:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.file-item a {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-color);
    text-decoration: none;
    word-break: break-word;
}

.folder a {
    color: var(--primary-color);
}

.file-item i {
    font-size: 1.2rem;
}

.folder i {
    color: var(--accent-color);
}

.parent-folder {
    background-color: var(--light-bg);
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    color: #a0aec0;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow: auto;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

.close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
}

/* Annotation page */
.annotation-container {
    display: flex;
    height: calc(100vh - 64px); /* Full height minus header */
}

.annotation-sidebar {
    width: 300px;
    background-color: white;
    border-right: 1px solid var(--border-color);
    padding: 20px;
    overflow-y: auto;
}

.annotation-workspace {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
}

.annotation-sidebar h3 {
    margin: 20px 0 10px;
    font-size: 1rem;
    color: var(--primary-color);
}

.annotation-sidebar h3:first-child {
    margin-top: 0;
}

.tool-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.tool-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 12px;
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s;
}

.tool-btn:hover {
    background-color: var(--border-color);
}

.tool-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.label-selector {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.label-selector select {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
}

.label-selector button {
    width: 40px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--light-bg);
    cursor: pointer;
}

.mini-modal {
    display: none;
    margin-top: 10px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 10px;
}

.mini-modal input {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.mini-modal button {
    width: 100%;
    padding: 8px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.object-properties {
    margin-bottom: 20px;
    padding: 10px;
    background-color: var(--light-bg);
    border-radius: 5px;
    font-size: 0.9rem;
}

.no-selection {
    font-style: italic;
    color: #a0aec0;
}

.output-folder input {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
}

.save-controls {
    margin-top: 30px;
}

.btn-save {
    width: 100%;
    padding: 12px;
    background-color: var(--success-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-save:hover {
    background-color: #2f855a;
}

.file-info {
    margin-bottom: 15px;
}

.file-info h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.file-info p {
    font-size: 0.9rem;
    color: #666;
}

.canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #f0f0f0;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 500px;
}

#annotationCanvas {
    background-color: white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.zoom-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    align-items: center;
    justify-content: center;
}

.zoom-controls button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.zoom-controls button:hover {
    background-color: var(--light-bg);
}

#zoomLevel {
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 60px;
    text-align: center;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .annotation-container {
        flex-direction: column;
    }
    
    .annotation-sidebar {
        width: 100%;
        max-width: none;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }
    
    .annotation-sidebar > div,
    .annotation-sidebar > h3 {
        flex: 1 1 200px;
    }
    
    .tool-buttons {
        display: flex;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .annotation-sidebar > div,
    .annotation-sidebar > h3 {
        flex: 1 1 100%;
    }
    
    .main-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 10px 15px;
    }
    
    .header-right {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }
    
    .file-browser {
        padding: 10px;
    }
    
    .file-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Annotation type selector */
.annotation-type-selector {
    margin-bottom: 20px;
}

.annotation-type-selector select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    background-color: white;
}

/* Text areas for writing and description */
#textContent, #imageDescription {
    width: 100%;
    min-height: 150px;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 0.9rem;
    resize: vertical;
}

/* Objects list */
.objects-list {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 8px;
    margin-bottom: 15px;
    background-color: white;
}

.object-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    border-bottom: 1px solid var(--border-color);
}

.object-item:last-child {
    border-bottom: none;
}

.object-label {
    font-weight: 600;
}

.object-actions {
    display: flex;
    gap: 5px;
}

.object-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--primary-color);
}

.object-actions button:hover {
    color: var(--accent-color);
}

.no-objects {
    text-align: center;
    color: #a0aec0;
    font-style: italic;
    padding: 10px;
}

/* Delete button */
.btn-delete {
    width: 100%;
    padding: 12px;
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.btn-delete:hover {
    background-color: #c53030;
}

/* Delete confirmation modal */
.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn-cancel {
    padding: 8px 16px;
    background-color: #e2e8f0;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.btn-confirm {
    padding: 8px 16px;
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Styles from base.html */
.navbar {
    background: linear-gradient(135deg,rgb(98, 130, 177),rgb(98, 121, 185)) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
    height: auto;
    background-color: transparent;
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 1px;
    font-size: 1.5rem;
}

.navbar .nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.navbar .nav-link:hover {
    color: #fff !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff !important;
    font-weight: 600;
}

.navbar .nav-link i {
    margin-right: 5px;
}

.dropdown-menu {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    padding: 0.5rem 0;
    margin-top: 10px;
}

.dropdown-item {
    padding: 0.6rem 1.2rem;
    transition: all 0.2s ease;
    color: #495057;
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    color: #4361ee;
}

.dropdown-item i {
    color: #4361ee;
}

.footer {
    color: black;
    padding: 0.8rem 0;
    margin-top: auto;
}

.footer .text-muted {
    color: rgba(0, 4, 24, 0.8) !important;
    font-weight: 700;
}