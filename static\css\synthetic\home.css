/* Dataset Generation Home Page Styles */

/* General Styles */
:root {
    --primary-color: #1e3a5f;
    --primary-light: #4A6385;
    --primary-dark: #132C46;
    --secondary-color: #f5f7fa;
    --text-dark: #1e3a5f;
    --text-medium: #516885;
    --text-light: #6B7C93;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(26, 59, 93, 0.1);
    --button-blue: #3566c5;
}
  
* {
    margin: 0;
    padding: 0;
}

.container, .card, .action-btn, .secondary-btn, .back-button, .badge {
    box-sizing: border-box;
}
  
body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f5f7fa;
    min-height: 100vh;
    padding-top: 100px;
    overflow-x: hidden;
}
  
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header/Navigation Styles */
header {
    background-color: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    border-bottom: 1px solid rgba(26, 59, 93, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.header-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-left: 10px;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.logo-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #3566c5;
    margin-bottom: 2px;
    display: block;
    white-space: nowrap;
}

.logo-subtitle {
    font-size: 1rem;
    color: var(--text-medium);
    margin-bottom: 2px;
    display: block;
    white-space: nowrap;
}

.logo-link img {
    height: 60px;
    width: auto;
    transition: var(--transition);
    margin-right: 15px;
}

.beta-label {
    font-size: 0.8rem;
    background: linear-gradient(to right, #90cf90, #82a5f1);
    color: rgb(31, 31, 32);
    padding: 1px 6px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-block;
    white-space: nowrap;
    width: fit-content;
    align-self: flex-start;
}

.nav-links {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    margin: 0 20px;
}

.nav-links ul {
    display: flex;
    list-style-type: none;
    justify-content: flex-end;
    padding: 12px 25px;
    margin: 0;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(26, 59, 93, 0.15);
    min-width: 600px;
}

.nav-links li {
    margin: 0 15px;
    display: inline;
    white-space: nowrap;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-medium);
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    border-radius: 25px;
    white-space: nowrap;
}

.nav-links a:hover {
    color: var(--button-blue);
    background-color: rgba(53, 102, 197, 0.1);
}

.button-area {
    display: flex;
    justify-content: flex-end;
}

.back-button {
    background-color: var(--button-blue);
    color: white;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 50px;
    font-size: 0.95rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.back-button:hover {
    background-color: #2a539b;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(53, 102, 197, 0.3);
}

.arrow {
    font-size: 1.1rem;
}

/* Responsive Header */
@media (max-width: 1200px) {
    .header-content {
        max-width: 100%;
        padding: 0 20px;
    }

    .nav-links ul {
        min-width: auto;
        padding: 10px 20px;
    }

    .logo-title {
        font-size: 1.2rem;
    }

    .logo-subtitle {
        font-size: 0.9rem;
    }
}

@media (max-width: 992px) {
    .header-content {
        flex-wrap: wrap;
        gap: 15px;
    }

    .nav-links {
        order: 3;
        width: 100%;
        margin: 10px 0 0;
    }

    .nav-links ul {
        justify-content: center;
        padding: 10px;
        flex-wrap: wrap;
    }

    .nav-links li {
        margin: 5px 10px;
    }

    .logo-link img {
        height: 50px;
    }
}

@media (max-width: 768px) {
    header {
        padding: 15px 0;
    }

    .header-content {
        padding: 0 15px;
        justify-content: space-between;
    }

    .logo {
        padding-left: 0;
    }

    .logo-link img {
        height: 40px;
        margin-right: 10px;
    }

    .logo-title {
        font-size: 1.1rem;
    }

    .logo-subtitle {
        font-size: 0.8rem;
    }

    .beta-label {
        font-size: 0.7rem;
        padding: 0px 4px;
    }

    .nav-links ul {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 8px 15px;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-links a {
        font-size: 0.9rem;
        padding: 5px 10px;
    }

    .back-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    body {
        padding-top: 140px;
    }

    .container {
        padding: 0 15px;
    }

    .hero-content-wrapper {
        padding: 0 15px;
    }

    .card-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0;
    }

    .card {
        padding: 30px;
        margin: 0;
        width: 100%;
    }

    .section {
        padding: 40px 0;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 30px;
        padding: 0;
    }

    .hero-container {
        min-height: auto;
        padding: 40px 0 60px;
    }

    .main-title {
        font-size: 2.8rem;
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 30px;
        max-width: 100%;
    }

    .typing-content {
        margin: 30px 0;
        min-height: auto;
    }

    .typing-text {
        font-size: 1.1rem;
        margin-bottom: 15px;
        padding-left: 25px;
    }

    .typing-text:before {
        font-size: 1.4rem;
        top: 0;
    }

    /* Dataset Types Grid */
    .dataset-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0;
    }

    .small-card {
        padding: 25px;
    }

    /* Model Cards */
    #models .card {
        min-height: auto;
    }

    /* Trust Badges */
    .trust-badges {
        flex-direction: column;
        gap: 15px;
        margin-bottom: 30px;
    }

    .badge {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 0 10px;
    }

    .logo {
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-bottom: 10px;
    }

    .logo-link img {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .logo-text {
        align-items: center;
    }

    .nav-links {
        margin: 5px 0;
    }

    .nav-links ul {
        padding: 5px 10px;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }

    .nav-links li {
        margin: 3px 5px;
    }

    .nav-links a {
        font-size: 0.85rem;
        padding: 4px 8px;
    }

    .button-area {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .back-button {
        width: 100%;
        justify-content: center;
        padding: 8px 12px;
        font-size: 0.85rem;
    }

    body {
        padding-top: 180px;
    }

    .container {
        padding: 0 12px;
    }

    .card {
        padding: 25px;
    }

    .main-title {
        font-size: 2.4rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .typing-text {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.6rem;
        margin-bottom: 25px;
    }

    .card h3 {
        font-size: 1.3rem;
        margin-bottom: 12px;
    }

    .card p {
        font-size: 0.95rem;
    }

    .card-icon {
        width: 55px;
        height: 55px;
        margin-bottom: 15px;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }

    .section {
        padding: 30px 0;
    }

    /* Adjust spacing between sections */
    #features, #how-it-works, #models, #dataset-types, #model-distillation, #use-cases {
        margin-top: 0;
        padding-top: 30px;
    }
}

/* Hero Section */
.hero-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 60px 0;
    position: relative;
    text-align: left;
    margin: 0;
    background: transparent;
}

.hero-content-wrapper {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    width: 100%;
}

.hero-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 900px;
}

.main-title {
    font-size: 5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 24px;
    line-height: 1.1;
    letter-spacing: -1px;
    text-align: left;
}

.hero-secondary-title {
    font-size: 2.6rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 20px;
    line-height: 1.3;
    letter-spacing: -0.5px;
    text-align: left;
    opacity: 0.9;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-medium);
    margin-bottom: 40px;
    line-height: 1.6;
    font-weight: 400;
    text-align: left;
    max-width: 600px;
    opacity: 0.8;
}

/* Trust Badges */
.trust-badges {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
}

.badge {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.badge i {
    color: #3566c5;
    font-size: 1.2rem;
}

.badge span {
    color: var(--text-medium);
    font-weight: 500;
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.get-started-btn {
    background-color: var(--button-blue);
    color: white;
    box-shadow: 0 4px 15px rgba(53, 102, 197, 0.2);
    padding: 14px 32px;
    font-size: 1.1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.learn-more-btn, .sample-dataset-btn, .synground-btn {
    background-color: white;
    color: var(--text-medium);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.get-started-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(53, 102, 197, 0.3);
}

/* Secondary Buttons */
.secondary-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 40px;
}

.secondary-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.7);
    color: var(--text-medium);
    text-decoration: none;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background-color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: -30px;
    left: 60%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    cursor: pointer;
    z-index: 10;
}

.scroll-indicator a {
    color: var(--button-blue);
    font-size: 2rem;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease, transform 0.3s ease;
    display: block;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scroll-indicator a:hover {
    opacity: 1;
    transform: scale(1.1);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-15px);
    }
    60% {
        transform: translateY(-7px);
    }
}

/* Smooth Scrolling */
html {
    scroll-padding-top: 100px; /* Should match body padding-top */
    scroll-behavior: smooth;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .card-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    #models .card-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .card-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
    }

    #models .card-grid {
        grid-template-columns: 1fr;
    }

    #models .card {
        min-height: auto;
        padding: 25px;
    }

    .card {
        padding: 25px;
    }

    .section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 40px;
        padding: 0 15px;
    }

    .hero-container {
        padding: 40px 0;
    }

    .main-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .typing-text {
        font-size: 1.2rem;
        padding-right: 15px;
    }
}

@media (max-width: 480px) {
    .card-grid {
        gap: 15px;
    }

    .card {
        padding: 20px;
    }

    .card h3 {
        font-size: 1.2rem;
    }

    .card p {
        font-size: 0.9rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .typing-text {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
    }

    .card-icon i {
        font-size: 1.4rem;
    }
}

/* Section Styles */
.section {
    padding: 80px 0;
    margin: 0;
    position: relative;
    background: transparent;
}

/* Remove all existing background gradients */
.section::before,
.hero-container::before,
.hero-container::after {
    display: none;
}

/* Single unified background gradient for the entire page */
.main-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 70% 30%, rgba(53, 102, 197, 0.06), transparent 60%), 
        radial-gradient(circle at 30% 70%, rgba(42, 83, 155, 0.06), transparent 60%),
        radial-gradient(circle at 90% 90%, rgba(74, 99, 133, 0.04), transparent 40%),
        radial-gradient(circle at 10% 10%, rgba(26, 59, 93, 0.04), transparent 40%);
    z-index: -1;
    pointer-events: none;
}

#features {
    padding-top: 40px;
}

.section-title {
    position: relative;
    z-index: 1;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 60px;
}
  
.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 15px auto 0;
    border-radius: 2px;
}
  
/* Card Grid */
.card-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-bottom: 40px;
}
  
/* Model Cards Grid */
#models .card-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}
  
#models .card {
    min-height: 320px;
    padding: 25px 15px;
}
  
#models .card p {
    font-size: 0.9rem;
}
  
.dataset-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}
#model-distillation .card-grid {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 35px;
}

#use-cases .card-grid {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 35px;
}
  
/* Card Styles */
.card {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}
  
#dataset-types .card {
    background-color: transparent;
    box-shadow: none;
    padding: 20px;
    border: none;
}
  
#dataset-types .card:hover {
    transform: none;
    box-shadow: none;
}
  
#dataset-types .card-icon {
    background-color: #F5F7FA;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
}
  
#dataset-types .card h3 {
    color: #1A3B5D;
    font-size: 1.2rem;
    margin-bottom: 8px;
}
  
#dataset-types .card p {
    color: #4A6385;
    font-size: 0.9rem;
    line-height: 1.5;
}
  
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(26, 59, 93, 0.15);
}
  
.small-card {
    padding: 25px;
}
  
.card-icon {
    width: 65px;
    height: 65px;
    background-color: rgba(26, 59, 93, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}
  
.small-icon {
    width: 55px;
    height: 55px;
}
  
.card-icon i {
    font-size: 1.8rem;
    color: #3566c5;
}
  
.small-icon i {
    font-size: 1.4rem;
}
  
.card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
}
  
.small-card h3 {
    font-size: 1.2rem;
    margin-bottom: 12px;
}
  
.card p {
    color: var(--text-light);
    line-height: 1.5;
    font-size: 0.95rem;
}
  
.small-card p {
    font-size: 0.85rem;
    line-height: 1.4;
}
  
/* Step Number */
.step-number {
    width: 40px;
    height: 40px;
    background-color: #3566c5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 700;
    font-size: 1.2rem;
    margin-bottom: 15px;
}
  
/* Animations */
.animate-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}
  
.animate-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
  
/* Admin Login */
.admin-login-container {
    text-align: center;
    padding: 20px 0 40px;
    background-color: var(--secondary-color);
    border-top: 1px solid rgba(116, 69, 194, 0.1);
}
  
.admin-login-btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}
  
.admin-login-btn:hover {
    background-color: rgba(26, 59, 93, 0.1);
    transform: translateY(-2px);
}
  
/* Footer */
.main-footer {
    background-color: var(--primary-dark);
    color: white;
    padding: 20px 0;
    text-align: center;
}
  
/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: #3566c5;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
  
.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}
  
.back-to-top:hover {
    background-color: #2a539b;
    transform: translateY(-3px);
}
  
/* Model Distillation Section Styles */
.section-intro {
    max-width: 800px;
    margin: 0 auto 50px;
    text-align: center;
    color: var(--text-medium);
    font-size: 1.1rem;
    line-height: 1.8;
}
  
.subsection-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--primary-color);
    text-align: center;
    margin: 50px 0 30px;
}
  
.distillation-category {
    font-size: 1.4rem;
    font-weight: 500;
    color: var(--text-dark);
    margin: 40px 0 25px;
    text-align: center;
    position: relative;
}
  
.distillation-category::after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background-color: var(--primary-light);
    margin: 15px auto 0;
    border-radius: 2px;
}
  
/* Footer */
.main-footer {
    background-color: #1a3b5d;
    color: white;
    padding: 20px 0;
    text-align: center;
}

#features {
    margin-top: -60px;
    padding-top: 60px;
    background: transparent;
}

/* Typing Animation Styles */
.typing-content {
    margin: 40px 0;
    min-height: 120px;
}

.typing-text {
    font-size: 1.5rem;
    color: var(--text-medium);
    opacity: 0;
    transform: translateY(20px);
    margin-bottom: 25px;
    position: relative;
    padding-left: 30px;
    animation: fadeInUp 0.8s ease forwards;
    font-weight: 500;
}

.typing-text:before {
    content: '›';
    position: absolute;
    left: 0;
    color: var(--button-blue);
    font-weight: bold;
    font-size: 1.8rem;
    top: -2px;
}

.typing-text:nth-child(1) {
    animation-delay: 0.7s;
}

.typing-text:nth-child(2) {
    animation-delay: 1.7s;
}

.typing-text:nth-child(3) {
    animation-delay: 2.7s;
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Remove the blinking cursor effect as it doesn't look good */
.typing-text:after {
    display: none;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .card-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    #models .card-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .card-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0 15px;
    }

    #models .card-grid {
        grid-template-columns: 1fr;
    }

    #models .card {
        min-height: auto;
        padding: 25px;
    }

    .card {
        padding: 25px;
    }

    .section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 40px;
        padding: 0 15px;
    }

    .hero-container {
        padding: 40px 0;
    }

    .main-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .typing-text {
        font-size: 1.2rem;
        padding-right: 15px;
    }
}

@media (max-width: 480px) {
    .card-grid {
        gap: 15px;
    }

    .card {
        padding: 20px;
    }

    .card h3 {
        font-size: 1.2rem;
    }

    .card p {
        font-size: 0.9rem;
    }

    .main-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .typing-text {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .card-icon {
        width: 50px;
        height: 50px;
    }

    .card-icon i {
        font-size: 1.4rem;
    }
} 