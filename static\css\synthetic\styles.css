/* ===== Common Styles ===== */
:root {
  --primary-color: #0056b3;
  --secondary-color: #003366;
  --accent-color: #4a90e2;
  --text-light: #E5E5E5;
  --text-white: #FFFFFF;
  --text-color: #495057;
  --bg-color: #ffffff;
  --card-bg: #ffffff;
  --input-bg: #ffffff;
  --input-border: #dee2e6;
  --input-focus: var(--accent-color);
  --error-color: #dc3545;
  --success-color: #28a745;
  --font-primary: 'Poppins', sans-serif;
  --font-secondary: 'Roboto', sans-serif;
  --font-tertiary: 'Lato', sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
  position: relative;
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-color);
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 15px;
}

/* ===== Background Effects ===== */
.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
  opacity: 0.1;
  z-index: -3;
}

.noise-texture {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAANQSURBVGhD7ZpNSFRRFMfPfX5MTTNGoyRhIwVREEmrFhG0KChatKgW0aKoFhFC0ApahItoEUWbwEUUFLRo1SqoTUhEQR+bIlD6cCJnJHWc6cy8c/7vzLyZ92beR5s8DN8fhpl5991z7n333HPvHaRKlSp/HdhoKRRV9Nh6cHGpxzVdR+vqGsHc7AwaxmIzD3p8gy+pKpQCBYFsP9LwYFuTcbW3uwGNehXKvqt+CF+/9Z17MfRF9JWGvECq6+sba5uw5+a5A7KvTMHwS7DJiYngzcA29P5WeT+F6KjtOHu8N/tZZrhGhm7V1W2KJ/Ac2pOdm+KK3eFt33ZXDnxOXsZgdTvyYfJ7+1D8LNV7N7q7+x8+HGzLrvwRnEBWzYDTLRulVavOhTutZ7AzO5bR6HdpCkKhSekKqMvO80FWA5qM1+qLjKSk/JD+4wQWP3qS7KrNXsv9/eaYHOeClUhte+tL9Psy5qIeITpYn4mQlp2UGfEsnE7oSXRdXNkj1NhN2CmYmJnZg0QsNnxh5ESdzMlBzZERPZZYg4bRLXOFz7eTcySZ4aeP5vk0OnZl3pGp5CZrRrQpCTG3iiLnSDzKA60TKRuJDUZrZmH8sOjCo+Qgke8YOLLw1UhjZqOplRvBa2F9cC3KH1tGZuZRE/NqacYk9k4YJHKxZYQimv51IuWGLSOMEY5FT6jYRpKJ5IowsQVbRoRG0XVtYZgYMUh4xZYR3dATvw0jpDzlCR9tkcOWkTS7EV4pCZRo8gkWsiNKS37h90vTLVH+2DIyO2dCWEwsPvNNWojIaY2twhg9zSt2dCNM4e9fC+rlU1/I8+zIRw0LYZ5vLQeKpf65JiNEzbZmZGGe6LzMSJXrWEo5TnhkxAV+G9GEL7pJoqb4bvSXUPzFNqIlp5EPDj8mXGnmwfFdaygUWjnWdpRsJLLosCQD0RzRXInobOCbGFfvK/pIEiLj5eUoXm7CYl4eJJq+dE2Oh88vXiMixPuQYSZTurIGYeYWRiQPXoL24dPo6RM32ZUbYiXH4SPuxNYWvtTRYcg9UcVi7H1I/HoxWvLn/CpV/ldw7ge/6xM/Z5kBJwAAAABJRU5ErkJggg==');
  opacity: 0.03;
  z-index: -2;
}

.background-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(rgba(65, 84, 255, 0.03) 1px, transparent 1px),
  linear-gradient(90deg, rgba(65, 84, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: -1;
}

.blob-1, .blob-2 {
  position: fixed;
  border-radius: 50%;
  filter: blur(50px);
  z-index: -2;
}

.blob-1 {
  width: 50vw;
  height: 50vw;
  background: radial-gradient(rgba(74, 144, 226, 0.1), rgba(0, 86, 179, 0.05));
  top: -10%;
  right: -10%;
}

.blob-2 {
  width: 40vw;
  height: 40vw;
  background: radial-gradient(rgba(0, 51, 102, 0.1), rgba(74, 144, 226, 0.05));
  bottom: -10%;
  left: -10%;
}

#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -3;
}

/* ===== Buttons ===== */
.btn-primary, .btn-secondary, .btn-outline-primary, .btn-outline-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  gap: 0.5rem;
  border: none;
}


.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary-color);
  color: white;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.1rem;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.85rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--primary-color);
}

/* ===== Forms and Inputs ===== */
.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--input-bg);
  background-clip: padding-box;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--input-focus);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--input-bg);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid var(--input-border);
  border-radius: 4px;
  appearance: none;
}

.form-select:focus {
  border-color: var(--input-focus);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.form-label {
  display: inline-block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.5rem;
}

.form-check-input {
  float: left;
  margin-left: -1.5em;
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: var(--input-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid var(--input-border);
  appearance: none;
}

.form-check-input[type="checkbox"] {
  border-radius: 0.25em;
}

.form-check .form-check-input {
  margin-left: -0.5rem;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3 6-6'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

/* ===== Header ===== */
.main-header {
  padding: 1rem 0;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(222, 226, 230, 0.5);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo img {
  height: 40px;
  width: auto;
}

.logo-text {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}

.logo-text span {
  font-weight: 700;
  font-size: 1.1rem;
}

.logo-text .subtitle {
  font-size: 0.8rem;
  font-weight: 400;
  color: var(--secondary-color);
}

.main-nav {
  display: flex;
  align-items: center;
}

.nav-container {
  margin-right: 1.5rem;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 1.5rem;
  margin: 0;
}

.nav-links a {
  color: var(--text-color);
  font-weight: 500;
  transition: color 0.3s;
  position: relative;
}

.nav-links a:hover {
  color: var(--primary-color);
}

.nav-links a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s;
}

.nav-links a:hover::after {
  width: 100%;
}

.login-btn {
  background-color: var(--primary-color);
  color: var(--text-white);
  padding: 0.5rem 1.25rem;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s;
}

.login-btn:hover {
  background-color: var(--secondary-color);
  color: var(--text-white);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.mobile-menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px 0;
  background-color: var(--text-color);
  transition: transform 0.3s, opacity 0.3s;
}

/* ===== Footer ===== */
.main-footer {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(222, 226, 230, 0.5);
  padding: 1.5rem 0;
  text-align: center;
  position: relative;
  margin-top: 3rem;
}

.main-footer .container {
  position: relative;
  z-index: 1;
}

.main-footer p {
  margin: 0;
  color: var(--secondary-color);
}

/* ===== Layout Styles ===== */
.section-title {
  font-family: var(--font-primary);
  font-weight: 700;
  margin-bottom: 2rem;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  width: 50%;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -8px;
  left: 0;
}

/* ===== Dataset Page Styles ===== */
.dataset-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 2rem;
}

.dataset-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dataset-header h1 {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.8rem;
}

.dataset-header p {
  color: var(--secondary-color);
  font-size: 1.2rem;
}

.panels-container {
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
  margin-bottom: 2.5rem;
}
.card-body {
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: relative;
  backdrop-filter: blur(5px);
}

.dataset-panel {
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  position: relative;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: transform 0.3s, box-shadow 0.3s;
  width: 100%;
}

.dataset-panel:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dataset-panel h2 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dataset-panel p {
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.dataset-panel h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

/* Improved Dataset Type Styling */
.selected-dataset-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 12px;
  padding: 1.8rem;
  margin-bottom: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(222, 226, 230, 0.6);
}

.selected-dataset-container:hover {
  background-color: rgba(245, 247, 255, 0.8);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.selected-dataset-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(74, 107, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.2rem;
  color: var(--primary-color);
  font-size: 1.6rem;
  /* Fix for SVG mask icons */
  mask-size: 32px;
  -webkit-mask-size: 32px;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-position: center;
}

.selected-dataset-name {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.change-dataset-link {
  font-size: 0.95rem;
  color: var(--primary-color);
  text-decoration: underline;
  transition: all 0.2s ease;
  display: inline-block;
  margin-top: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
}

.change-dataset-link:hover {
  background-color: rgba(74, 107, 255, 0.1);
  text-decoration: none;
}

/* Dataset Type Options Styling */
.dataset-type-options {
  background-color: rgba(245, 247, 255, 0.8);
  border-radius: 12px;
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  border: 1px solid rgba(222, 226, 230, 0.6);
}

.form-check {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  margin-bottom: 0;
}

.form-check:hover {
  border-color: var(--primary-color);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.form-check-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  cursor: pointer;
}

.form-check-input:checked + .form-check-label {
  color: var(--primary-color);
}

.sources-list-container {
  border: 1px solid var(--input-border);
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  margin-bottom: 2rem;
}

.sources-list-header {
  background-color: rgba(245, 247, 255, 0.6);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--input-border);
  font-weight: 600;
}

.sources-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sources-count {
  font-size: 0.85rem;
  color: var(--secondary-color);
}

.sources-list-body {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  background-color: rgba(245, 247, 255, 0.6);
}

.loading-sources, .loading-keywords {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--secondary-color);
  gap: 0.5rem;
}

.keywords-container {
  padding: 1.5rem;
  max-height: 300px;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.keyword-tag {
  background-color: rgba(74, 107, 255, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.keyword-tag:hover {
  background-color: rgba(74, 107, 255, 0.2);
}

.info-message {
  background-color: rgba(23, 162, 184, 0.1);
  color: var(--accent-color);
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.model-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

.model-option {
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
  border: 2px solid transparent;
}

.model-option:hover {
  background-color: rgba(245, 247, 255, 0.9);
}

.model-option.selected {
  border-color: var(--primary-color);
  background-color: rgba(74, 107, 255, 0.05);
}

.model-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.model-icon {
  width: 24px;
  height: 24px;
  margin-right: 0.5rem;
}

.model-logo {
  max-width: 100%;
  height: auto;
}

.model-name {
  font-weight: 600;
}

.model-description {
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.api-key-container {
  margin-top: 1rem;
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  padding: 1rem;
}

.api-key-options {
  margin-bottom: 1rem;
}

.api-key-notification {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.hidden {
  display: none;
}

.dataset-options {
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  padding: 1rem;
}

.option-group {
  margin-bottom: 1.25rem;
}

.option-group:last-child {
  margin-bottom: 0;
}

.small-text {
  font-size: 0.8rem;
  color: var(--secondary-color);
  margin-top: 0.25rem;
}

.select-wrapper {
  position: relative;
}

.custom-query-container {
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.custom-query-list {
  margin-bottom: 1rem;
  max-height: 150px;
  overflow-y: auto;
}

.query-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  background-color: rgba(255, 255, 255, 0.5);
}

.query-text {
  flex: 1;
  margin-right: 0.5rem;
}

.query-input-group {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.query-input-group textarea {
  flex: 1;
  min-height: 100px;
}

.query-input-group button {
  align-self: flex-end;
}

.custom-query-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.generate-btn-container {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.generate-btn-container .btn {
  padding: 0.9rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.01em;
  box-shadow: 0 4px 12px rgba(74, 107, 255, 0.25);
}

.dataset-preview {
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  overflow: visible; /* Changed from hidden to visible to prevent outer container scrolling */
  margin-bottom: 1.5rem;
}

.dataset-preview-header {
  background-color: rgba(74, 107, 255, 0.05);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--input-border);
}

.dataset-preview-body {
  padding: 1rem;
  max-height: 700px; /* Keep this to maintain inner container scrolling */
  overflow-y: auto; /* Keep this to maintain inner container scrolling */
}

.qa-pair {
  background-color: rgba(255, 255, 255, 0.5);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.qa-pair:last-child {
  margin-bottom: 0;
}

.qa-question {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.qa-answer {
  font-size: 0.95rem;
}

.preview-controls {
  display: flex;
  gap: 0.5rem;
}

.export-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.btn-export {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: rgba(245, 247, 255, 0.6);
  border: 1px solid var(--input-border);
  border-radius: 4px;
  font-weight: 500;
  transition: all 0.3s;
  cursor: pointer;
}

.btn-export:hover {
  background-color: rgba(74, 107, 255, 0.1);
  border-color: var(--primary-color);
}

/* ===== Upload Page Styles ===== */
.upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1.5rem;
}

.upload-header {
  text-align: center;
  margin-bottom: 3rem;
}

.upload-header h1 {
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: 0.5rem;
}

.upload-header p {
  color: var(--secondary-color);
  font-size: 1.1rem;
}

.upload-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.upload-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-tab i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
}

.upload-tab span {
  font-weight: 500;
}

.upload-tab:hover {
  background-color: rgba(245, 247, 255, 0.9);
  transform: translateY(-3px);
}

.upload-tab.active {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.upload-tab.active i {
  color: var(--text-white);
}

.upload-panel {
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.dropzone {
  border: 2px dashed var(--input-border);
  border-radius: 8px;
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;
}

.dropzone:hover {
  border-color: var(--primary-color);
  background-color: rgba(74, 107, 255, 0.02);
}

.dropzone i {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.dropzone h3 {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.browse-btn {
  color: var(--primary-color);
  text-decoration: underline;
  cursor: pointer;
}

.url-input-group {
  display: flex;
  gap: 0.5rem;
}

.url-input-group input {
  flex: 1;
}

.proceed-btn-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.preview-header h3 {
  margin: 0;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--secondary-color);
  transition: color 0.3s;
}

.action-btn:hover {
  color: var(--error-color);
}

.preview-content {
  background-color: rgba(245, 247, 255, 0.6);
  border-radius: 8px;
  padding: 1.5rem;
  max-height: 700px;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.preview-actions {
  display: flex;
  justify-content: center;
}

/* ===== Source Item Styles ===== */
.source-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.source-item:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.source-icon {
  font-size: 1.25rem;
  color: var(--primary-color);
  margin-right: 1rem;
  width: 24px;
  text-align: center;
}

.source-info {
  flex: 1;
  overflow: hidden;
}

.source-name {
  font-weight: 600;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.source-details {
  font-size: 0.85rem;
  color: var(--secondary-color);
}

.source-actions {
  display: flex;
  gap: 0.5rem;
}

.delete-source {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  transition: color 0.2s;
  padding: 0.25rem;
  border-radius: 4px;
}

.delete-source:hover {
  color: var(--error-color);
  background-color: rgba(220, 53, 69, 0.1);
}

.source-checkbox {
  margin-right: 0.75rem;
}

/* Specific styling for the sources list */
.sources-list-body {
  background-color: rgba(245, 247, 255, 0.6);
}

.sources-list-header {
  border-bottom: 1px solid rgba(222, 226, 230, 0.5);
}

/* Improved Source Item Styling */
.sources-list-body .source-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 10px;
  padding: 1rem 1.25rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.sources-list-body .source-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(74, 107, 255, 0.2);
}

.sources-list-body .source-item:last-child {
  margin-bottom: 0;
}

.sources-list-body .source-icon {
  background-color: rgba(74, 107, 255, 0.1);
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  color: var(--primary-color);
  font-size: 1.25rem;
}

.sources-list-body .source-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sources-list-body .source-name {
  font-weight: 600;
  margin: 0;
  font-size: 1.1rem;
  color: var(--text-color);
}

.sources-list-body .source-details {
  margin-top: 0.25rem;
  font-size: 0.85rem;
  color: var(--secondary-color);
}

.sources-list-body .source-meta {
  padding: 0.35rem 0.75rem;
  background-color: rgba(245, 247, 255, 0.8);
  border-radius: 20px;
  font-size: 0.85rem;
  color: var(--secondary-color);
  font-weight: 500;
}

.sources-list-body .source-actions {
  margin-left: 1rem;
}

/* Style for the checkbox */
.source-checkbox {
  width: 18px;
  height: 18px;
  margin-right: 1.25rem;
}

.source-checkbox:checked {
  accent-color: var(--primary-color);
}

/* ===== Responsive Styles ===== */
@media (max-width: 992px) {
  .panels-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .panels-container {
    grid-template-columns: 1fr;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .main-nav {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 250px;
    background-color: white;
    flex-direction: column;
    align-items: flex-start;
    padding: 2rem;
    transform: translateX(100%);
    transition: transform 0.3s;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1001;
  }

  .main-nav.active {
    transform: translateX(0);
  }

  .nav-container {
    margin: 0 0 2rem 0;
    width: 100%;
  }

  .nav-links {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .nav-cta {
    width: 100%;
    text-align: center;
  }

  .upload-tabs {
    flex-direction: column;
    align-items: stretch;
  }

  .upload-tab {
    flex-direction: row;
    justify-content: center;
  }

  .upload-tab i {
    margin: 0 0.5rem 0 0;
  }
}

@media (max-width: 576px) {
  .upload-panel {
    padding: 1.5rem 1rem;
  }

  .dropzone {
    padding: 2rem 1rem;
  }

  .url-input-group {
    flex-direction: column;
  }

  .export-options {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn-export {
    width: 100%;
  }
}

/* Source Items in Upload Page */
.upload-container .sources-list-container {
  border: 1px solid var(--input-border);
  border-radius: 8px;
  overflow: hidden;
  margin: 1.5rem 0;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.upload-container .sources-list-header {
  background-color: rgba(245, 247, 255, 0.9);
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--input-border);
  font-weight: 500;
}

.upload-container .sources-list-body {
  padding: 0.75rem;
}

.upload-container .sources-count {
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.upload-container .source-item {
  margin-bottom: 0.5rem;
  border-left: 3px solid var(--primary-color);
}

.upload-container .source-item:last-child {
  margin-bottom: 0;
}

/* Content Preview Styles */
#preview-section {
  position: relative;
  background-color: var(--card-bg);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--input-border);
}

.preview-header h3 {
  font-size: 1.25rem;
  margin: 0;
  font-weight: 600;
}

#extracted-content {
  padding: 0;
  max-height: 500px;
  overflow-y: auto;
  background-color: var(--bg-color);
  border-radius: 0 0 10px 10px;
}

.raw-markdown-content {
  margin: 0;
  padding: 1.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--text-color);
  background-color: var(--bg-color);
  border: none;
  overflow-x: auto;
}

/* Add syntax highlighting for markdown headings */
.raw-markdown-content {
  counter-reset: line;
}

.raw-markdown-content h3,
.raw-markdown-content h2,
.raw-markdown-content h1,
.raw-markdown-content strong {
  color: var(--primary-color);
}

/* Improve readability of code blocks if present */
.raw-markdown-content code,
.raw-markdown-content pre {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-size: 0.9em;
}

/* Markdown Content Styling */
.preview-content pre {
  margin: 0;
  padding: 0;
  background: transparent;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
}

.preview-content h3 {
  color: var(--primary-color);
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  border-bottom: 1px solid var(--input-border);
  padding-bottom: 0.25rem;
}

.preview-content hr {
  border: none;
  border-top: 1px dashed var(--input-border);
  margin: 1.5rem 0;
}

/* Improve markdown content display for wider containers */
.markdown-content {
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
  padding: 2rem;
  margin: 0;
  line-height: 1.6;
  font-size: 0.95rem;
  background-color: var(--bg-color);
  color: var(--text-color);
  border: none;
  width: 100%;
  overflow-x: auto;
  max-width: 100%;
  box-sizing: border-box;
}

#extracted-content {
  margin: 0;
  padding: 0;
  max-height: 700px;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

#preview-section {
  position: relative;
}

#preview-section .preview-header {
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--input-border);
  padding: 1rem 1.5rem;
}

#preview-section .preview-content {
  padding: 0;
  background-color: var(--bg-color);
  border-radius: 0 0 8px 8px;
  border: none;
}

/* Source selection layout */
.sources-selection-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
}

.sources-selection-header .source-count {
  font-size: 0.9rem;
  background-color: rgba(74, 107, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 50px;
}

.sources-selection-header .selection-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Add styling for custom queries */
.custom-query-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 3px solid var(--primary-color);
  transition: all 0.2s ease;
}

.custom-query-item:hover {
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.custom-query-item .query-text {
  flex: 1;
  font-size: 0.9rem;
  color: var(--text-color);
  line-height: 1.4;
  margin-right: 0.75rem;
  word-break: break-word;
}

.custom-query-item .query-remove {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.custom-query-item .query-remove:hover {
  color: var(--error-color);
  background-color: rgba(220, 53, 69, 0.1);
}

/* Dataset Page Styles */
/* Remove shadow effects from all containers */
.dataset-panel,
.dataset-container,
.panels-container,
.sources-list-container,
.model-option,
.keyword-tag,
.custom-query-item,
.qa-pair,
.selected-dataset-container,
.loading-keywords,
.loading-sources,
.dataset-preview,
.dataset-type-options,
.form-check,
.api-key-container,
.export-options button {
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    filter: none !important;
    text-shadow: none !important;
}

/* Base styles for a modern look */
:root {
    --panel-bg: rgba(255, 255, 255, 0.9);
    --panel-border: rgba(230, 234, 240, 0.8);
    --panel-radius: 10px;
    --primary: #4a6bff;
    --primary-light: rgba(74, 107, 255, 0.1);
    --text-main: #2a2f45;
    --text-secondary: #6c757d;
    --spacing-xs: 0.3rem;
    --spacing-sm: 0.6rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.4rem;
}

/* Compact panel layout */
.dataset-panel {
    border: 1px solid var(--panel-border) !important;
    border-radius: var(--panel-radius) !important;
    background-color: var(--panel-bg) !important;
    transition: transform 0.15s ease, border-color 0.2s ease;
    max-height: 450px;
    overflow-y: auto;
    padding: var(--spacing-md);
}

/* Override for preview panel to remove scroll */
#preview-panel {
    max-height: none;
    overflow-y: visible;
}

.dataset-panel:hover {
    border-color: rgba(74, 107, 255, 0.25) !important;
    transform: translateY(-2px);
}

/* Modern, compact headers with accent color */
.dataset-panel h2 {
    color: var(--text-main);
    font-size: 1rem;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--panel-border);
}

.dataset-panel h2 i {
    color: var(--primary-color);
}

.dataset-panel p {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
}

/* Optimized grid layout */
.panels-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin: 0 auto;
    max-width: 1350px;
}

/* Compact dataset type options */
.dataset-type-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-xs);
    max-height: 190px;
    overflow-y: auto;
}

/* Modern radio buttons with minimalist design */
.form-check {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--panel-border) !important;
    border-radius: 6px;
    padding: var(--spacing-sm);
    transition: all 0.15s ease;
    margin-bottom: var(--spacing-xs);
    cursor: pointer;
}

.form-check:hover {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: rgba(74, 107, 255, 0.2) !important;
}

.form-check-input:checked + .form-check-label {
    color: var(--primary-color);
    font-weight: 500;
}

.form-check-label {
    cursor: pointer;
    font-size: 0.85rem;
    padding-left: var(--spacing-xs);
}

/* Sleek source selection */
.sources-list-container {
    border: 1px solid var(--panel-border) !important;
    border-radius: 6px;
    overflow: hidden;
}

.sources-list-header {
    background-color: rgba(245, 247, 255, 0.5);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.85rem;
    font-weight: 500;
}

.sources-list-body {
    max-height: 150px;
    overflow-y: auto;
    padding: var(--spacing-sm);
}

/* Minimalist model selection */
.model-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.model-option {
    background-color: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--panel-border) !important;
    border-radius: 6px;
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all 0.15s ease;
}

.model-option:hover {
    background-color: rgba(255, 255, 255, 0.95);
    border-color: rgba(74, 107, 255, 0.2) !important;
}

.model-option.selected {
    border-color: var(--primary-color) !important;
    background-color: rgba(74, 107, 255, 0.05);
}

.model-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.model-icon {
    width: 18px;
    height: 18px;
}

.model-name {
    font-size: 0.9rem;
    font-weight: 500;
}

.model-description {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    text-align: center;
}

/* Sleek form inputs */
.form-control, .form-select {
    border: 1px solid rgba(222, 226, 230, 0.8);
    border-radius: 6px;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.85rem;
    background-color: rgba(255, 255, 255, 0.8);
}

.form-control:focus, .form-select:focus {
    border-color: rgba(74, 107, 255, 0.4);
    box-shadow: 0 0 0 0.15rem rgba(74, 107, 255, 0.1);
}

/* Optimized custom query section */
.custom-query-container {
    margin-bottom: var(--spacing-sm);
}

.custom-query-list {
    max-height: 120px;
    overflow-y: auto;
    margin-bottom: var(--spacing-sm);
}

.query-input-group {
    display: flex;
    gap: var(--spacing-sm);
}

.query-input-group textarea {
    border-radius: 6px;
    padding: var(--spacing-sm);
    min-height: 60px;
}

/* Elegant keyword tags */
.keyword-tag {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: 30px;
    padding: 0.25rem 0.7rem;
    font-size: 0.8rem;
    margin: 0.2rem;
    display: inline-block;
    cursor: pointer;
    transition: all 0.15s ease;
}

.keyword-tag:hover {
    background-color: rgba(74, 107, 255, 0.2);
}

/* Refined scrollbars */
.dataset-panel::-webkit-scrollbar,
.sources-list-body::-webkit-scrollbar,
.dataset-type-options::-webkit-scrollbar,
.custom-query-list::-webkit-scrollbar {
    width: 4px;
}

.dataset-panel::-webkit-scrollbar-track,
.sources-list-body::-webkit-scrollbar-track,
.dataset-type-options::-webkit-scrollbar-track,
.custom-query-list::-webkit-scrollbar-track {
    background: transparent;
}

.dataset-panel::-webkit-scrollbar-thumb,
.sources-list-body::-webkit-scrollbar-thumb,
.dataset-type-options::-webkit-scrollbar-thumb,
.custom-query-list::-webkit-scrollbar-thumb {
    background: rgba(180, 180, 180, 0.4);
    border-radius: 10px;
}

.dataset-panel::-webkit-scrollbar-thumb:hover,
.sources-list-body::-webkit-scrollbar-thumb:hover,
.dataset-type-options::-webkit-scrollbar-thumb:hover,
.custom-query-list::-webkit-scrollbar-thumb:hover {
    background: rgba(150, 150, 150, 0.6);
}

/* Preview panel full width */
#preview-panel {
    grid-column: 1 / span 2;
    max-height: none; /* Remove max-height to prevent outer container scrolling */
    overflow-y: visible; /* Make sure the outer container doesn't scroll */
}

/* Modern-styled button */
.generate-btn-container {
    text-align: center;
    margin: 2rem auto 1.5rem;
}

.generate-btn-container .btn {
    padding: 0.6rem 1.8rem;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.01em;
    transition: all 0.2s ease;
    background-color: var(--primary-color);
    border: none;
    font-size: 0.9rem;
}

.generate-btn-container .btn:hover:not(:disabled) {
    background-color: #3451d1;
    transform: translateY(-1px);
}

.generate-btn-container .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Enhance data presentation in tables/lists */
.source-item {
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    margin-bottom: 0.4rem;
    background-color: rgba(255, 255, 255, 0.6);
}

.source-info {
    font-size: 0.85rem;
}

/* Responsive layout */
@media (max-width: 992px) {
    .panels-container {
        grid-template-columns: 1fr;
    }

    #preview-panel {
        grid-column: 1;
    }

    .model-options {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Add source button in sources panel */
.sources-header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.add-source-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.6rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.15s ease;
}

.add-source-btn:hover {
    background-color: rgba(74, 107, 255, 0.2);
}

/* Modal styles */
.source-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.source-modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.source-modal {
    width: 90%;
    max-width: 600px;
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.source-modal-backdrop.show .source-modal {
    transform: translateY(0);
}

.source-modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--panel-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.source-modal-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.source-modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: color 0.15s ease;
}

.source-modal-close:hover {
    color: var(--text-main);
}

.source-modal-body {
    padding: 1.5rem;
}

.source-modal-tabs {
    display: flex;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--panel-border);
}

.source-modal-tab {
    padding: 0.6rem 1rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-secondary);
    transition: all 0.15s ease;
}

.source-modal-tab:hover {
    color: var(--text-main);
}

.source-modal-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.upload-area {
    border: 2px dashed var(--panel-border);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.2s ease, background-color 0.2s ease;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(74, 107, 255, 0.02);
}

.upload-area i {
    font-size: 2rem;
    color: var(--primary);
    margin-bottom: 1rem;
    display: block;
}

.upload-area p {
    margin-bottom: 0.5rem;
}

.upload-area .browse {
    color: var(--primary);
    text-decoration: underline;
    cursor: pointer;
}

#file-input {
    display: none;
}

.url-area {
    display: none;
}

.text-area {
    display: none;
}

.text-area textarea {
    resize: vertical;
    min-height: 200px;
    font-family: var(--font-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    padding: 0.8rem;
}

.text-options {
    margin-top: 1rem;
}

.url-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.url-input-group input {
    flex: 1;
}

.source-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--panel-border);
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.no-sources-message {
    margin-top: 1rem;
    text-align: center;
    padding: 1.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.no-sources-message i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
    color: #ddd;
}

.no-sources-message p {
    margin-bottom: 1rem;
}

.qa-content {
    margin-bottom: 1rem;
}

.thinking-process-container {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    padding-top: 1rem;
}

.thinking-process-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s;
}

.thinking-process-header:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.thinking-process-header i.fa-brain {
    color: #4a6bff;
}

.thinking-process-header i.fa-chevron-down {
    margin-left: auto;
    transition: transform 0.3s;
}

.thinking-process-content {
    margin-top: 0.5rem;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.thinking-process-content pre {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #333;
}

/* Add styles for specific dataset types */
.qa-question, .qa-answer {
    margin-bottom: 0.5rem;
}

.qa-question strong, .qa-answer strong {
    color: #4a6bff;
}

.qa-source {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
}

/* Fallback QA Pair Styles */
.fallback-pair {
    border: 2px solid #ffc107 !important;
    background-color: rgba(255, 248, 225, 0.3) !important;
    position: relative;
}

.fallback-indicator {
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: #ffc107;
    color: #000;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: bold;
    z-index: 1;
}

.fallback-warning {
    display: flex;
    background-color: #fff8e1;
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    align-items: center;
    gap: 15px;
}

.fallback-warning .warning-icon {
    font-size: 1.5rem;
    color: #ffc107;
}

.fallback-warning .warning-message h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: #e65100;
    font-size: 1rem;
}

.fallback-warning .warning-message p {
    margin: 0;
    color: #333;
    font-size: 0.9rem;
}

/* Styles for specific dataset types */
.qa-steps, .qa-similarities, .qa-differences, .qa-relationship, .qa-context, .qa-explanation {
    margin-top: 0.5rem;
}

.qa-steps strong, .qa-similarities strong, .qa-differences strong,
.qa-relationship strong, .qa-context strong, .qa-explanation strong {
    color: #4a6bff;
}

/* Code snippet styling */
.qa-question pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    border-radius: 4px;
    overflow-x: auto;
    margin: 0.5rem 0;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 300px;
  max-width: 400px;
  padding: 1rem;
  background-color: var(--bg-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 9999;
  border-left: 4px solid var(--primary-color);
  animation: slideIn 0.3s ease-out;
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--error-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-icon {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-success .notification-icon {
  color: var(--success-color);
}

.notification-error .notification-icon {
  color: var(--error-color);
}

.notification-warning .notification-icon {
  color: var(--warning-color);
}

.notification-content {
  flex: 1;
  font-size: 0.95rem;
  color: var(--text-color);
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Make sure containers maintain their width */
.card,
.card-body,
.upload-panel,
.sources-list-container {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  transition: none !important;
}

/* Ensure processing state doesn't affect layout */
#process-sources.disabled {
  opacity: 0.7;
  position: relative;
  min-width: 180px;
}

#process-sources.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
}

.global-thinking-process {
    border: 2px solid rgba(74, 107, 255, 0.3);
    border-radius: 8px;
    padding: 20px;
    background-color: rgba(74, 107, 255, 0.08);
    margin-top: 15px;
    margin-bottom: 25px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.global-thinking-process .thinking-process-btn {
    font-weight: bold;
    background-color: rgba(74, 107, 255, 0.2);
    border: 1px solid rgba(74, 107, 255, 0.3);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 0 auto;
    transition: all 0.2s ease;
    color: #4a6bff;
}

.global-thinking-process .thinking-process-btn:hover {
    background-color: rgba(74, 107, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(74, 107, 255, 0.2);
}

.global-thinking-process .thinking-process-btn i {
    color: #4a6bff;
}

/* Styles from admin_synthetic_upload.html */
.main-heading {
    text-align: center;
    color: #003366;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 2.5rem 0;
    position: relative;
    border-bottom: 1px solid rgba(0,0,0,0.07);
    padding-bottom: 2.5rem;
    border-radius: 8px;
}

.main-heading::after {
    content: '';
    display: block;
    width: 180px;
    height: 4px;
    background-color: #003366;
    margin: 10px auto 0;
}

.upload-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.upload-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.upload-tab {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-tab:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.upload-tab.active {
    background: #0D47A1;
    color: white;
}

.upload-tab i {
    font-size: 1.5rem;
}

.upload-panel {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.dropzone {
    border: 2px dashed rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.dropzone:hover {
    border-color: #0D47A1;
    background: rgba(13, 71, 161, 0.02);
}

.dropzone i {
    font-size: 3rem;
    color: #0D47A1;
    margin-bottom: 1rem;
}

.form-control {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0D47A1;
    box-shadow: 0 0 0 2px rgba(13, 71, 161, 0.1);
}

.btn-primary {
    background: #0D47A1;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #1565C0;
    transform: translateY(-2px);
}

.sources-list-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.sources-list-header {
    background-color: rgba(13, 71, 161, 0.05);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sources-list-body {
    padding: 1.5rem;
}

#preview-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.preview-header {
    background: rgba(13, 71, 161, 0.05);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.preview-content {
    padding: 1.5rem;
    background: rgba(13, 71, 161, 0.02);
}

.url-input-group {
    display: flex;
    gap: 1rem;
}

.text-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

.model-option.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
    position: relative;
}

.coming-soon-badge {
    background-color: #6bc4ff;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 8px;
    display: inline-block;
}
