// Admin Layout Management
const AdminLayout = {
    // State
    state: {
        sidebarCollapsed: false,
        isMobile: window.innerWidth <= 768
    },

    // Elements
    elements: {
        body: document.body,
        sidebarToggle: document.getElementById('sidebarToggle'),
        sidebar: document.querySelector('.sidebar'),
        mainContent: document.querySelector('.main-content'),
        navLinks: null,
        tooltips: []
    },

    // Initialize
    init() {
        this.initElements();
        this.initEventListeners();
        this.initDropdowns();
        this.loadSavedState();
        this.handleInitialResize();
    },

    // Initialize DOM elements
    initElements() {
        this.elements = {
            body: document.body,
            sidebarToggle: document.getElementById('sidebarToggle'),
            sidebar: document.querySelector('.sidebar'),
            mainContent: document.querySelector('.main-content'),
            navLinks: document.querySelectorAll('.nav-link')
        };
    },

    // Attach event listeners
    initEventListeners() {
        // Toggle button click
        this.elements.sidebarToggle?.addEventListener('click', () => {
            this.toggleSidebar();
        });

        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Click outside to close (mobile only)
        document.addEventListener('click', (e) => {
            if (this.state.isMobile && 
                !this.elements.sidebar.contains(e.target) && 
                !this.elements.sidebarToggle.contains(e.target)) {
                this.closeSidebar();
            }
        });
    },

    // Initialize Bootstrap tooltips
    initializeTooltips() {
        this.elements.navLinks.forEach(link => {
            if (link.getAttribute('data-bs-toggle') === 'tooltip') {
                const tooltip = new bootstrap.Tooltip(link);
                this.elements.tooltips.push(tooltip);
            }
        });
    },

    // Initialize Bootstrap dropdowns
    initDropdowns() {
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(dropdownTriggerEl => {
            new bootstrap.Dropdown(dropdownTriggerEl);
        });
    },

    // Toggle sidebar
    toggleSidebar() {
        this.elements.body.classList.toggle('sidebar-collapsed');
        this.saveState();
    },

    // Update layout based on state
    updateLayout() {
        this.elements.sidebar.classList.toggle('collapsed', this.state.sidebarCollapsed);
        this.elements.mainContent.classList.toggle('expanded', this.state.sidebarCollapsed);
        
        // Update tooltips
        this.updateTooltips();
        
        // Update toggle button icon
        this.elements.sidebarToggle.querySelector('i').classList.toggle('bi-list');
        this.elements.sidebarToggle.querySelector('i').classList.toggle('bi-x-lg');
    },

    // Check mobile state
    checkMobileState() {
        const wasMobile = this.state.isMobile;
        this.state.isMobile = window.innerWidth <= 768;

        if (wasMobile !== this.state.isMobile) {
            this.handleResponsiveChange();
        }
    },

    // Handle responsive layout changes
    handleResponsiveChange() {
        if (this.state.isMobile) {
            this.elements.sidebar.classList.remove('collapsed');
            this.elements.mainContent.classList.remove('expanded');
            this.elements.sidebar.classList.add('mobile');
        } else {
            this.elements.sidebar.classList.remove('mobile');
            this.updateLayout();
        }
    },

    // Close sidebar (mobile only)
    closeSidebar() {
        if (this.state.isMobile) {
            this.elements.sidebar.classList.remove('show');
        }
    },

    // Update tooltips based on sidebar state
    updateTooltips() {
        this.elements.tooltips.forEach(tooltip => {
            if (this.state.sidebarCollapsed && !this.state.isMobile) {
                tooltip.enable();
            } else {
                tooltip.disable();
            }
        });
    },

    // Save state to localStorage
    saveState() {
        localStorage.setItem('sidebarCollapsed', 
            this.elements.body.classList.contains('sidebar-collapsed'));
    },

    // Load saved state
    loadSavedState() {
        const sidebarState = localStorage.getItem('sidebarCollapsed');
        if (sidebarState === 'true') {
            this.elements.body.classList.add('sidebar-collapsed');
        }
    },

    // Debounce helper
    debounce(func, wait) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    },

    handleResize() {
        if (window.innerWidth <= 768) {
            this.elements.body.classList.add('sidebar-collapsed');
        } else if (!localStorage.getItem('sidebarCollapsed')) {
            this.elements.body.classList.remove('sidebar-collapsed');
        }
    },

    handleInitialResize() {
        this.handleResize();
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    AdminLayout.init();
}); 