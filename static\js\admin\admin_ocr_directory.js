// Global variables
let currentPath = '/';
let currentSelectionType = 'folder';
let selectedPath = null;
let nasBrowserModal = null;
let manualFolderPath = null;
let verificationImageFolderPath = null;
let verificationLabelFilePath = null;

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modal
    nasBrowserModal = new bootstrap.Modal(document.getElementById('nasBrowserModal'));

    // Get data container element
    const dataContainer = document.getElementById('initialDataContainer');

    // Set initial paths from data attributes
    manualFolderPath = getDataAttributeValue(dataContainer, 'data-manual-folder');
    verificationImageFolderPath = getDataAttributeValue(dataContainer, 'data-verification-image-folder');
    verificationLabelFilePath = getDataAttributeValue(dataContainer, 'data-verification-label-folder');

    // Update UI with initial paths
    updateUIWithPaths();
});

// Helper function to safely get data attribute values
function getDataAttributeValue(element, attributeName) {
    if (!element || !element.hasAttribute(attributeName)) return null;

    const value = element.getAttribute(attributeName);
    if (!value || value === '') return null;

    try {
        return JSON.parse(value);
    } catch (e) {
        console.error(`Error parsing ${attributeName}:`, e);
        return value; // Return the raw value if parsing fails
    }
}

// Toggle annotation mode
function toggleAnnotationMode(mode) {
    const manualForm = document.getElementById('manualLabellingForm');
    const verificationForm = document.getElementById('verificationForm');

    if (mode === 'manual') {
        manualForm.style.display = 'block';
        verificationForm.style.display = 'none';
    } else if (mode === 'verification') {
        manualForm.style.display = 'none';
        verificationForm.style.display = 'block';
    }
}

// Open NAS browser for different selection modes
function openNasBrowser(selectionMode) {
    // Reset browser state
    currentPath = '/';
    selectedPath = null;

    // Set selection type based on the mode
    if (selectionMode === 'verification-label-file') {
        currentSelectionType = 'file';
        document.getElementById('selectionModeInfo').textContent = 'Browse to select a JSON label file';
        document.getElementById('selectPathButton').textContent = 'Select File';
    } else {
        currentSelectionType = 'folder';
        document.getElementById('selectionModeInfo').textContent = 'Browse to select a folder';
        document.getElementById('selectPathButton').textContent = 'Select Folder';
    }

    // Store the selection mode
    document.getElementById('nasBrowserModal').setAttribute('data-selection-mode', selectionMode);

    // Reset UI elements
    document.getElementById('nasBreadcrumb').innerHTML = '<li class="breadcrumb-item"><a href="#" onclick="browseNasDirectory(\'/\')">Root</a></li>';
    document.getElementById('currentSelectionPath').textContent = '/';

    // Load root directory and show modal
    browseNasDirectory('/');
    nasBrowserModal.show();
}

// Browse NAS directory
function browseNasDirectory(path) {
    currentPath = path;
    const loadingElement = document.getElementById('nasLoading');
    const contentsElement = document.getElementById('nasDirectoryContents');

    // Show loading state
    loadingElement.style.display = 'flex';
    contentsElement.innerHTML = '';
    document.getElementById('currentSelectionPath').textContent = path;

    // Fetch directory contents
    fetch(`/admin/browse-nas-directory?path=${encodeURIComponent(path)}`)
        .then(response => response.json())
        .then(data => {
            loadingElement.style.display = 'none';
            if (data.success) {
                updateBreadcrumb(path);
                displayDirectoryContents(data.items, path);
            } else {
                showError(contentsElement, data.message || 'Error loading directory contents');
            }
        })
        .catch(error => {
            loadingElement.style.display = 'none';
            showError(contentsElement, error.message);
        });
}

// Update breadcrumb navigation
function updateBreadcrumb(path) {
    const breadcrumb = document.getElementById('nasBreadcrumb');
    breadcrumb.innerHTML = '<li class="breadcrumb-item"><a href="#" onclick="browseNasDirectory(\'/\')">Root</a></li>';

    if (path === '/') return;

    const segments = path.split('/').filter(segment => segment);
    let currentPath = '';

    segments.forEach((segment, index) => {
        currentPath += '/' + segment;
        const li = document.createElement('li');
        li.className = 'breadcrumb-item';

        if (index === segments.length - 1) {
            li.className += ' active';
            li.setAttribute('aria-current', 'page');
            li.textContent = segment;
        } else {
            const a = document.createElement('a');
            a.href = '#';
            a.textContent = segment;
            a.onclick = () => {
                browseNasDirectory(currentPath);
                return false;
            };
            li.appendChild(a);
        }

        breadcrumb.appendChild(li);
    });
}

// Display directory contents
function displayDirectoryContents(items, path) {
    const container = document.getElementById('nasDirectoryContents');
    container.innerHTML = '';

    // Add parent directory navigation
    if (path !== '/') {
        addParentDirectoryItem(container, path);
    }

    // Handle empty directory
    if (!items || items.length === 0) {
        showEmptyMessage(container);
        return;
    }

    // Sort and display items
    const sortedItems = sortDirectoryItems(items);
    sortedItems.forEach(item => {
        const isDirectory = item.type === 'directory';
        const isJsonFile = !isDirectory && item.name.toLowerCase().endsWith('.json');

        if (shouldDisplayItem(isDirectory, isJsonFile)) {
            addDirectoryItem(container, item, path, isDirectory, isJsonFile);
        }
    });
}

// Helper functions for directory display
function addParentDirectoryItem(container, path) {
    const parentPath = path.substring(0, path.lastIndexOf('/')) || '/';
    const parentItem = createDirectoryItem('Parent Directory', 'bi-arrow-up-circle', () => browseNasDirectory(parentPath));
    container.appendChild(parentItem);
}

function showEmptyMessage(container) {
    const emptyMessage = document.createElement('div');
    emptyMessage.className = 'alert alert-light mt-3';
    emptyMessage.textContent = 'This directory is empty';
    container.appendChild(emptyMessage);
}

function sortDirectoryItems(items) {
    return [...items].sort((a, b) => {
        const aIsDir = a.type === 'directory';
        const bIsDir = b.type === 'directory';
        return aIsDir === bIsDir ? a.name.localeCompare(b.name) : aIsDir ? -1 : 1;
    });
}

function shouldDisplayItem(isDirectory, isJsonFile) {
    return isDirectory || (currentSelectionType === 'file' && isJsonFile);
}

function createDirectoryItem(name, iconClass, onClick) {
    const item = document.createElement('div');
    item.className = 'nas-item folder';
    item.innerHTML = `
        <div style="display: flex; align-items: center; width: 100%;">
            <i class="bi ${iconClass}"></i>
            <span class="nas-item-name">${name}</span>
        </div>
    `;
    if (onClick) {
        item.onclick = onClick;
    }
    return item;
}

// Select current path
function selectCurrentPath() {
    const selectionMode = document.getElementById('nasBrowserModal').getAttribute('data-selection-mode');

    if (currentSelectionType === 'folder') {
        selectedPath = currentPath;
    }

    if (!selectedPath) {
        alert('Please select a valid path first');
        return;
    }

    updateSelectedPath(selectionMode);
    nasBrowserModal.hide();
}

// Update selected path based on mode
function updateSelectedPath(selectionMode) {
    switch (selectionMode) {
        case 'manual-folder':
            manualFolderPath = selectedPath;
            document.getElementById('manualFolderPath').textContent = selectedPath;
            document.getElementById('setManualFolderBtn').disabled = false;
            break;
        case 'verification-image-folder':
            verificationImageFolderPath = selectedPath;
            document.getElementById('verificationImageFolderPath').textContent = selectedPath;
            updateVerificationButtonState();
            break;
        case 'verification-label-file':
            verificationLabelFilePath = selectedPath;
            document.getElementById('verificationLabelFilePath').textContent = selectedPath;
            updateVerificationButtonState();
            break;
    }
}

// Update verification button state
function updateVerificationButtonState() {
    const btn = document.getElementById('setVerificationFoldersBtn');
    btn.disabled = !(verificationImageFolderPath && verificationLabelFilePath);
}

// Set manual folder
function setManualFolder() {
    if (!manualFolderPath) {
        alert('Please select a folder first');
        return;
    }

    const button = document.getElementById('setManualFolderBtn');
    handleFolderSelection('/admin/select-manual-folder', {
        folder_path: manualFolderPath
    }, button);
}

// Set verification folders
function setVerificationFolders() {
    if (!verificationImageFolderPath || !verificationLabelFilePath) {
        alert('Please select both an image folder and a label file');
        return;
    }

    const button = document.getElementById('setVerificationFoldersBtn');
    handleFolderSelection('/admin/select-verification-folders', {
        image_folder: verificationImageFolderPath,
        label_file: verificationLabelFilePath
    }, button);
}

// Handle folder selection AJAX requests
async function handleFolderSelection(url, data, button) {
    const originalText = button.innerHTML;
    try {
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Setting...';
        button.disabled = true;

        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => formData.append(key, value));

        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        const result = await response.json();

        if (result.success) {
            alert(result.message);
            window.location.reload();
        } else {
            alert('Error: ' + result.message);
        }
    } catch (error) {
        alert('Error: ' + error.message);
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Update UI with paths
function updateUIWithPaths() {
    if (manualFolderPath) {
        document.getElementById('manualFolderPath').textContent = manualFolderPath;
        document.getElementById('setManualFolderBtn').disabled = false;
    }

    if (verificationImageFolderPath) {
        document.getElementById('verificationImageFolderPath').textContent = verificationImageFolderPath;
    }

    if (verificationLabelFilePath) {
        document.getElementById('verificationLabelFilePath').textContent = verificationLabelFilePath;
    }

    updateVerificationButtonState();
}

// Show instruction tabs
function showManualTab() {
    document.getElementById('manual-tab').click();
}

function showVerificationTab() {
    document.getElementById('verification-tab').click();
}

// Error display helper
function showError(element, message) {
    element.innerHTML = `
        <div class="alert alert-danger">
            Error: ${message}
        </div>
    `;
}

function addDirectoryItem(container, item, path, isDirectory, isJsonFile) {
    const itemPath = path === '/' ? `/${item.name}` : `${path}/${item.name}`;
    const iconClass = isDirectory ? 'bi-folder-fill' : (isJsonFile ? 'bi-file-earmark-text' : 'bi-file');

    const itemElement = document.createElement('div');
    itemElement.className = `nas-item ${isDirectory ? 'folder' : 'file'} ${isJsonFile ? 'json' : ''}`;
    itemElement.innerHTML = `
        <div style="display: flex; align-items: center; width: 100%;">
            <i class="bi ${iconClass}"></i>
            <span class="nas-item-name">${item.name}</span>
        </div>
    `;

    // Add click handler
    itemElement.addEventListener('click', () => {
        if (isDirectory) {
            browseNasDirectory(itemPath);
        } else if (isJsonFile && currentSelectionType === 'file') {
            selectedPath = itemPath;
            document.getElementById('currentSelectionPath').textContent = itemPath;
        }
    });

    container.appendChild(itemElement);
}