$(document).ready(function() {
    // Set progress bar width from data attribute
    $('.progress-bar').each(function() {
        var progress = $(this).data('progress');
        $(this).css('width', progress + '%');
    });
    
    // Check Google Drive configuration when page loads or when drive option is selected
    function checkGoogleDriveConfig() {
        if ($('#storage-destination').val() === 'google-drive') {
            $.ajax({
                url: CHECK_DRIVE_URL,
                type: 'GET',
                success: function(response) {
                    if (!response.connected) {
                        showMergeResult('Google Drive is not configured or connected. Please configure it in the Admin Dashboard before using this option.', 'warning');
                        // Revert to current storage
                        $('#storage-destination').val('current');
                    }
                },
                error: function() {
                    showMergeResult('Error checking Google Drive configuration', 'danger');
                    // Revert to current storage
                    $('#storage-destination').val('current');
                }
            });
        }
    }
    
    // Check when storage option changes
    $('#storage-destination').on('change', function() {
        checkGoogleDriveConfig();
    });
    
    // Initial check on page load
    checkGoogleDriveConfig();
    
    // Handle dataset dropdown changes
    $('#dataset-select').on('change', function() {
        var selected = $(this).val();
        window.location.href = AUDITOR_TRACKING_URL + "?dataset=" + encodeURIComponent(selected);
    });
    
    // Handle merge button click
    $('#merge-json-btn').on('click', function() {
        var dataset = $('#dataset-select').val();
        if (!dataset) {
            showMergeResult('Please select a dataset first', 'warning');
            return;
        }
        
        // Get the selected storage destination
        var storageDestination = $('#storage-destination').val();
        
        // Validate storage selection
        if (storageDestination === 'google-drive') {
            // For Google Drive, double-check connection first
            $.ajax({
                url: CHECK_DRIVE_URL,
                type: 'GET',
                async: false,
                success: function(response) {
                    if (!response.connected) {
                        showMergeResult('Google Drive is not connected. Please configure it in the Admin Dashboard.', 'warning');
                        return false;
                    }
                },
                error: function() {
                    showMergeResult('Error checking Google Drive configuration', 'danger');
                    return false;
                }
            });
        } else if (storageDestination !== 'current') {
            showMergeResult('This storage destination is coming soon. Please use Current Storage option.', 'info');
            return;
        }
        
        // Show processing state
        var $button = $(this);
        $button.prop('disabled', true);
        var originalContent = $button.html();
        $button.html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Processing...');
        showMergeResult('Merging JSON files, please wait...', 'info');
        
        // Send AJAX request to merge JSON files
        $.ajax({
            url: MERGE_DATASET_URL,
            type: 'POST',
            data: { 
                dataset_name: dataset,
                storage_destination: storageDestination
            },
            success: function(response) {
                // Reset button state
                $button.html(originalContent);
                $button.prop('disabled', !response.success);
                
                if (response.success) {
                    // Show success message with details
                    var message = '<i class="bi bi-check-circle me-2"></i>' + response.message + 
                        '<div class="mt-2 small"><strong>File:</strong> ' + response.output_file + 
                        ' | <strong>Entries:</strong> ' + response.total_entries;
                    
                    // Add Drive link if available
                    if (response.drive_link) {
                        message += ' | <a href="' + response.drive_link + '" target="_blank">View in Google Drive</a>';
                    }
                    
                    message += '</div>';
                    showMergeResult(message, 'success');
                    
                    // Refresh the page after 2 seconds to show updated dataset status
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    // Show error message
                    showMergeResult('<i class="bi bi-exclamation-triangle me-2"></i>' + response.message, 'danger');
                }
            },
            error: function(xhr, status, error) {
                // Reset button state
                $button.html(originalContent);
                $button.prop('disabled', false);
                showMergeResult('<i class="bi bi-x-circle me-2"></i>Error: ' + error, 'danger');
            }
        });
    });
    
    // Function to show merge result alert
    function showMergeResult(message, type) {
        var alert = $('#merge-result');
        alert.attr('class', 'alert alert-' + type);
        alert.html(message);
        alert.fadeIn();
        
        // Auto-hide alert after 5 seconds for success and info messages
        if (type === 'success' || type === 'info') {
            setTimeout(function() {
                alert.fadeOut();
            }, 5000);
        }
    }
    
    // Auto-refresh data every 5 minutes
    setTimeout(function() {
        var url = AUDITOR_TRACKING_URL + "?refresh=1";
        if (SELECTED_DATASET) {
            url += "&dataset=" + encodeURIComponent(SELECTED_DATASET);
        }
        window.location.href = url;
    }, 300000); // 5 minutes in milliseconds
}); 