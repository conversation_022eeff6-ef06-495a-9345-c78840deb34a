/**
 * Image Browser - JavaScript functionality
 * Handles image modal opening, closing, and zoom/navigation functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const imageCards = document.querySelectorAll('.image-card');
    const imageModal = document.getElementById('imageModal');
    const modalTitle = document.getElementById('imageModalLabel');
    const modalImage = document.getElementById('modalImage');
    const prevBtn = document.getElementById('prevImageBtn');
    const nextBtn = document.getElementById('nextImageBtn');
    
    // Variables
    let currentZoom = 1;
    const zoomStep = 0.15;
    const minZoom = 0.5;
    const maxZoom = 5;
    let currentImageIndex = 0;
    const imagesList = [...imageCards];
    
    // Initialize Bootstrap modal
    const modal = new bootstrap.Modal(imageModal);
    
    // Add click event to all image cards
    imageCards.forEach((card, index) => {
        card.addEventListener('click', function() {
            openImageModal(index);
        });
    });
    
    // Function to open modal with specific image
    function openImageModal(index) {
        currentImageIndex = index;
        const card = imagesList[index];
        
        const imagePath = card.getAttribute('data-image-path');
        const imageName = card.getAttribute('data-image-name');
        
        // Set modal content
        modalTitle.textContent = imageName;
        modalImage.src = imagePath;
        modalImage.alt = imageName;
        
        // Reset zoom when opening modal
        resetZoom();
        
        // Update navigation buttons
        updateNavigationButtons();
        
        // Open modal
        modal.show();
    }
    
    // Update navigation button states
    function updateNavigationButtons() {
        prevBtn.style.display = currentImageIndex > 0 ? 'flex' : 'none';
        nextBtn.style.display = currentImageIndex < imagesList.length - 1 ? 'flex' : 'none';
    }
    
    // Navigation buttons
    prevBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        if (currentImageIndex > 0) {
            openImageModal(currentImageIndex - 1);
        }
    });
    
    nextBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        if (currentImageIndex < imagesList.length - 1) {
            openImageModal(currentImageIndex + 1);
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (!imageModal.classList.contains('show')) return;
        
        if (e.key === 'ArrowLeft') {
            prevBtn.click();
        } else if (e.key === 'ArrowRight') {
            nextBtn.click();
        } else if (e.key === 'Escape') {
            modal.hide();
        }
    });
    
    // Zoom functionality
    function updateZoom() {
        modalImage.style.transform = `scale(${currentZoom})`;
    }
    
    function resetZoom() {
        currentZoom = 1;
        updateZoom();
    }
    
    // Mouse wheel zoom
    modalImage.addEventListener('wheel', function(e) {
        e.preventDefault(); // Prevent page scrolling
        
        if (e.deltaY < 0) {
            // Zoom in on scroll up
            if (currentZoom < maxZoom) {
                currentZoom = Math.min(currentZoom + zoomStep, maxZoom);
                updateZoom();
            }
        } else {
            // Zoom out on scroll down
            if (currentZoom > minZoom) {
                currentZoom = Math.max(currentZoom - zoomStep, minZoom);
                updateZoom();
            }
        }
    }, { passive: false });
    
    // Double-click to reset zoom
    modalImage.addEventListener('dblclick', resetZoom);
    
    // Reset zoom when modal is closed
    imageModal.addEventListener('hidden.bs.modal', resetZoom);
    
    // Handle back button functionality
    const backButton = document.querySelector('.back-btn');
    if (backButton) {
        backButton.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            if (href) {
                window.location = '/ocr-directory';
            }
        });
    }
}); 