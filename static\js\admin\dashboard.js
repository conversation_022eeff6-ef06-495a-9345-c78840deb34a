// Routes are defined in the HTML template

$(document).ready(function() {
    // Check for OAuth parameters in URL
    const urlParams = new URLSearchParams(window.location.search);
    const oauthSuccess = urlParams.get('drive_auth_success');
    const oauthError = urlParams.get('drive_auth_error');
    const errorMsg = urlParams.get('error_msg');

    // Open Google Drive modal if redirected with OAuth params
    if (oauthSuccess === 'true' || oauthError === 'true') {
        $('#googleDriveConnectionModal').modal('show');
    }

    // Handle success case
    if (oauthSuccess === 'true') {
        $('#driveConnectionResultArea')
            .removeClass('d-none alert-danger')
            .addClass('alert-success')
            .html('<i class="bi bi-check-circle me-1"></i> Google Drive connected successfully!');

        // Check connection status
        checkGoogleDriveConnection();

        // Close modal after delay
        setTimeout(function() {
            $('#googleDriveConnectionModal').modal('hide');
        }, 3000);
    }

    // Handle error case
    if (oauthError === 'true') {
        $('#driveConnectionResultArea')
            .removeClass('d-none alert-success')
            .addClass('alert-danger')
            .html('<i class="bi bi-exclamation-triangle me-1"></i> ' +
                  (errorMsg || 'Authentication failed. Please try again.'));
    }

    // Check initial connection status
    checkNasConnection();
    checkGoogleDriveConnection();

    // Open connection modal
    $('#btnConnectNas').click(function() {
        $('#connectionResultArea').addClass('d-none');
        $('#nasConnectionModal').modal('show');
    });

    // Disconnect NAS
    $(document).on('click', '#btnDisconnectNas', function() {
        const $btn = $(this);

        // Show loading state
        $btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i>');

        // Send disconnect request
        $.ajax({
            url: routes.disconnect_nas,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    // Update connection status
                    $('#nasConnectionStatus').html('<span class="text-secondary"><i class="bi bi-dash-circle me-1"></i> Not connected</span>');
                    // Hide folders section
                    $('#nasFoldersSection').addClass('d-none');
                    // Hide OCR directory button
                    $('#ocrDirectorySection').addClass('d-none');
                    // Disable auditor folder browse button
                    $('#browseAuditorFolder').prop('disabled', true);
                } else {
                    // Show error
                    $('#nasConnectionStatus').html(
                        `<span class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i> Error: ${response.message}</span>`
                    );
                }
            },
            error: function(_, __, error) {
                // Show error
                $('#nasConnectionStatus').html(
                    `<span class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i> Server error: ${error}</span>`
                );
            }
        });
    });

    // Test NAS connection
    $('#btnTestNasConnection').click(function() {
        const $btn = $(this);
        const $form = $('#nasConnectionForm');
        const $resultArea = $('#connectionResultArea');

        // Validate form
        if (!$form[0].checkValidity()) {
            $form[0].reportValidity();
            return;
        }

        // Collect form data
        const formData = {
            nas_type: $('#nasType').val(),
            nas_url: $('#nasUrl').val(),
            nas_username: $('#nasUsername').val(),
            nas_password: $('#nasPassword').val()
        };

        // Disable button and show loading
        $btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i> Connecting...');
        $resultArea.removeClass('d-none alert-success alert-danger').addClass('alert-info').html('Testing connection...');

        // Send request
        $.ajax({
            url: routes.connect_nas,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    $resultArea.removeClass('alert-info alert-danger').addClass('alert-success');
                    $resultArea.html(`<i class="bi bi-check-circle me-1"></i> ${response.message}`);

                    // Update connection status outside modal with disconnect button
                    $('#nasConnectionStatus').html(
                        `<span class="text-success"><i class="bi bi-check-circle me-1"></i> Connected</span>
                         ${response.using_custom ? '(Custom credentials)' : ''}
                         <button id="btnDisconnectNas" class="btn btn-sm btn-outline-danger ms-2">
                            <i class="bi bi-plug me-1"></i>Disconnect</button>`
                    );

                    // Enable auditor folder browse button
                    $('#browseAuditorFolder').prop('disabled', false);
                    
                    // Show OCR directory button immediately
                    $('#ocrDirectorySection').removeClass('d-none');

                    // Auto close the modal after 2 seconds
                    setTimeout(function() {
                        $('#nasConnectionModal').modal('hide');

                        // Load NAS folders
                        loadNasFolders();
                    }, 2000);
                } else {
                    $resultArea.removeClass('alert-info alert-success').addClass('alert-danger');
                    $resultArea.html(`<i class="bi bi-exclamation-triangle me-1"></i> ${response.message}`);
                }
            },
            error: function(_, __, error) {
                $resultArea.removeClass('alert-info alert-success').addClass('alert-danger');
                $resultArea.html(`<i class="bi bi-exclamation-triangle me-1"></i> Server error: ${error}`);
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html('<i class="bi bi-plug-fill me-1"></i> Connect');
            }
        });
    });

    // Function to load NAS folders
    function loadNasFolders() {
        $.ajax({
            url: routes.get_nas_folders,
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    if (response.folders && response.folders.length > 0) {
                        // Show folders section
                        $('#nasFoldersSection').removeClass('d-none');

                        // Populate folders list
                        const $foldersList = $('#nasFoldersList');
                        $foldersList.empty();

                        response.folders.forEach(function(folder) {
                            const folderName = folder.name || folder.path;
                            $foldersList.append(
                                `<div class="nas-folder mb-1">
                                    <i class="bi bi-folder-fill text-warning me-1"></i> ${folderName}
                                </div>`
                            );
                        });
                    }
                } else {
                    console.error("Error loading NAS folders:", response.message);
                }
            },
            error: function(_, __, error) {
                console.error("Error loading NAS folders:", error);
            }
        });
    }

    // Auditor folder browse functionality
    let currentPath = '/';
    let folderBrowserOpen = false;

    $('#browseAuditorFolder').click(function() {
        // Create folder browser modal if it doesn't exist
        if (!folderBrowserOpen) {
            createFolderBrowserModal();
        }

        // Load initial directory
        loadNasDirectory(currentPath);

        // Show the modal
        $('#folderBrowserModal').modal('show');
    });

    function createFolderBrowserModal() {
        // Create the modal HTML
        const modalHtml = `
            <div class="modal fade" id="folderBrowserModal" tabindex="-1" aria-labelledby="folderBrowserModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="folderBrowserModalLabel">Browse NAS Folders</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb" id="folderBreadcrumb">
                                    <li class="breadcrumb-item active" aria-current="page">Root</li>
                                </ol>
                            </nav>

                            <div id="folderLoading" class="text-center py-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>

                            <div id="folderContents" class="list-group" style="max-height: 400px; overflow-y: auto;"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="selectFolderBtn">Select Folder</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Append to body
        $('body').append(modalHtml);

        // Handle folder selection
        $('#selectFolderBtn').click(function() {
            // Set the input value
            $('#auditorImageFolder').val(currentPath);
            // Enable save button
            $('#saveAuditorFolderBtn').prop('disabled', false);
            // Close the modal
            $('#folderBrowserModal').modal('hide');
        });

        folderBrowserOpen = true;
    }

    function loadNasDirectory(path) {
        // Show loading
        $('#folderLoading').show();
        $('#folderContents').hide();

        // Make AJAX request
        $.ajax({
            url: routes.browse_nas_directory,
            type: 'GET',
            data: { path: path },
            success: function(response) {
                if (response.success) {
                    // Update current path
                    currentPath = response.path;

                    // Update breadcrumb
                    updateBreadcrumb(currentPath);

                    // Populate folder contents
                    populateFolderContents(response.items, response.parent_path);
                } else {
                    showFolderBrowserError(response.message);
                }
            },
            error: function(_, __, error) {
                showFolderBrowserError(`Server error: ${error}`);
            },
            complete: function() {
                // Hide loading
                $('#folderLoading').hide();
                $('#folderContents').show();
            }
        });
    }

    function updateBreadcrumb(path) {
        const $breadcrumb = $('#folderBreadcrumb');
        $breadcrumb.empty();

        if (path === '/') {
            $breadcrumb.append('<li class="breadcrumb-item active" aria-current="page">Root</li>');
            return;
        }

        // Add root
        $breadcrumb.append('<li class="breadcrumb-item"><a href="#" data-path="/">Root</a></li>');

        // Split path
        const parts = path.split('/').filter(part => part);
        let currentPath = '';

        // Add intermediate paths
        parts.forEach((part, index) => {
            currentPath += '/' + part;

            if (index === parts.length - 1) {
                $breadcrumb.append(`<li class="breadcrumb-item active" aria-current="page">${part}</li>`);
            } else {
                $breadcrumb.append(`<li class="breadcrumb-item"><a href="#" data-path="${currentPath}">${part}</a></li>`);
            }
        });

        // Add click handler for breadcrumb links
        $breadcrumb.find('a').click(function(e) {
            e.preventDefault();
            const clickedPath = $(this).data('path');
            loadNasDirectory(clickedPath);
        });
    }

    function populateFolderContents(items, parentPath) {
        const $contents = $('#folderContents');
        $contents.empty();

        // Add parent directory link if not at root
        if (currentPath !== '/') {
            $contents.append(`
                <a href="#" class="list-group-item list-group-item-action" data-path="${parentPath}">
                    <i class="bi bi-arrow-up-circle me-2"></i> ..
                </a>
            `);
        }

        // Sort items: directories first, then files
        const sortedItems = items.sort((a, b) => {
            const typeA = a.type || '';
            const typeB = b.type || '';

            // If types are different, directories come first
            if (typeA.toLowerCase().includes('dir') && !typeB.toLowerCase().includes('dir')) return -1;
            if (!typeA.toLowerCase().includes('dir') && typeB.toLowerCase().includes('dir')) return 1;

            // Otherwise sort by name
            return (a.name || '').localeCompare(b.name || '');
        });

        // Add items
        sortedItems.forEach(item => {
            const itemName = item.name || '';
            const itemType = item.type || '';
            const isDirectory = itemType.toLowerCase().includes('dir') || itemType.toLowerCase() === 'directory';
            const itemPath = item.path || `${currentPath}/${itemName}`;

            if (isDirectory) {
                $contents.append(`
                    <a href="#" class="list-group-item list-group-item-action" data-path="${itemPath}">
                        <i class="bi bi-folder me-2"></i> ${itemName}
                    </a>
                `);
            }
        });

        // Add click handler for directory items
        $contents.find('a').click(function(e) {
            e.preventDefault();
            const clickedPath = $(this).data('path');
            loadNasDirectory(clickedPath);
        });
    }

    function showFolderBrowserError(message) {
        const $contents = $('#folderContents');
        $contents.empty().append(`
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i> ${message}
            </div>
        `);
    }

    // Save Auditor Image Folder Path
    $('#saveAuditorFolderBtn').click(function() {
        const $btn = $(this);
        const folderPath = $('#auditorImageFolder').val();

        if (!folderPath) {
            alert('Please select a folder path');
            return;
        }

        // Disable button and show loading
        $btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i> Saving...');

        // Send request
        $.ajax({
            url: routes.set_auditor_image_folder,
            type: 'POST',
            data: { folder_path: folderPath },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert(response.message);
                    // Disable save button
                    $btn.prop('disabled', true);
                } else {
                    // Show error
                    alert('Error: ' + response.message);
                }
            },
            error: function(_, __, error) {
                // Show error
                alert('Server error: ' + error);
            },
            complete: function() {
                // Re-enable button
                $btn.prop('disabled', false).html('<i class="bi bi-save me-1"></i> Save');
            }
        });
    });

    function checkNasConnection() {
        $.ajax({
            url: routes.check_nas_connection,
            type: 'GET',
            success: function(response) {
                if (response.success && response.connected) {
                    $('#nasConnectionStatus').html(
                        `<span class="text-success"><i class="bi bi-check-circle me-1"></i> Connected</span>
                         ${response.using_custom ? '(Custom credentials)' : ''}
                         <button id="btnDisconnectNas" class="btn btn-sm btn-outline-danger ms-2">
                            <i class="bi bi-plug me-1"></i>Disconnect</button>`
                    );

                    // Enable auditor folder browse button
                    $('#browseAuditorFolder').prop('disabled', false);

                    // Show OCR directory button
                    $('#ocrDirectorySection').removeClass('d-none');

                    // Load NAS folders
                    loadNasFolders();
                } else {
                    $('#nasConnectionStatus').html('<span class="text-secondary"><i class="bi bi-dash-circle me-1"></i> Not connected</span>');
                    // Hide folders section
                    $('#nasFoldersSection').addClass('d-none');
                    // Hide OCR directory button
                    $('#ocrDirectorySection').addClass('d-none');
                    // Disable auditor folder browse button
                    $('#browseAuditorFolder').prop('disabled', true);
                }
            },
            error: function() {
                $('#nasConnectionStatus').html('<span class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i> Error checking connection</span>');
                // Hide OCR directory button on error
                $('#ocrDirectorySection').addClass('d-none');
            }
        });
    }

    // Open Google Drive connection modal
    $('#btnConnectGoogleDrive').click(function() {
        $('#driveConnectionResultArea').addClass('d-none');
        $('#googleDriveConnectionModal').modal('show');
    });

    // Configure Google Drive connection
    $('#btnConfigureGoogleDrive').click(function() {
        const $btn = $(this);
        // Form validation is handled by the browser
        const clientId = $('#driveClientId').val();
        const clientSecret = $('#driveClientSecret').val();
        const folderId = $('#driveFolderId').val();

        // Validate input
        if (!clientId || !clientSecret) {
            $('#driveConnectionResultArea')
                .removeClass('d-none alert-success')
                .addClass('alert-danger')
                .html('<i class="bi bi-exclamation-triangle me-1"></i> Client ID and Client Secret are required');
            return;
        }

        // Show loading state
        $btn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i> Connecting...');

        // Send configuration to server
        $.ajax({
            url: routes.configure_google_drive,
            type: 'POST',
            data: {
                client_id: clientId,
                client_secret: clientSecret,
                folder_id: folderId
            },
            success: function(response) {
                $btn.prop('disabled', false).html('<i class="bi bi-plug-fill me-1"></i> Configure & Connect');

                if (response.success) {
                    $('#driveConnectionResultArea')
                        .removeClass('d-none alert-danger')
                        .addClass('alert-success')
                        .html('<i class="bi bi-check-circle me-1"></i> ' + response.message);

                    // If we need to redirect for auth
                    if (response.auth_url) {
                        // Add a listener for auth completion message from popup
                        window.addEventListener('message', function(event) {
                            if (event.data === 'google-auth-success') {
                                // Auth succeeded, update UI
                                $('#driveConnectionResultArea')
                                    .html('<i class="bi bi-check-circle me-1"></i> Authentication successful! Checking connection...');

                                // Check connection status after a short delay
                                setTimeout(function() {
                                    checkGoogleDriveConnection();
                                    $('#googleDriveConnectionModal').modal('hide');
                                }, 2000);
                            } else if (event.data === 'google-auth-error') {
                                // Auth failed, update UI
                                $('#driveConnectionResultArea')
                                    .removeClass('alert-success alert-info')
                                    .addClass('alert-danger')
                                    .html('<i class="bi bi-exclamation-triangle me-1"></i> Authentication failed. Please try again.');
                            }
                        });

                        // Open auth URL in popup
                        const authWindow = window.open(response.auth_url, 'GoogleAuthWindow',
                            'width=800,height=600,status=yes,scrollbars=yes');

                        $('#driveConnectionResultArea')
                            .removeClass('alert-danger alert-success')
                            .addClass('alert-info')
                            .html('<i class="bi bi-info-circle me-1"></i> Please complete authentication in the opened window. This page will update automatically when done.');

                        // Check if popup was blocked
                        if (!authWindow || authWindow.closed || typeof authWindow.closed == 'undefined') {
                            $('#driveConnectionResultArea')
                                .removeClass('alert-success alert-info')
                                .addClass('alert-warning')
                                .html('<i class="bi bi-exclamation-triangle me-1"></i> Popup blocked! Please allow popups and <a href="' + response.auth_url + '" target="_blank">click here</a> to open the auth page.');
                        }
                    }

                    // Update status if successful
                    if (response.connected) {
                        updateGoogleDriveStatus(true);
                        setTimeout(function() {
                            $('#googleDriveConnectionModal').modal('hide');
                        }, 2000);
                    }
                } else {
                    $('#driveConnectionResultArea')
                        .removeClass('d-none alert-success')
                        .addClass('alert-danger')
                        .html('<i class="bi bi-exclamation-triangle me-1"></i> ' + response.message);
                }
            },
            error: function(_, __, error) {
                $btn.prop('disabled', false).html('<i class="bi bi-plug-fill me-1"></i> Configure & Connect');
                $('#driveConnectionResultArea')
                    .removeClass('d-none alert-success')
                    .addClass('alert-danger')
                    .html('<i class="bi bi-exclamation-triangle me-1"></i> Server error: ' + error);
            }
        });
    });

    // Check Google Drive connection status
    function checkGoogleDriveConnection() {
        $.ajax({
            url: routes.check_google_drive_connection,
            type: 'GET',
            success: function(response) {
                updateGoogleDriveStatus(response.connected);

                // If connected, also update the modal status
                if (response.connected) {
                    $('#driveConnectionResultArea')
                        .removeClass('d-none alert-danger')
                        .addClass('alert-success')
                        .html('<i class="bi bi-check-circle me-1"></i> Google Drive is connected!');
                }
            },
            error: function() {
                updateGoogleDriveStatus(false);
            }
        });
    }

    // Update Google Drive connection status UI
    function updateGoogleDriveStatus(isConnected) {
        const statusEl = $('#googleDriveConnectionStatus');
        const btnEl = $('#btnConnectGoogleDrive');

        if (isConnected) {
            statusEl.html('<span class="text-success"><i class="bi bi-check-circle me-1"></i> Connected</span> ' +
                '<a href="#" id="testDriveConnection" class="ms-2 small text-decoration-none">Test connection</a>');
            btnEl.html('<i class="bi bi-arrow-repeat me-1"></i> Reconnect');

            // Add click handler for test connection
            $('#testDriveConnection').click(function(e) {
                e.preventDefault();
                statusEl.html('<span class="text-info"><i class="spinner-border spinner-border-sm me-1"></i> Testing connection...</span>');

                // Make test request
                $.ajax({
                    url: routes.check_google_drive_connection,
                    type: 'GET',
                    success: function(response) {
                        if (response.connected) {
                            statusEl.html('<span class="text-success"><i class="bi bi-check-circle me-1"></i> Connection verified!</span> ' +
                                '<a href="#" id="testDriveConnection" class="ms-2 small text-decoration-none">Test again</a>');

                            // Re-attach the click handler
                            $('#testDriveConnection').click(function(e) {
                                e.preventDefault();
                                updateGoogleDriveStatus(true);
                            });

                            setTimeout(function() {
                                updateGoogleDriveStatus(true);
                            }, 3000);
                        } else {
                            statusEl.html('<span class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i> Connection test failed!</span>');
                            setTimeout(function() {
                                updateGoogleDriveStatus(false);
                            }, 3000);
                        }
                    },
                    error: function() {
                        statusEl.html('<span class="text-danger"><i class="bi bi-exclamation-triangle me-1"></i> Connection test failed!</span>');
                        setTimeout(function() {
                            updateGoogleDriveStatus(false);
                        }, 3000);
                    }
                });
            });
        } else {
            statusEl.html('<span class="text-secondary"><i class="bi bi-dash-circle me-1"></i> Not connected</span>');
            btnEl.html('<i class="bi bi-plug-fill me-1"></i> Connect to Google Drive');
        }
    }

    // Reset Google Drive configuration
    $('#btnResetGoogleDrive').click(function() {
        if (!confirm('Are you sure you want to reset Google Drive configuration? This will remove all current settings and credentials.')) {
            return;
        }

        $.ajax({
            url: routes.reset_google_drive,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    // Update status
                    updateGoogleDriveStatus(false);
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(_, __, error) {
                alert('Server error: ' + error);
            }
        });
    });
    
    // Handle OCR directory button click
    $('#btnGoToOcrDirectory').click(function() {
        window.location.href = '/ocr-directory';
    });
});