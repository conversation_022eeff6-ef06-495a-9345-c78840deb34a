// Data Sources Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initAnimations();

    // Add event listeners
    addEventListeners();
});

/**
 * Add event listeners to interactive elements
 */
function addEventListeners() {
    // Process guide close button
    const processGuideCloseBtn = document.querySelector('.close-flash');
    if (processGuideCloseBtn) {
        processGuideCloseBtn.addEventListener('click', closeFlash);
    }

    // Coming soon message close button
    const comingSoonCloseBtn = document.querySelector('#coming-soon-message .close-flash');
    if (comingSoonCloseBtn) {
        comingSoonCloseBtn.addEventListener('click', closeComingSoon);
    }

    // Source card info buttons - using more reliable selectors
    const sourceCards = document.querySelectorAll('.source-card');
    sourceCards.forEach(card => {
        const infoButton = card.querySelector('.btn-info');
        if (infoButton) {
            infoButton.addEventListener('click', function() {
                if (card.querySelector('.fab.fa-telegram')) {
                    showTelegramInfo();
                } else if (card.querySelector('.fab.fa-twitter')) {
                    showTwitterComingSoon();
                } else if (card.querySelector('.fas.fa-database')) {
                    showDatabaseComingSoon();
                }
            });
        }
    });

    // Add click event to "Coming Soon" badges
    const comingSoonBadges = document.querySelectorAll('.status-badge.coming-soon');
    comingSoonBadges.forEach(function(badge) {
        badge.addEventListener('click', function() {
            const card = this.closest('.source-card');
            if (card.querySelector('.fab.fa-twitter')) {
                showTwitterComingSoon();
            } else if (card.querySelector('.fas.fa-database')) {
                showDatabaseComingSoon();
            }
        });
    });

    // Disabled buttons
    const disabledButtons = document.querySelectorAll('.btn-disabled');
    disabledButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            showToast('This feature is currently disabled', 'info');
        });
    });

    // Hover effect for active buttons
    const activeButtons = document.querySelectorAll('.btn:not(.btn-disabled)');
    activeButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.classList.add('btn-hover');
        });
        
        button.addEventListener('mouseleave', function() {
            this.classList.remove('btn-hover');
        });
    });
}

/**
 * Show a toast notification
 * @param {string} message - Message to display
 * @param {string} type - Type of notification (success, error, info)
 */
function showToast(message, type = 'info') {
    // Create the toast element if it doesn't exist
    let toast = document.getElementById('toast-notification');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-notification';
        document.body.appendChild(toast);
    }
    
    // Clear previous toasts
    toast.innerHTML = '';
    
    // Set the message and type
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="${type === 'success' ? 'fas fa-check-circle' : type === 'error' ? 'fas fa-exclamation-circle' : 'fas fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close">&times;</button>
    `;
    
    // Show the toast
    toast.style.display = 'flex';
    
    // Add click event to close button
    const closeBtn = toast.querySelector('.toast-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            toast.style.display = 'none';
        });
    }
    
    // Automatically hide after 3 seconds
    setTimeout(() => {
        toast.style.display = 'none';
    }, 3000);
}

/**
 * Show Telegram information message
 */
function showTelegramInfo() {
    document.getElementById('flash-message').style.display = 'flex';
}

/**
 * Show Twitter coming soon message
 */
function showTwitterComingSoon() {
    document.getElementById('coming-soon-icon').className = 'fab fa-twitter';
    document.getElementById('coming-soon-title').textContent = 'Twitter Integration';
    document.getElementById('coming-soon-text').textContent = 'This feature is currently under development and will be available soon.';
    document.getElementById('coming-soon-message').style.display = 'flex';
}

/**
 * Show Database coming soon message
 */
function showDatabaseComingSoon() {
    document.getElementById('coming-soon-icon').className = 'fas fa-database';
    document.getElementById('coming-soon-title').textContent = 'Database Connection';
    document.getElementById('coming-soon-text').textContent = 'This feature is currently under development and will be available soon.';
    document.getElementById('coming-soon-message').style.display = 'flex';
}

/**
 * Close the coming soon message
 */
function closeComingSoon() {
    document.getElementById('coming-soon-message').style.display = 'none';
}

/**
 * Close flash message
 */
function closeFlash() {
    document.getElementById('flash-message').style.display = 'none';
}

// Add CSS for toast animations
const style = document.createElement('style');
style.textContent = `
    .btn-source.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .toast {
        min-width: 250px;
    }

    .toast-header {
        padding: 0.5rem 0.75rem;
    }

    .toast-body {
        padding: 0.75rem;
    }
`;
document.head.appendChild(style);
