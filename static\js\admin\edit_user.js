document.addEventListener('DOMContentLoaded', function() {
    const role = document.getElementById('role');
    const annotatorOptions = document.querySelectorAll('.annotator-options');

    // Show/hide annotator options based on role
    function toggleAnnotatorOptions() {
        if (role.value === 'annotator') {
            annotatorOptions.forEach(option => {
                if (option) {
                    option.style.display = 'block';
                    // Add animation class instead of inline style
                    option.classList.add('fade-in-animation');
                }
            });
        } else {
            annotatorOptions.forEach(option => {
                if (option) {
                    option.style.display = 'none';
                    option.classList.remove('fade-in-animation');
                }
            });
        }
    }

    // Initial toggle
    if (role) {
        toggleAnnotatorOptions();

        // Toggle on role change
        role.addEventListener('change', toggleAnnotatorOptions);
    }

    // Add input focus effects using classes instead of inline styles
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(control => {
        // Add focus effect to input group
        control.addEventListener('focus', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.classList.add('input-group-focus');
            }
        });

        // Remove focus effect
        control.addEventListener('blur', function() {
            const inputGroup = this.closest('.input-group');
            if (inputGroup) {
                inputGroup.classList.remove('input-group-focus');
            }
        });
    });

    // Add animation to save button using classes instead of inline styles
    const saveButton = document.querySelector('button[type="submit"]');
    if (saveButton) {
        saveButton.addEventListener('mouseenter', function() {
            this.classList.add('btn-hover-effect');
        });

        saveButton.addEventListener('mouseleave', function() {
            this.classList.remove('btn-hover-effect');
        });
    }
});