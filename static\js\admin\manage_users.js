// User Management Module
const UserManagement = {
    // State management
    state: {
        currentRole: 'all',
        currentStatus: 'all',
        searchTerm: '',
        totalCounts: {
            admin: 0,
            annotator: 0,
            auditor: 0
        }
    },

    // DOM Elements
    elements: {},

    // Initialize the module
    init() {
        this.initializeElements();
        this.calculateTotalCounts();
        this.attachEventListeners();
    },

    // Initialize DOM elements
    initializeElements() {
        this.elements = {
            searchInput: document.getElementById('userSearchInput'),
            clearSearchBtn: document.getElementById('clearSearchBtn'),
            userRows: document.querySelectorAll('.user-row'),
            roleCards: document.querySelectorAll('.role-card'),
            statusButtons: document.querySelectorAll('[data-filter-status]'),
            noResultsMessage: document.getElementById('noResultsMessage'),
            totalUsers: document.querySelector('.badge.bg-primary-soft'),
            roleCounters: {
                admin: document.querySelector('.role-card.admin .role-count'),
                annotator: document.querySelector('.role-card.annotator .role-count'),
                auditor: document.querySelector('.role-card.auditor .role-count')
            }
        };
    },

    // Calculate total counts for each role
    calculateTotalCounts() {
        this.elements.userRows.forEach(row => {
            const role = row.dataset.role;
            if (this.state.totalCounts.hasOwnProperty(role)) {
                this.state.totalCounts[role]++;
            }
        });

        // Set the initial counts in the UI
        Object.entries(this.state.totalCounts).forEach(([role, count]) => {
            if (this.elements.roleCounters[role]) {
                this.elements.roleCounters[role].textContent = count;
            }
        });
    },

    // Attach event listeners
    attachEventListeners() {
        // Search input with debounce
        this.elements.searchInput?.addEventListener('input', 
            this.debounce(() => this.handleSearch(), 300)
        );

        // Clear search
        this.elements.clearSearchBtn?.addEventListener('click', () => {
            this.clearSearch();
        });

        // Role card clicks
        this.elements.roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const role = e.currentTarget.dataset.role;
                this.filterByRole(role);
            });
        });

        // Status filter clicks
        this.elements.statusButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const status = e.currentTarget.dataset.filterStatus;
                this.filterByStatus(status);
            });
        });
    },

    // Handle search
    handleSearch() {
        const searchTerm = this.elements.searchInput.value.toLowerCase().trim();
        this.state.searchTerm = searchTerm;
        this.applyFilters();
        this.toggleClearButton();
    },

    // Clear search
    clearSearch() {
        this.elements.searchInput.value = '';
        this.state.searchTerm = '';
        this.applyFilters();
        this.toggleClearButton();
    },

    // Toggle clear button visibility
    toggleClearButton() {
        this.elements.clearSearchBtn.classList.toggle('d-none', !this.state.searchTerm);
    },

    // Filter by role
    filterByRole(role) {
        this.state.currentRole = role === this.state.currentRole ? 'all' : role;
        
        // Update active state of role cards
        this.elements.roleCards.forEach(card => {
            card.classList.toggle('active', card.dataset.role === this.state.currentRole);
        });

        this.applyFilters();
    },

    // Filter by status
    filterByStatus(status) {
        this.state.currentStatus = status;
        
        // Update active state of status buttons
        this.elements.statusButtons.forEach(button => {
            const isActive = button.dataset.filterStatus === status;
            button.classList.toggle('active', isActive);
            this.updateStatusButtonStyle(button, isActive);
        });

        this.applyFilters();
    },

    // Update status button style
    updateStatusButtonStyle(button, isActive) {
        const status = button.dataset.filterStatus;
        button.className = 'btn btn-sm ';
        
        if (isActive) {
            switch (status) {
                case 'active':
                    button.classList.add('btn-success');
                    break;
                case 'suspended':
                    button.classList.add('btn-danger');
                    break;
                default:
                    button.classList.add('btn-secondary');
            }
        } else {
            button.classList.add('btn-outline-secondary');
        }
    },

    // Apply all filters
    applyFilters() {
        let visibleCount = 0;
        
        this.elements.userRows.forEach(row => {
            const isVisible = this.shouldShowRow(row);
            row.classList.toggle('d-none', !isVisible);
            if (isVisible) visibleCount++;
        });

        this.updateNoResults(visibleCount);
        this.updateFilteredCount(visibleCount);
    },

    // Check if row should be visible
    shouldShowRow(row) {
        const matchesSearch = this.matchesSearch(row);
        const matchesRole = this.state.currentRole === 'all' || row.dataset.role === this.state.currentRole;
        const matchesStatus = this.state.currentStatus === 'all' || row.dataset.status === this.state.currentStatus;

        return matchesSearch && matchesRole && matchesStatus;
    },

    // Check if row matches search term
    matchesSearch(row) {
        if (!this.state.searchTerm) return true;

        const searchableContent = [
            row.querySelector('.user-username')?.textContent,
            row.querySelector('.user-fullname')?.textContent,
            row.querySelector('.user-email')?.textContent,
            row.querySelector('.user-role')?.textContent
        ].join(' ').toLowerCase();

        return searchableContent.includes(this.state.searchTerm);
    },

    // Update no results message
    updateNoResults(visibleCount) {
        const hasFilters = this.state.searchTerm || 
                          this.state.currentRole !== 'all' || 
                          this.state.currentStatus !== 'all';
        
        this.elements.noResultsMessage.classList.toggle('d-none', 
            visibleCount > 0 || !hasFilters
        );
    },

    // Update filtered count in the header
    updateFilteredCount(visibleCount) {
        if (this.elements.totalUsers) {
            const totalUsers = this.elements.userRows.length;
            const text = visibleCount === totalUsers ? 
                `${totalUsers} Total` : 
                `${visibleCount} of ${totalUsers}`;
            this.elements.totalUsers.textContent = text;
        }
    },

    // Debounce helper
    debounce(func, wait) {
        let timeout;
        return (...args) => {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), wait);
        };
    }
};

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', () => {
    UserManagement.init();
});
