// Show/hide loading spinner
function toggleLoading(show) {
    document.getElementById('loading-spinner').style.display = show ? 'block' : 'none';
}

// Show status message
function showStatus(message, type = 'success', duration = 3000) {
    const statusEl = document.getElementById('status-message');
    statusEl.textContent = message;
    statusEl.className = `alert alert-${type} status-message`;
    statusEl.style.display = 'block';

    // Auto-hide after duration
    setTimeout(() => {
        statusEl.style.display = 'none';
    }, duration);
}

// Handle API errors
function handleApiError(error, defaultMessage) {
    console.error(error);
    const errorMessage = error.message || defaultMessage;
    showStatus(errorMessage, 'danger', 5000);
}

// Check authentication status
async function checkAuth() {
    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/check-auth');
        const data = await response.json();

        if (data.authenticated) {
            // Redirect to channels list if already authenticated
            window.location.href = "/fetch-data/telegram/channels";
            return;
        }

        // Check session status
        await checkSessionStatus();
    } catch (error) {
        handleApiError(error, 'Error checking authentication status');
    } finally {
        toggleLoading(false);
    }
}

// Check session status and display to user
async function checkSessionStatus() {
    const sessionStatusDiv = document.getElementById('session-status');
    if (!sessionStatusDiv) return;

    sessionStatusDiv.style.display = 'block';
    sessionStatusDiv.textContent = 'Checking for existing session...';
    sessionStatusDiv.className = 'alert alert-info mb-3';

    try {
        const response = await fetch('/fetch-data/telegram/check-auth');

        if (!response.ok) {
            sessionStatusDiv.textContent = 'Error checking session status';
            sessionStatusDiv.className = 'alert alert-danger mb-3';
            return;
        }

        const data = await response.json();

        if (data.authenticated) {
            sessionStatusDiv.textContent = `Found existing session (${data.username || 'authenticated user'})`;
            sessionStatusDiv.className = 'alert alert-success mb-3';
        } else if (data.message && data.message.includes('Session exists')) {
            sessionStatusDiv.textContent = 'Found existing session but it appears invalid. You may need to reset it.';
            sessionStatusDiv.className = 'alert alert-warning mb-3';
        } else {
            sessionStatusDiv.textContent = 'No active session found. Please log in.';
            sessionStatusDiv.className = 'alert alert-secondary mb-3';
        }
    } catch (error) {
        sessionStatusDiv.textContent = 'Error checking session status';
        sessionStatusDiv.className = 'alert alert-danger mb-3';
        console.error('Error checking session status:', error);
    }
}

// Connect to Telegram
async function connectToTelegram(event) {
    event.preventDefault();
    const apiId = document.getElementById('api_id').value;
    const apiHash = document.getElementById('api_hash').value;
    const phone = document.getElementById('phone').value;

    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/connect', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ api_id: apiId, api_hash: apiHash, phone })
        });

        if (!response.ok) {
            if (response.status === 429) {
                const data = await response.json();
                showStatus(data.message || 'Rate limited by Telegram. Please try again later.', 'danger', 10000);
            } else {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Server error');
            }
            return;
        }

        const data = await response.json();

        if (data.status === 'code_sent') {
            document.getElementById('connection-form').style.display = 'none';
            document.getElementById('verification-form').style.display = 'block';
            showStatus(data.message);
        } else if (data.status === 'connected') {
            showStatus(data.message);
            window.location.href = "/fetch-data/telegram/channels";
        } else if (data.status === 'phone_required') {
            showStatus(data.message, 'warning');
        } else if (data.status === 'rate_limited') {
            showStatus(data.message, 'danger', 10000);
        }
    } catch (error) {
        handleApiError(error, 'Error connecting to Telegram');
    } finally {
        toggleLoading(false);
    }
}

// Verify code
async function verifyCode(event) {
    event.preventDefault();
    const code = document.getElementById('code').value;

    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/verify-code', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ code })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        if (data.status === 'connected') {
            showStatus(data.message);
            window.location.href = "/fetch-data/telegram/channels";
        } else if (data.status === 'password_required') {
            document.getElementById('verification-form').style.display = 'none';
            document.getElementById('password-form').style.display = 'block';
            showStatus(data.message, 'warning');
        }
    } catch (error) {
        handleApiError(error, 'Error verifying code');
    } finally {
        toggleLoading(false);
    }
}

// Verify password
async function verifyPassword(event) {
    event.preventDefault();
    const password = document.getElementById('password').value;

    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/verify-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        if (data.status === 'connected') {
            showStatus(data.message);
            window.location.href = "/fetch-data/telegram/channels";
        }
    } catch (error) {
        handleApiError(error, 'Error verifying password');
    } finally {
        toggleLoading(false);
    }
}

// Reset session
async function resetSession() {
    if (!confirm('Are you sure you want to reset your session? You will need to log in again.')) {
        return;
    }

    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/reset-session');

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        showStatus(data.message || 'Session reset', 'info');

        // Update session status display
        await checkSessionStatus();

        // Ensure UI is reset to login form
        document.getElementById('connection-form').style.display = 'block';
        document.getElementById('verification-form').style.display = 'none';
        document.getElementById('password-form').style.display = 'none';
    } catch (error) {
        handleApiError(error, 'Error resetting session');
    } finally {
        toggleLoading(false);
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('telegram-form').addEventListener('submit', connectToTelegram);
    document.getElementById('code-form').addEventListener('submit', verifyCode);
    document.getElementById('password-form-inputs').addEventListener('submit', verifyPassword);
    document.getElementById('reset-session-btn').addEventListener('click', resetSession);

    // Check authentication on page load
    checkAuth();
});