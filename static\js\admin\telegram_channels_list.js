// Global variables
let channelsData = [];
let channelAnalytics = {};
let selectedChannelId = null;
let selectedChannel = null;

// Show/hide loading spinner
function toggleLoading(show) {
    document.getElementById('loading-spinner').style.display = show ? 'block' : 'none';
}

// Show status message
function showStatus(message, type = 'success', duration = 3000) {
    const statusEl = document.getElementById('status-message');
    statusEl.textContent = message;
    statusEl.className = `alert alert-${type} status-message`;
    statusEl.style.display = 'block';

    // Auto-hide after duration
    setTimeout(() => {
        statusEl.style.display = 'none';
    }, duration);
}

// Handle API errors
function handleApiError(error, defaultMessage) {
    console.error(error);
    const errorMessage = error.message || defaultMessage;
    showStatus(errorMessage, 'danger', 5000);
}

// Check authentication status
async function checkAuth() {
    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/check-auth?refresh=1');
        const data = await response.json();

        if (!data.authenticated) {
            window.location.href = "/fetch-data/telegram-connect";
            return;
        }

        // Show username if available
        if (data.username) {
            const headerElement = document.querySelector('h1.mb-0');
            if (headerElement) {
                headerElement.innerHTML = `Telegram Channels for <strong>${data.username}</strong> <span class="channel-count-badge" id="channel-count">0</span>`;
            }
        }

        // Load channels
        await loadChannels();
    } catch (error) {
        handleApiError(error, 'Error checking authentication status');
    } finally {
        toggleLoading(false);
    }
}

// Load channels
async function loadChannels() {
    try {
        document.getElementById('channels-loading').style.display = 'block';
        document.getElementById('channels-grid').style.display = 'none';

        const response = await fetch('/fetch-data/telegram/get-channels');

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        // Hide loading
        document.getElementById('channels-loading').style.display = 'none';
        document.getElementById('channels-grid').style.display = 'grid';

        if (data.channels && data.channels.length > 0) {
            channelsData = data.channels;
            // Sort channels alphabetically by title
            channelsData.sort((a, b) => a.title.localeCompare(b.title));

            const channelsGrid = document.getElementById('channels-grid');
            channelsGrid.innerHTML = '';

            // Update channel count badge
            document.getElementById('channel-count').textContent = channelsData.length;

            channelsData.forEach(channel => {
                // Create channel card
                const card = document.createElement('div');
                card.className = 'card h-100 channel-card';
                card.setAttribute('data-channel-id', channel.id);

                // Format participants text
                const participantsText = channel.participants_count
                    ? `<span class="badge bg-secondary">${channel.participants_count} participants</span>`
                    : '';

                card.innerHTML = `
                    <div class="card-body">
                        <h5 class="card-title mb-2">${channel.title}</h5>
                        <div class="mb-3">
                            <small class="text-muted">ID: ${channel.id}</small><br>
                            ${channel.username ? `<small class="text-muted">@${channel.username}</small><br>` : ''}
                            ${participantsText}
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-sm btn-primary analyze-btn" data-channel-id="${channel.id}">
                                <i class="fas fa-chart-bar"></i> View Analysis
                            </button>
                            <a href="/fetch-data/telegram/images?channel_id=${channel.id}" class="btn btn-sm btn-success">
                                <i class="fas fa-images"></i> View Images
                            </a>
                        </div>
                    </div>
                `;
                channelsGrid.appendChild(card);

                // Add click event for the analyze button
                const analyzeBtn = card.querySelector('.analyze-btn');
                analyzeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    selectChannel(channel.id);
                });

                // Make the whole card clickable for analysis
                card.addEventListener('click', (e) => {
                    // Only if we didn't click on a button or link
                    if (!e.target.closest('button') && !e.target.closest('a')) {
                        selectChannel(channel.id);
                    }
                });
            });

            // Hide no channels message
            document.getElementById('no-channels-message').style.display = 'none';
        } else {
            // Show no channels message
            document.getElementById('channels-grid').innerHTML = '';
            document.getElementById('no-channels-message').style.display = 'block';
            document.getElementById('channel-count').textContent = '0';
        }
    } catch (error) {
        handleApiError(error, 'Error loading channels');
        document.getElementById('channels-loading').style.display = 'none';
        document.getElementById('no-channels-message').style.display = 'block';
        document.getElementById('no-channels-message').textContent = 'Error loading channels: ' + error.message;
    }
}

// Select and analyze a channel
async function selectChannel(channelId) {
    if (selectedChannelId === channelId && channelAnalytics[channelId]) {
        return;
    }

    selectedChannelId = channelId;
    selectedChannel = channelsData.find(c => c.id === channelId);

    if (!selectedChannel) return;

    // Update UI to show selected channel
    const channelCards = document.querySelectorAll('.channel-card');
    channelCards.forEach(card => {
        if (card.getAttribute('data-channel-id') == channelId) {
            card.classList.add('active');
        } else {
            card.classList.remove('active');
        }
    });

    // Update analysis title
    document.getElementById('analysis-title').textContent = `Analysis: ${selectedChannel.title}`;

    // Show loading, hide other states
    document.getElementById('no-channel-selected').style.display = 'none';
    document.getElementById('analysis-loading').style.display = 'flex';
    document.getElementById('analysis-content').style.display = 'none';

    // Check if we already have analytics data for this channel
    if (channelAnalytics[channelId]) {
        displayChannelAnalytics(channelId);
    } else {
        // Fetch and process analytics
        await analyzeChannel(channelId);
    }

    // Smooth scroll to analytics section on mobile
    if (window.innerWidth < 992) {
        document.querySelector('.analysis-container').scrollIntoView({ behavior: 'smooth' });
    }
}

// Analyze a single channel
async function analyzeChannel(channelId) {
    try {
        const channel = channelsData.find(c => c.id === channelId);
        if (!channel) return;

        showStatus(`Analyzing channel: ${channel.title}...`, 'info');

        const response = await fetch(`/fetch-data/telegram/get-channel-analytics/${channelId}`);

        if (!response.ok) {
            throw new Error(`Failed to fetch analytics for ${channel.title}`);
        }

        const channelData = await response.json();

        if (channelData.error) {
            throw new Error(channelData.error);
        }

        channelAnalytics[channelId] = {
            channelId: channelId,
            channelName: channelData.channel.title,
            channelUsername: channelData.channel.username,
            participantsCount: channelData.channel.participants_count,
            totalImages: channelData.total_images,
            dates: channelData.dates || [],
            imagesPerDate: channelData.date_image_counts || {},
            advanced_analytics: channelData.advanced_analytics || {}
        };

        displayChannelAnalytics(channelId);
        showStatus(`Analysis complete for ${channel.title}`, 'success');
    } catch (error) {
        console.error(`Error analyzing channel ${channelId}:`, error);
        showStatus(`Analysis failed: ${error.message}`, 'danger');

        document.getElementById('analysis-loading').style.display = 'none';
        document.getElementById('analysis-content').style.display = 'block';
        document.getElementById('analysis-content').innerHTML = `
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-triangle"></i> Analysis Failed</h5>
                <p>${error.message}</p>
                <button class="btn btn-sm btn-outline-danger mt-2" onclick="analyzeChannel('${channelId}')">
                    <i class="fas fa-sync-alt"></i> Retry Analysis
                </button>
            </div>
        `;
    }
}

// Display the analytics for a selected channel
function displayChannelAnalytics(channelId) {
    const channelData = channelAnalytics[channelId];
    if (!channelData) return;

    document.getElementById('analysis-loading').style.display = 'none';
    document.getElementById('analysis-content').style.display = 'block';

    document.getElementById('selected-channel-name').textContent = channelData.channelName;
    document.getElementById('selected-channel-details').textContent =
        `${channelData.channelUsername ? '@' + channelData.channelUsername + ' • ' : ''}${channelData.participantsCount || 0} participants`;

    document.getElementById('view-images-btn').onclick = () => {
        if (!channelId) return;
        window.location.href = `/fetch-data/telegram/images?channel_id=${channelId}`;
    };

    document.getElementById('total-images-stat').textContent = channelData.totalImages;

    const dates = channelData.dates || [];
    document.getElementById('active-days-stat').textContent = dates.length;

    if (dates.length > 0) {
        document.getElementById('first-date-stat').textContent = formatDateShort(dates[dates.length - 1]);
        document.getElementById('last-date-stat').textContent = formatDateShort(dates[0]);
    }

    generateDateAnalysis(channelData);
}

// Generate date-based analysis tables
function generateDateAnalysis(channelData) {
    const dates = channelData.dates || [];
    const imageCounts = channelData.imagesPerDate || {};
    const totalImages = channelData.totalImages || 0;

    if (dates.length === 0) {
        ['year', 'month', 'date'].forEach(type => {
            document.getElementById(`${type}-analysis-table`).innerHTML =
                `<tr><td colspan="3" class="text-center">No images available for this channel</td></tr>`;
        });
        return;
    }

    const yearData = {};
    const monthData = {};
    const dateData = {};

    Object.entries(imageCounts).forEach(([dateStr, count]) => {
        const date = new Date(dateStr);
        const year = date.getFullYear().toString();
        const month = `${year}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

        if (!yearData[year]) yearData[year] = 0;
        yearData[year] += count;

        if (!monthData[month]) monthData[month] = 0;
        monthData[month] += count;

        dateData[dateStr] = count;
    });

    // Generate year table
    generateYearTable(yearData, totalImages);

    // Generate month table
    generateMonthTable(monthData, totalImages);

    // Generate date table
    generateDateTable(dateData, totalImages);
}

// Generate year analysis table
function generateYearTable(yearData, totalImages) {
    const yearTable = document.getElementById('year-analysis-table');
    yearTable.innerHTML = '';

    if (Object.keys(yearData).length === 0) {
        yearTable.innerHTML = `<tr><td colspan="3" class="text-center">No yearly data available</td></tr>`;
        return;
    }

    Object.entries(yearData)
        .sort((a, b) => b[0].localeCompare(a[0]))
        .forEach(([year, count]) => {
            const percentage = ((count / totalImages) * 100).toFixed(1);
            yearTable.innerHTML += `
                <tr>
                    <td>${year}</td>
                    <td>${count}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });
}

// Generate month analysis table
function generateMonthTable(monthData, totalImages) {
    const monthTable = document.getElementById('month-analysis-table');
    monthTable.innerHTML = '';

    if (Object.keys(monthData).length === 0) {
        monthTable.innerHTML = `<tr><td colspan="3" class="text-center">No monthly data available</td></tr>`;
        return;
    }

    Object.entries(monthData)
        .sort((a, b) => b[0].localeCompare(a[0]))
        .forEach(([month, count]) => {
            const [year, monthNum] = month.split('-');
            const monthName = new Date(year, parseInt(monthNum) - 1, 1).toLocaleString('default', { month: 'long' });
            const percentage = ((count / totalImages) * 100).toFixed(1);
            monthTable.innerHTML += `
                <tr>
                    <td>${monthName} ${year}</td>
                    <td>${count}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });
}

// Generate date analysis table
function generateDateTable(dateData, totalImages) {
    const dateTable = document.getElementById('date-analysis-table');
    dateTable.innerHTML = '';

    if (Object.keys(dateData).length === 0) {
        dateTable.innerHTML = `<tr><td colspan="3" class="text-center">No daily data available</td></tr>`;
        return;
    }

    Object.entries(dateData)
        .sort((a, b) => b[0].localeCompare(a[0]))
        .forEach(([dateStr, count]) => {
            const percentage = ((count / totalImages) * 100).toFixed(1);
            dateTable.innerHTML += `
                <tr>
                    <td>${formatDateShort(dateStr)}</td>
                    <td>${count}</td>
                    <td>${percentage}%</td>
                </tr>
            `;
        });
}

// Format date for UI display
function formatDateShort(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: '2-digit' 
    });
}

// Download analytics data as CSV
function downloadAnalyticsCSV() {
    if (!selectedChannelId || !channelAnalytics[selectedChannelId]) {
        showStatus('No channel selected for export', 'warning');
        return;
    }

    try {
        toggleLoading(true);

        const channelData = channelAnalytics[selectedChannelId];
        const filename = `${channelData.channelName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_analytics.csv`;

        let csvContent = generateCSVContent(channelData);

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showStatus(`Analytics exported for ${channelData.channelName}`, 'success');
    } catch (error) {
        handleApiError(error, 'Error exporting analytics');
    } finally {
        toggleLoading(false);
    }
}

// Generate CSV content
function generateCSVContent(channelData) {
    let csvContent = 'Metric,Value\n';
    csvContent += `Channel Name,${channelData.channelName}\n`;
    csvContent += `Channel Username,${channelData.channelUsername || 'N/A'}\n`;
    csvContent += `Total Images,${channelData.totalImages}\n`;
    csvContent += `Active Days,${channelData.dates.length}\n`;

    const dates = channelData.dates || [];
    const imageCounts = channelData.imagesPerDate || {};
    const totalImages = channelData.totalImages || 0;

    if (dates.length > 0) {
        const yearData = {};
        const monthData = {};

        Object.entries(imageCounts).forEach(([dateStr, count]) => {
            const date = new Date(dateStr);
            const year = date.getFullYear().toString();
            const month = `${year}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;

            if (!yearData[year]) yearData[year] = 0;
            yearData[year] += count;

            if (!monthData[month]) monthData[month] = 0;
            monthData[month] += count;
        });

        csvContent += '\nYear Breakdown\nYear,Images,Percentage\n';
        Object.entries(yearData)
            .sort((a, b) => b[0].localeCompare(a[0]))
            .forEach(([year, count]) => {
                const percentage = ((count / totalImages) * 100).toFixed(1);
                csvContent += `${year},${count},${percentage}%\n`;
            });

        csvContent += '\nMonth Breakdown\nMonth,Images,Percentage\n';
        Object.entries(monthData)
            .sort((a, b) => b[0].localeCompare(a[0]))
            .forEach(([month, count]) => {
                const [year, monthNum] = month.split('-');
                const monthName = new Date(year, parseInt(monthNum) - 1, 1).toLocaleString('default', { month: 'long' });
                const percentage = ((count / totalImages) * 100).toFixed(1);
                csvContent += `${monthName} ${year},${count},${percentage}%\n`;
            });

        csvContent += '\nDate Breakdown\nDate,Images,Percentage\n';
        Object.entries(imageCounts)
            .sort((a, b) => b[0].localeCompare(a[0]))
            .forEach(([dateStr, count]) => {
                const percentage = ((count / totalImages) * 100).toFixed(1);
                csvContent += `${dateStr},${count},${percentage}%\n`;
            });
    }

    return csvContent;
}

// Disconnect from Telegram
async function disconnect() {
    try {
        toggleLoading(true);
        const response = await fetch('/fetch-data/telegram/disconnect');

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        if (data.message) {
            showStatus(data.message);
            window.location.href = "/fetch-data/telegram-connect";
        }
    } catch (error) {
        handleApiError(error, 'Error disconnecting');
    } finally {
        toggleLoading(false);
    }
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('disconnect-btn').addEventListener('click', disconnect);
    document.getElementById('download-analytics-btn').addEventListener('click', downloadAnalyticsCSV);

    const refreshBtn = document.getElementById('refresh-channels-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            loadChannels();
        });
    }

    checkAuth();

    if (!isAdmin) {
        const analysisContainer = document.querySelector('.analysis-container');
        if (analysisContainer) {
            analysisContainer.style.display = 'none';

            const channelsContainer = document.querySelector('.channels-container');
            if (channelsContainer) {
                channelsContainer.style.flexBasis = '100%';
            }
        }
    }
});