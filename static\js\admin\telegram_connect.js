// Enhanced Telegram Channels JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    // initAnimations();

    // Add event listeners
    setupFormListeners();

    // Check for existing session
    checkExistingSession();
});

/**
 * Setup form event listeners
 */
function setupFormListeners() {
    // Telegram connection form
    const telegramForm = document.getElementById('telegram-form');
    if (telegramForm) {
        telegramForm.addEventListener('submit', function(e) {
            e.preventDefault();
            connectToTelegram();
        });
    }

    // Reset session button
    const resetSessionBtn = document.getElementById('reset-session-btn');
    if (resetSessionBtn) {
        resetSessionBtn.addEventListener('click', function() {
            resetSession();
        });
    }

    // Code verification form
    const codeForm = document.getElementById('code-form');
    if (codeForm) {
        codeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            verifyCode();
        });
    }

    // Password form
    const passwordForm = document.getElementById('password-form-inputs');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            verifyPassword();
        });
    }

    // Add input animations
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('input-focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('input-focused');
        });
    });
}

/**
 * Check for existing Telegram session
 */
function checkExistingSession() {
    showLoading(true);

    // Show session status
    const sessionStatus = document.getElementById('session-status');
    if (sessionStatus) {
        sessionStatus.style.display = 'flex';
    }

    // Make API request to check session
    fetch('/fetch-data/telegram/check-auth?refresh=1')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.authenticated) {
                showStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');
                updateSessionStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');

                // Load channels if authenticated
                loadChannels();
            } else {
                updateSessionStatus('No active session found. Please connect.', 'info');
            }
        })
        .catch(error => {
            console.error('Error checking session:', error);
            updateSessionStatus('Error checking session', 'danger');
            showStatus('Error checking session: ' + error.message, 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

/**
 * Connect to Telegram
 */
function connectToTelegram() {
    const apiId = document.getElementById('api_id').value;
    const apiHash = document.getElementById('api_hash').value;
    const phone = document.getElementById('phone').value;

    if (!apiId || !apiHash || !phone) {
        showStatus('Please fill in all fields', 'warning');
        return;
    }

    showLoading(true);
    updateSessionStatus('Connecting to Telegram...', 'info');

    // Add loading state to button
    const connectBtn = document.querySelector('#telegram-form button[type="submit"]');
    if (connectBtn) {
        connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
        connectBtn.disabled = true;
    }

    // Make API request to connect
    fetch('/fetch-data/telegram-connect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            api_id: apiId,
            api_hash: apiHash,
            phone: phone
        })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'code_sent') {
                // Show verification form
                showVerificationForm();
                showStatus('Please enter the verification code sent to your device', 'info');
            } else if (data.status === 'password_required') {
                // Show password form
                showPasswordForm();
                showStatus('Please enter your 2FA password', 'info');
            } else if (data.status === 'connected') {
                // Connection successful
                showStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');
                updateSessionStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');

                // Load channels
                loadChannels();
            } else {
                updateSessionStatus('Connection failed', 'danger');
                showStatus('Failed to connect: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error connecting:', error);
            updateSessionStatus('Connection error', 'danger');
            showStatus('Error connecting: ' + error.message, 'error');
        })
        .finally(() => {
            showLoading(false);

            // Reset button
            if (connectBtn) {
                connectBtn.innerHTML = '<i class="fas fa-plug"></i> Connect';
                connectBtn.disabled = false;
            }
        });
}

/**
 * Verify code
 */
function verifyCode() {
    const code = document.getElementById('code').value;

    if (!code) {
        showStatus('Please enter the verification code', 'warning');
        return;
    }

    showLoading(true);

    // Add loading state to button
    const verifyBtn = document.querySelector('#code-form button[type="submit"]');
    if (verifyBtn) {
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
        verifyBtn.disabled = true;
    }

    // Make API request to verify code
    fetch('/fetch-data/telegram/verify-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            code: code
        })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'password_required') {
                // Show password form
                showPasswordForm();
                showStatus('Please enter your 2FA password', 'info');
            } else if (data.status === 'connected') {
                // Verification successful
                showStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');
                updateSessionStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');

                // Hide verification form
                hideVerificationForm();

                // Load channels
                loadChannels();
            } else {
                showStatus('Failed to verify code: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error verifying code:', error);
            showStatus('Error verifying code: ' + error.message, 'error');
        })
        .finally(() => {
            showLoading(false);

            // Reset button
            if (verifyBtn) {
                verifyBtn.innerHTML = '<i class="fas fa-check"></i> Verify Code';
                verifyBtn.disabled = false;
            }
        });
}

/**
 * Verify password
 */
function verifyPassword() {
    const password = document.getElementById('password').value;

    if (!password) {
        showStatus('Please enter your 2FA password', 'warning');
        return;
    }

    showLoading(true);

    // Add loading state to button
    const verifyBtn = document.querySelector('#password-form-inputs button[type="submit"]');
    if (verifyBtn) {
        verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
        verifyBtn.disabled = true;
    }

    // Make API request to verify password
    fetch('/fetch-data/telegram/verify-password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            password: password
        })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'connected') {
                // Verification successful
                showStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');
                updateSessionStatus(`Connected to Telegram as ${data.username || 'User'}`, 'success');

                // Hide password form
                hidePasswordForm();

                // Load channels
                loadChannels();
            } else {
                showStatus('Failed to verify password: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error verifying password:', error);
            showStatus('Error verifying password: ' + error.message, 'error');
        })
        .finally(() => {
            showLoading(false);

            // Reset button
            if (verifyBtn) {
                verifyBtn.innerHTML = '<i class="fas fa-unlock-alt"></i> Verify Password';
                verifyBtn.disabled = false;
            }
        });
}

/**
 * Reset session
 */
function resetSession() {
    if (!confirm('Are you sure you want to reset your Telegram session? You will need to reconnect.')) {
        return;
    }

    showLoading(true);
    updateSessionStatus('Resetting session...', 'warning');

    // Make API request to reset session
    fetch('/fetch-data/telegram/reset-session')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.message) {
                showStatus('Session reset successfully', 'success');
                updateSessionStatus('Session reset. Please reconnect.', 'info');

                // Hide forms
                hideVerificationForm();
                hidePasswordForm();

                // Reset form fields
                document.getElementById('api_id').value = '';
                document.getElementById('api_hash').value = '';
                document.getElementById('phone').value = '';

                // Focus on first field
                document.getElementById('api_id').focus();
            } else {
                updateSessionStatus('Failed to reset session', 'danger');
                showStatus('Failed to reset session', 'error');
            }
        })
        .catch(error => {
            console.error('Error resetting session:', error);
            updateSessionStatus('Error resetting session', 'danger');
            showStatus('Error resetting session: ' + error.message, 'error');
        })
        .finally(() => {
            showLoading(false);
        });
}

/**
 * Load channels
 */
function loadChannels() {
    showLoading(true);
    showStatus('Connection successful! Redirecting to channels list...', 'success');

    // Redirect to the channels list page
    setTimeout(() => {
        window.location.href = '/fetch-data/telegram/channels';
    }, 1500);
}

/**
 * Show verification form
 */
function showVerificationForm() {
    const verificationForm = document.getElementById('verification-form');
    if (verificationForm) {
        verificationForm.style.display = 'block';

        // Focus on code input
        setTimeout(() => {
            const codeInput = document.getElementById('code');
            if (codeInput) {
                codeInput.focus();
            }
        }, 300);
    }
}

/**
 * Hide verification form
 */
function hideVerificationForm() {
    const verificationForm = document.getElementById('verification-form');
    if (verificationForm) {
        verificationForm.style.display = 'none';

        // Clear input
        const codeInput = document.getElementById('code');
        if (codeInput) {
            codeInput.value = '';
        }
    }
}

/**
 * Show password form
 */
function showPasswordForm() {
    const passwordForm = document.getElementById('password-form');
    if (passwordForm) {
        passwordForm.style.display = 'block';

        // Focus on password input
        setTimeout(() => {
            const passwordInput = document.getElementById('password');
            if (passwordInput) {
                passwordInput.focus();
            }
        }, 300);
    }
}

/**
 * Hide password form
 */
function hidePasswordForm() {
    const passwordForm = document.getElementById('password-form');
    if (passwordForm) {
        passwordForm.style.display = 'none';

        // Clear input
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.value = '';
        }
    }
}

/**
 * Update session status
 * @param {string} message - Status message
 * @param {string} type - Status type (info, success, warning, danger)
 */
function updateSessionStatus(message, type) {
    const sessionStatus = document.getElementById('session-status');
    if (!sessionStatus) return;

    // Update class
    sessionStatus.className = `session-status alert-${type}`;

    // Update icon
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'danger') icon = 'exclamation-circle';

    // Update content
    sessionStatus.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <div>${message}</div>
    `;

    // Show status
    sessionStatus.style.display = 'flex';
}

/**
 * Show status message
 * @param {string} message - Status message
 * @param {string} type - Status type (success, error, warning, info)
 */
function showStatus(message, type = 'info') {
    const statusMessage = document.getElementById('status-message');
    if (!statusMessage) return;

    // Map type to Bootstrap class
    const typeClass = type === 'error' ? 'danger' : type;

    // Update class
    statusMessage.className = `status-message alert-${typeClass}`;

    // Update icon
    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';

    // Update content
    statusMessage.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        <span class="message-text">${message}</span>
    `;

    // Show status
    statusMessage.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusMessage.style.display = 'none';
    }, 5000);
}

/**
 * Show/hide loading spinner
 * @param {boolean} show - Whether to show or hide the spinner
 */
function showLoading(show) {
    const loadingSpinner = document.getElementById('loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = show ? 'flex' : 'none';
    }
}
