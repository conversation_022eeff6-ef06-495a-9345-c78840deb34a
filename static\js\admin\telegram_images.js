// Global variables
let currentImageId = null;
let currentChannelId = null;
let selectedImages = [];
let allImages = [];
let channelTitle = '';
let cropper = null;
let cropSelections = [];
let isDriveConnected = false;

// Get channel ID from URL
const urlParams = new URLSearchParams(window.location.search);
const channelId = urlParams.get('channel_id');

// Show/hide loading spinner
function toggleLoading(show) {
    const spinner = document.getElementById('loading-spinner');
    if (show) {
        spinner.style.display = 'flex';
        document.body.classList.add('loading');
    } else {
        spinner.style.display = 'none';
        document.body.classList.remove('loading');
    }
}

// Show status message
function showStatus(message, type = 'success', duration = 3000) {
    const statusEl = document.getElementById('status-message');

    // Create icon based on message type
    let icon = 'check-circle';
    if (type === 'danger') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';
    if (type === 'info') icon = 'info-circle';

    statusEl.innerHTML = `<i class="fas fa-${icon} me-2"></i>${message}`;
    statusEl.className = `alert-${type} status-message`;
    statusEl.style.display = 'block';

    // Auto-hide after duration
    setTimeout(() => {
        statusEl.style.display = 'none';
    }, duration);
}

// Handle API errors
function handleApiError(error, defaultMessage) {
    console.error(error);
    const errorMessage = error.message || defaultMessage;
    showStatus(errorMessage, 'danger', 5000);
}

// Add this function to organize dates
function organizeDates(dates) {
    const dateStructure = {};

    dates.forEach(dateStr => {
        const [year, month, day] = dateStr.split('-');
        if (!dateStructure[year]) {
            dateStructure[year] = {};
        }
        if (!dateStructure[year][month]) {
            dateStructure[year][month] = [];
        }
        dateStructure[year][month].push(day);
    });

    return dateStructure;
}

// Load dates function
async function loadDates() {
    if (!channelId) {
        showStatus('No channel selected', 'warning');
        return;
    }

    try {
        toggleLoading(true);
        const response = await fetch(`/fetch-data/telegram/get-channel-dates/${channelId}`);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        // Check if channel has any dates with images
        if (!data.dates || data.dates.length === 0) {
            console.log("No image dates found in channel - showing empty message");

            // Set the channel title if available
            if (data.channel_title) {
                channelTitle = data.channel_title;
                document.getElementById('channel-title').textContent = `Images from ${data.channel_title}`;
            }

            // Show no images message
            document.getElementById('no-images-message').style.display = 'none';
            document.getElementById('empty-channel-message').style.display = 'block';
            document.getElementById('selection-controls').style.display = 'none';
            document.querySelector('.action-buttons-container').style.display = 'none';
            document.getElementById('images-grid').innerHTML = '';

            // Hide the date filter when there are no images
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) filterSection.style.display = 'none';

            toggleLoading(false);
            return; // Exit early - no need to continue
        }

        if (data.dates && data.dates.length > 0) {
            const dateStructure = organizeDates(data.dates);
            const yearSelect = document.getElementById('yearSelect');
            const monthSelect = document.getElementById('monthSelect');
            const dateSelect = document.getElementById('dateSelect');

            // Clear existing options
            yearSelect.innerHTML = '<option value="">All Years</option>';
            monthSelect.innerHTML = '<option value="">All Months</option>';
            dateSelect.innerHTML = '<option value="">All Dates</option>';

            // Add years
            Object.keys(dateStructure).sort().reverse().forEach(year => {
                yearSelect.add(new Option(year, year));
            });

            // Year change handler
            yearSelect.onchange = function() {
                monthSelect.innerHTML = '<option value="">All Months</option>';
                dateSelect.innerHTML = '<option value="">All Dates</option>';

                if (this.value) {
                    monthSelect.disabled = false;
                    const months = Object.keys(dateStructure[this.value]).sort();
                    months.forEach(month => {
                        const monthName = new Date(`${this.value}-${month}-01`).toLocaleString('default', { month: 'long' });
                        monthSelect.add(new Option(monthName, month));
                    });
                } else {
                    monthSelect.disabled = true;
                    dateSelect.disabled = true;
                    loadImages(); // Load all images when no year selected
                }
            };

            // Month change handler
            monthSelect.onchange = function() {
                dateSelect.innerHTML = '<option value="">All Dates</option>';

                if (this.value) {
                    dateSelect.disabled = false;
                    const year = yearSelect.value;
                    const dates = dateStructure[year][this.value].sort();
                    dates.forEach(day => {
                        dateSelect.add(new Option(day, day));
                    });
                } else {
                    dateSelect.disabled = true;
                    if (yearSelect.value) {
                        loadImages(`${yearSelect.value}-${this.value}`);
                    }
                }
            };

            // Date change handler
            dateSelect.onchange = function() {
                if (this.value) {
                    const year = yearSelect.value;
                    const month = monthSelect.value;
                    loadImages(`${year}-${month}-${this.value}`);
                } else if (monthSelect.value) {
                    loadImages(`${yearSelect.value}-${monthSelect.value}`);
                }
            };
        }
    } catch (error) {
        handleApiError(error, 'Error loading dates');

        // Show empty channel message as fallback
        document.getElementById('no-images-message').style.display = 'none';
        document.getElementById('empty-channel-message').style.display = 'block';
        document.getElementById('selection-controls').style.display = 'none';
        document.querySelector('.action-buttons-container').style.display = 'none';

        const filterSection = document.querySelector('.filter-section');
        if (filterSection) filterSection.style.display = 'none';
    } finally {
        toggleLoading(false);
    }
}

// Load images function
async function loadImages(date = null) {
    if (!channelId) {
        showStatus('No channel selected', 'warning');
        return;
    }

    try {
        toggleLoading(true);

        // First, check if there are any dates with images for this channel
        if (!date) {
            try {
                const datesResponse = await fetch(`/fetch-data/telegram/get-channel-dates/${channelId}`);
                if (datesResponse.ok) {
                    const datesData = await datesResponse.json();

                    // Set the channel title if available
                    if (datesData.channel_title) {
                        channelTitle = datesData.channel_title;
                        document.getElementById('channel-title').textContent = `Images from ${datesData.channel_title}`;
                    }

                    // Check if there are no dates with images
                    if (!datesData.dates || datesData.dates.length === 0) {
                        console.log("No image dates found in loadImages - showing empty message");

                        // Show no images message
                        document.getElementById('no-images-message').style.display = 'none';
                        document.getElementById('empty-channel-message').style.display = 'block';
                        document.getElementById('selection-controls').style.display = 'none';
                        document.querySelector('.action-buttons-container').style.display = 'none';
                        document.getElementById('images-grid').innerHTML = '';

                        // Hide the date filter when there are no images
                        const filterSection = document.querySelector('.filter-section');
                        if (filterSection) filterSection.style.display = 'none';

                        toggleLoading(false);
                        return; // Exit early - no need to continue
                    }
                }
            } catch (checkError) {
                console.error("Error checking dates:", checkError);
                // Continue with normal loading if check fails
            }
        }

        // Continue with regular image loading if the check passes
        // Validate and format date properly if provided
        let dateParam = null;
        if (date) {
            // Check if it's a properly formatted date (YYYY-MM-DD)
            if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                dateParam = date;
            }
            // Check if it's a partial date (YYYY-MM format)
            else if (/^\d{4}-\d{2}$/.test(date)) {
                // We'll use the partial date but not append anything to it
                dateParam = date;
            }
            // Check if it's just components that need formatting
            else {
                const parts = date.split('-');
                if (parts.length >= 2) {
                    // Ensure year and month are valid
                    const year = parts[0];
                    const month = parts[1].padStart(2, '0');

                    if (parts.length === 3 && parts[2]) {
                        // We have a day component too
                        const day = parts[2].padStart(2, '0');
                        dateParam = `${year}-${month}-${day}`;
                    } else {
                        // Just year and month
                        showStatus('Using only year and month for filtering', 'info', 2000);
                        dateParam = `${year}-${month}`;
                    }
                } else {
                    // Invalid format, show warning and continue without date filter
                    showStatus('Invalid date format, showing all images', 'warning');
                }
            }
        }

        const url = dateParam
            ? `/fetch-data/telegram/get-channel-images/${channelId}?date=${dateParam}`
            : `/fetch-data/telegram/get-channel-images/${channelId}`;

        const response = await fetch(url);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        const imagesGrid = document.getElementById('images-grid');
        // Clear the grid including skeleton loaders
        imagesGrid.innerHTML = '';
        selectedImages = [];
        allImages = data.images || [];

        // Update channel title if available
        if (data.channel_title) {
            channelTitle = data.channel_title;
            document.getElementById('channel-title').textContent = `Images from ${data.channel_title}`;
        }

        // Grid is now static, no animations
        if (data.images && data.images.length > 0) {
            document.getElementById('no-images-message').style.display = 'none';
            document.getElementById('empty-channel-message').style.display = 'none';
            document.getElementById('selection-controls').style.display = 'none';
            document.querySelector('.action-buttons-container').style.display = 'flex';

            const filterSection = document.querySelector('.filter-section');
            if (filterSection) filterSection.style.display = 'block';

            // Create array to track all image loads
            const imageLoadPromises = [];

            data.images.forEach(image => {
                // Create promise for each image load
                const loadPromise = new Promise((resolve) => {
                    const img = new Image();
                    img.onload = resolve;
                    img.onerror = () => resolve(); // Handle errors gracefully
                    img.src = `data:image/jpeg;base64,${image.image}`;
                });
                imageLoadPromises.push(loadPromise);

                // Create and append card
                const card = document.createElement('div');
                card.className = 'image-card';
                card.setAttribute('data-id', image.id);
                card.setAttribute('data-channel-id', image.channel_id);

                // Properly escape caption for both HTML and JavaScript
                const caption = image.caption || 'No caption';
                const escapedCaption = caption
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;')
                    .replace(/\\/g, '\\\\')
                    .replace(/\n/g, '\\n');

                card.innerHTML = `
                    <div class="image-container">
                        <div class="selection-overlay"></div>
                        <input type="checkbox" class="image-checkbox" onclick="toggleImageSelection(event, ${image.id}, ${image.channel_id})">
                        <img src="data:image/jpeg;base64,${image.image}" alt="Telegram image">
                    </div>
                    <div class="card-body">
                        <p class="card-text text-truncate" title="${escapedCaption}">${caption}</p>
                        <p class="card-text"><small class="text-muted"><i class="fas fa-calendar-alt me-1"></i>${image.date}</small></p>
                        <div class="image-actions">
                            <button class="btn btn-primary" onclick="openImageModal('${image.image}', '${escapedCaption}', ${image.id}, ${image.channel_id})">
                                <i class="fas fa-eye"></i><span> View</span>
                            </button>
                            <button class="btn btn-outline-primary" onclick="openImageEditor(${image.id}, ${image.channel_id})">
                                <i class="fas fa-edit"></i><span> Edit</span>
                            </button>
                            <button class="btn btn-outline-primary" onclick="downloadImage(${image.id}, ${image.channel_id})">
                                <i class="fas fa-download"></i><span> Download</span>
                            </button>
                        </div>
                    </div>
                `;
                imagesGrid.appendChild(card);
            });

            // Wait for all images to load before hiding spinner
            await Promise.all(imageLoadPromises);
            updateSelectedCount();

        } else {
            // Show same empty channel message for dates with no images
            console.log("No images found for date: " + dateParam);
            document.getElementById('no-images-message').style.display = 'none';
            document.getElementById('empty-channel-message').style.display = 'block';
            document.getElementById('selection-controls').style.display = 'none';
            document.querySelector('.action-buttons-container').style.display = 'none';

            // Update message content based on if this was date-specific
            if (dateParam) {
                // Format date in a readable way
                let formattedDate = dateParam;
                try {
                    const dateObj = new Date(dateParam);
                    if (!isNaN(dateObj)) {
                        formattedDate = dateObj.toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        });
                    }
                } catch (e) { }

                const msgTitle = document.querySelector('#empty-channel-message h3');
                const msgText = document.querySelector('#empty-channel-message p');

                if (msgTitle) msgTitle.textContent = "No Images Available";
                if (msgText) msgText.textContent = `No images found for ${formattedDate}.`;

                // Keep filter visible for date filtering
                const filterSection = document.querySelector('.filter-section');
                if (filterSection) filterSection.style.display = 'block';
            } else {
                // Full channel has no images
                const msgTitle = document.querySelector('#empty-channel-message h3');
                const msgText = document.querySelector('#empty-channel-message p');

                if (msgTitle) msgTitle.textContent = "No Images Available";
                if (msgText) msgText.textContent = "This channel does not contain any images.";

                // Hide filter for channels with no images at all
                const filterSection = document.querySelector('.filter-section');
                if (filterSection) filterSection.style.display = 'none';
            }
        }

    } catch (error) {
        handleApiError(error, 'Error loading images');
        // Show empty channel message as fallback
        document.getElementById('no-images-message').style.display = 'none';
        document.getElementById('empty-channel-message').style.display = 'block';
        document.getElementById('selection-controls').style.display = 'none';
        document.querySelector('.action-buttons-container').style.display = 'none';

        // Update message content based on if this was date-specific
        if (dateParam) {
            // Format date in a readable way
            let formattedDate = dateParam;
            try {
                const dateObj = new Date(dateParam);
                if (!isNaN(dateObj)) {
                    formattedDate = dateObj.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                }
            } catch (e) { }

            const msgTitle = document.querySelector('#empty-channel-message h3');
            const msgText = document.querySelector('#empty-channel-message p');

            if (msgTitle) msgTitle.textContent = "No Images Available";
            if (msgText) msgText.textContent = `No images found for ${formattedDate}.`;

            // Keep filter visible for date filtering
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) filterSection.style.display = 'block';
        } else {
            // Full channel has no images
            const msgTitle = document.querySelector('#empty-channel-message h3');
            const msgText = document.querySelector('#empty-channel-message p');

            if (msgTitle) msgTitle.textContent = "No Images Available";
            if (msgText) msgText.textContent = "This channel does not contain any images.";

            // Hide filter for channels with no images at all
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) filterSection.style.display = 'none';
        }
    } finally {
        toggleLoading(false);
    }
}

// Image modal functions
function openImageModal(imageBase64, caption, imageId, channelId) {
    const modalImage = document.getElementById('modal-image');
    modalImage.src = `data:image/jpeg;base64,${imageBase64}`;
    // Decode HTML entities in the caption before displaying
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = caption;
    document.getElementById('modal-caption').textContent = tempDiv.textContent || '';

    currentImageId = imageId;
    currentChannelId = channelId;

    const modalElement = document.getElementById('imageModal');
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
}

// Image selection functions
function toggleImageSelection(event, imageId, channelId) {
    event.stopPropagation();

    const checkbox = event.target;
    const card = checkbox.closest('.image-card');

    if (checkbox.checked) {
        selectedImages.push({id: imageId, channel_id: channelId});
        card.classList.add('selected');
    } else {
        selectedImages = selectedImages.filter(img => !(img.id === imageId && img.channel_id === channelId));
        card.classList.remove('selected');
    }

    updateSelectedCount();
}

function updateSelectedCount() {
    const countElement = document.getElementById('selected-count');
    countElement.textContent = `${selectedImages.length} images selected`;
    
    // Update dropdown based on selection status
    updateDriveButtonsState();
}

function selectAllImages() {
    const checkboxes = document.querySelectorAll('.image-checkbox');
    selectedImages = [];

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        const card = checkbox.closest('.image-card');
        card.classList.add('selected');

        const imageId = parseInt(card.dataset.id);
        const channelId = parseInt(card.dataset.channelId);
        selectedImages.push({id: imageId, channel_id: channelId});
    });

    updateSelectedCount();
}

function deselectAllImages() {
    const checkboxes = document.querySelectorAll('.image-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        const card = checkbox.closest('.image-card');
        card.classList.remove('selected');
    });

    selectedImages = [];
    updateSelectedCount();
}

// Download functions
async function downloadImage(imageId, channelId) {
    try {
        toggleLoading(true);

        // Find the image data from allImages array
        const imageData = allImages.find(img => img.id === imageId && img.channel_id === channelId);

        if (!imageData) {
            throw new Error('Image not found');
        }

        // Create a blob from the base64 image
        const response = await fetch(`data:image/jpeg;base64,${imageData.image}`);
        const blob = await response.blob();

        // Create object URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create temporary link element
        const link = document.createElement('a');
        link.href = url;

        // Generate filename using caption if available, or default to image ID
        const sanitizedCaption = imageData.caption ?
            imageData.caption.replace(/[^a-z0-9]/gi, '_').substring(0, 30) :
            `image_${imageId}`;
        link.download = `${sanitizedCaption}.jpg`;

        // Append link to body, click it, and remove it
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL object
        window.URL.revokeObjectURL(url);

        showStatus('Image download started', 'success', 3000);
    } catch (error) {
        handleApiError(error, 'Error downloading image');
    } finally {
        toggleLoading(false);
    }
}

async function downloadSelectedImages() {
    if (selectedImages.length === 0) {
        showStatus('No images selected', 'warning');
        return;
    }

    try {
        toggleLoading(true);

        const zip = new JSZip();
        let processedCount = 0;

        for (const image of selectedImages) {
            const imgData = allImages.find(img => img.id === image.id && img.channel_id === image.channel_id);
            if (imgData) {
                const imageBlob = await fetch(`data:image/jpeg;base64,${imgData.image}`).then(r => r.blob());
                const sanitizedCaption = imgData.caption ?
                    imgData.caption.replace(/[^a-z0-9]/gi, '_').substring(0, 30) :
                    `image_${imgData.id}`;
                zip.file(`${sanitizedCaption}.jpg`, imageBlob);
                processedCount++;
            }
        }

        // Generate zip file
        const zipBlob = await zip.generateAsync({type: 'blob'});

        // Create download link for zip
        const url = window.URL.createObjectURL(zipBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `telegram_images_${new Date().toISOString().slice(0,10)}.zip`;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(url);

        showStatus(`Downloaded ${processedCount} images`, 'success', 3000);
    } catch (error) {
        handleApiError(error, 'Error downloading images');
    } finally {
        toggleLoading(false);
    }
}

// Drive upload function
async function uploadSelectedImagesToDrive() {
    if (selectedImages.length === 0) {
        showStatus('No images selected', 'warning');
        return;
    }

    // Check if Google Drive is connected
    if (!isDriveConnected) {
        // Don't show any popup, just return silently
        return;
    }

    const uploadModal = new bootstrap.Modal(document.getElementById('uploadResultsModal'));
    uploadModal.show();

    // Reset modal state
    document.getElementById('upload-status').style.display = 'block';
    document.getElementById('upload-results').style.display = 'none';
    document.getElementById('upload-error').style.display = 'none';
    document.getElementById('view-folder-btn').style.display = 'none';

    try {
        const response = await fetch('/fetch-data/telegram/upload-to-drive', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                images: selectedImages,
                channel_name: channelTitle
            })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || 'Server error');
        }

        const data = await response.json();

        document.getElementById('upload-status').style.display = 'none';

        if (data.success) {
            document.getElementById('upload-results').style.display = 'block';

            // Update folder information
            const folderNameElement = document.getElementById('folder-name');
            folderNameElement.textContent = data.folder_name;

            // Add date folder information
            const folderInfoElement = document.getElementById('folder-info');
            if (folderInfoElement) {
                folderInfoElement.innerHTML = `
                    <span class="info-label">Date Folder:</span>
                    <div class="info-value">
                        ${data.date_folder_name}
                        <a href="${data.date_folder_link}" target="_blank" class="ms-2 link-primary">View Date Folder</a>
                    </div>
                `;
            }

            const folderLink = document.getElementById('folder-link');
            folderLink.href = data.folder_link;
            folderLink.textContent = data.folder_link;

            const viewFolderBtn = document.getElementById('view-folder-btn');
            viewFolderBtn.href = data.folder_link;
            viewFolderBtn.style.display = 'inline-block';

            const filesList = document.getElementById('uploaded-files-list');
            filesList.innerHTML = '';

            data.uploaded_files.forEach(file => {
                const listItem = document.createElement('li');
                listItem.innerHTML = `
                    <span class="file-name">${file.name}</span>
                    <a href="${file.link}" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>View
                    </a>
                `;
                filesList.appendChild(listItem);
            });
        } else {
            const errorElement = document.getElementById('upload-error');
            const errorMessage = errorElement.querySelector('.error-message p');
            errorMessage.textContent = data.error || 'Failed to upload images';
            errorElement.style.display = 'block';
        }
    } catch (error) {
        console.error(error);
        const errorElement = document.getElementById('upload-error');
        const errorMessage = errorElement.querySelector('.error-message p');
        errorMessage.textContent = error.message || 'Error uploading images';
        errorElement.style.display = 'block';
        document.getElementById('upload-status').style.display = 'none';
    }
}

// Image editor functions
function openImageEditor(imageId, channelId) {
    console.log('Opening editor for image:', imageId, channelId);
    currentImageId = imageId;
    currentChannelId = channelId;
    cropSelections = []; // Reset crop selections

    // Find the image data
    const imageData = allImages.find(img => img.id === imageId && img.channel_id === channelId);
    if (!imageData) {
        showStatus('Image not found', 'error');
        return;
    }

    // Get the modal and image elements
    const modalElement = document.getElementById('imageEditorModal');
    const editorImage = document.getElementById('editor-image');
    const cropList = document.getElementById('crop-list');

    if (!modalElement || !editorImage || !cropList) {
        showStatus('Editor not found', 'error');
        return;
    }

    // Clear crop list and show empty state
    cropList.innerHTML = '';
    document.getElementById('no-crops-message').style.display = 'block';

    // Show the modal
    const modal = new bootstrap.Modal(modalElement);
    modal.show();

    // Set image source
    // Make sure to use the correct MIME type for the image
    const imageType = imageData.mime_type || 'image/jpeg';
    editorImage.src = `data:${imageType};base64,${imageData.image}`;

    // Force the image to be visible immediately
    editorImage.style.display = 'block';
    editorImage.style.visibility = 'visible';
    editorImage.style.opacity = '1';
    editorImage.style.maxWidth = '100%';
    editorImage.style.maxHeight = '100%';

    // Initialize cropper after image loads
    editorImage.onload = function() {
        if (cropper) {
            cropper.destroy();
        }

        // Make sure the image is visible before initializing Cropper
        editorImage.style.display = 'block';
        editorImage.style.visibility = 'visible';
        editorImage.style.opacity = '1';
        editorImage.style.maxWidth = '100%';
        editorImage.style.maxHeight = '100%';

        // Make sure the container is properly sized
        const imgContainer = document.querySelector('.img-container');
        imgContainer.style.width = '100%';
        imgContainer.style.height = '700px';
        imgContainer.style.backgroundColor = 'white';
        imgContainer.style.display = 'flex';
        imgContainer.style.alignItems = 'center';
        imgContainer.style.justifyContent = 'center';

        // Initialize Cropper with simplified settings
        cropper = new Cropper(editorImage, {
            aspectRatio: NaN,
            viewMode: 0, // No restrictions
            responsive: false, // Don't auto-resize
            restore: false,
            autoCrop: false,
            movable: true,
            rotatable: true,
            scalable: true,
            zoomable: true,
            dragMode: 'move',
            minContainerWidth: 700,
            minContainerHeight: 700,
            minCanvasWidth: 400,
            minCanvasHeight: 400,
            ready() {
                // Force a reset to ensure the image is visible
                setTimeout(() => {
                    cropper.reset();
                    // Force the image to be visible
                    const cropperCanvas = document.querySelector('.cropper-canvas');
                    if (cropperCanvas) {
                        cropperCanvas.style.opacity = '1';
                        cropperCanvas.style.visibility = 'visible';
                    }
                    const cropperContainer = document.querySelector('.cropper-container');
                    if (cropperContainer) {
                        cropperContainer.style.backgroundColor = 'white';
                        cropperContainer.style.width = '700px';
                        cropperContainer.style.height = '700px';
                    }
                }, 100);
                updateCropList();
            }
        });
    };
}

// Add rotate function
function rotateImage() {
    if (!cropper) return;

    // Get current rotation
    const data = cropper.getData();
    // Add 90 degrees to current rotation
    const newRotation = ((data.rotate || 0) + 90) % 360;

    // Apply new rotation
    cropper.rotateTo(newRotation);
    showStatus('Image rotated 90° clockwise', 'success');
}

function startNewCrop() {
    if (cropper) {
        cropper.clear();  // Clear any existing crop box
        cropper.setDragMode('crop');  // Switch to crop mode
    }
}

function addCurrentCrop() {
    if (!cropper) return;

    const cropData = cropper.getData(true); // Get actual pixel values
    const cropBoxData = cropper.getCropBoxData();

    if (cropData.width > 0 && cropData.height > 0) {
        // Get the cropped canvas
        const canvas = cropper.getCroppedCanvas({
            width: cropData.width,
            height: cropData.height
        });

        // Convert canvas to data URL
        const previewUrl = canvas.toDataURL('image/jpeg');

        cropSelections.push({
            x: cropData.x,
            y: cropData.y,
            width: cropData.width,
            height: cropData.height,
            rotate: cropData.rotate,
            scaleX: cropData.scaleX,
            scaleY: cropData.scaleY,
            preview: previewUrl
        });

        updateCropList();
        showStatus('Crop area added', 'success');

        // Reset crop box
        cropper.clear();
        cropper.setDragMode('crop');
    } else {
        showStatus('Please select an area to crop', 'warning');
    }
}

function updateCropList() {
    const cropList = document.getElementById('crop-list');
    const noCropsMessage = document.getElementById('no-crops-message');
    if (!cropList || !noCropsMessage) return;

    cropList.innerHTML = '';

    // Show/hide the empty state message
    if (cropSelections.length === 0) {
        noCropsMessage.style.display = 'block';
        return;
    } else {
        noCropsMessage.style.display = 'none';
    }

    // Add each crop to the list
    cropSelections.forEach((crop, index) => {
        const item = document.createElement('div');
        item.className = 'list-group-item';
        item.innerHTML = `
            <img src="${crop.preview}" class="crop-preview" alt="Crop preview ${index + 1}">
            <div class="crop-info">
                <div>Crop #${index + 1}</div>
                <small class="text-muted">${Math.round(crop.width)}×${Math.round(crop.height)}px</small>
            </div>
            <div class="crop-actions">
                <button class="btn-remove" onclick="removeCrop(${index})" title="Remove crop">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        cropList.appendChild(item);
    });
}

function removeCrop(index) {
    cropSelections.splice(index, 1);
    updateCropList();
}

function resetCrop() {
    if (cropper) {
        cropper.reset();
        cropper.setDragMode('move');
        cropSelections = [];
        updateCropList();
        showStatus('Crop selections reset', 'info', 2000);
    }
}

async function saveAllCropsLocally() {
    if (!cropper) {
        showStatus('No image loaded', 'warning');
        return;
    }

    try {
        // Create a zip file for multiple crops
        const zip = new JSZip();

        // Get the original image data
        const imageData = allImages.find(img => img.id === currentImageId && img.channel_id === currentChannelId);
        if (!imageData) {
            throw new Error('Original image not found');
        }

        // Create a new image element for processing
        const img = new Image();
        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = `data:image/jpeg;base64,${imageData.image}`;
        });

        // If there are crop selections, save them
        if (cropSelections.length > 0) {
            // Process each crop selection
            for (let i = 0; i < cropSelections.length; i++) {
                const crop = cropSelections[i];
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = crop.width;
                canvas.height = crop.height;

                ctx.save();
                ctx.translate(canvas.width/2, canvas.height/2);
                if (crop.rotate) {
                    ctx.rotate((crop.rotate * Math.PI) / 180);
                }
                ctx.scale(crop.scaleX || 1, crop.scaleY || 1);

                ctx.drawImage(
                    img,
                    crop.x,
                    crop.y,
                    crop.width,
                    crop.height,
                    -crop.width/2,
                    -crop.height/2,
                    crop.width,
                    crop.height
                );

                ctx.restore();

                const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/jpeg', 0.9));
                zip.file(`crop_${i + 1}_${currentImageId}.jpg`, blob);
            }
        } else {
            // Save the full rotated image if no crops
            const canvas = cropper.getCroppedCanvas({
                width: img.width,
                height: img.height,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            if (canvas) {
                const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/jpeg', 0.9));
                zip.file(`rotated_${currentImageId}.jpg`, blob);
            }
        }

        // Generate and download zip file
        const zipBlob = await zip.generateAsync({type: 'blob'});
        const url = URL.createObjectURL(zipBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = cropSelections.length > 0 ?
            `crops_${currentImageId}.zip` :
            `edited_${currentImageId}.zip`;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
        showStatus('Image saved successfully', 'success');

        const modal = bootstrap.Modal.getInstance(document.getElementById('imageEditorModal'));
        if (modal) {
            modal.hide();
        }
    } catch (error) {
        console.error('Error saving image:', error);
        showStatus('Error saving image: ' + error.message, 'error');
    }
}

async function saveAllCropsToGoogleDrive() {
    if (!cropper) {
        showStatus('No image loaded', 'warning');
        return;
    }

    // Check if Google Drive is connected
    if (!isDriveConnected) {
        // Don't show any popup, just return silently
        return;
    }

    try {
        showStatus('Uploading to Google Drive...', 'info');
        console.log('Starting upload to Google Drive...');

        // Get the original image data
        const imageData = allImages.find(img => img.id === currentImageId && img.channel_id === currentChannelId);
        if (!imageData) {
            throw new Error('Original image not found');
        }

        // Create a new image element for processing
        const img = new Image();
        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = `data:image/jpeg;base64,${imageData.image}`;
        });

        // If there are crop selections, save them
        if (cropSelections.length > 0) {
            // Process each crop selection
            for (let i = 0; i < cropSelections.length; i++) {
                const crop = cropSelections[i];
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = crop.width;
                canvas.height = crop.height;

                ctx.save();
                ctx.translate(canvas.width/2, canvas.height/2);
                if (crop.rotate) {
                    ctx.rotate((crop.rotate * Math.PI) / 180);
                }
                ctx.scale(crop.scaleX || 1, crop.scaleY || 1);

                ctx.drawImage(
                    img,
                    crop.x,
                    crop.y,
                    crop.width,
                    crop.height,
                    -crop.width/2,
                    -crop.height/2,
                    crop.width,
                    crop.height
                );

                ctx.restore();

                const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/jpeg', 0.9));

                // Create a base filename from the original image ID
                // Generate a sanitized base filename
                let baseFilename = `image${currentImageId}`;

                // If the image has a caption, use it for a more descriptive filename
                if (imageData.caption) {
                    // Sanitize caption for filename use
                    const sanitizedCaption = imageData.caption
                        .replace(/[^a-z0-9]/gi, '_')
                        .substring(0, 30)
                        .trim();

                    if (sanitizedCaption) {
                        baseFilename = sanitizedCaption;
                    }
                }

                // Create the filename with the crop number
                const cropFilename = `${baseFilename}_${i + 1}.jpg`;

                // Create form data
                const formData = new FormData();
                formData.append('file', blob, cropFilename);
                formData.append('channel_id', currentChannelId);
                formData.append('channel_name', channelTitle);
                formData.append('original_id', currentImageId);
                formData.append('crop_number', i + 1);

                // Upload to Google Drive using the unified endpoint
                const response = await fetch('/fetch-data/telegram/upload-to-drive', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        // Don't set Content-Type header - browser will set it automatically with boundary
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `Failed to upload crop ${i + 1}`);
                }

                const responseData = await response.json();
                console.log(`Crop ${i + 1} uploaded successfully:`, responseData);
            }
        } else {
            // Save the full rotated image if no crops
            const canvas = cropper.getCroppedCanvas({
                width: img.width,
                height: img.height,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high',
            });

            if (canvas) {
                const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/jpeg', 0.9));

                // Generate a sanitized base filename
                let baseFilename = `image${currentImageId}`;

                // If the image has a caption, use it for a more descriptive filename
                if (imageData.caption) {
                    // Sanitize caption for filename use
                    const sanitizedCaption = imageData.caption
                        .replace(/[^a-z0-9]/gi, '_')
                        .substring(0, 30)
                        .trim();

                    if (sanitizedCaption) {
                        baseFilename = sanitizedCaption;
                    }
                }

                // Create the filename for the rotated image
                const rotatedFilename = `${baseFilename}_rotated.jpg`;

                // Create form data
                const formData = new FormData();
                formData.append('file', blob, rotatedFilename);
                formData.append('channel_id', currentChannelId);
                formData.append('channel_name', channelTitle);
                formData.append('original_id', currentImageId);
                formData.append('is_rotated', 'true');

                // Upload to Google Drive using the unified endpoint
                const response = await fetch('/fetch-data/telegram/upload-to-drive', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        // Don't set Content-Type header - browser will set it automatically with boundary
                    }
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || 'Failed to upload rotated image');
                }

                const responseData = await response.json();
                console.log('Rotated image uploaded successfully:', responseData);
            }
        }

        showStatus('Upload to Google Drive successful', 'success');

        const modal = bootstrap.Modal.getInstance(document.getElementById('imageEditorModal'));
        if (modal) {
            modal.hide();
        }
    } catch (error) {
        console.error('Error uploading to Drive:', error);
        const errorMsg = error.message || 'Unknown error occurred';
        showStatus('Error uploading to Drive: ' + errorMsg, 'error', 5000);

        // Log additional details for debugging
        if (error.stack) {
            console.error('Error stack:', error.stack);
        }
    }
}

// Function to check Google Drive connection status
async function checkGoogleDriveConnection() {
    try {
        const response = await fetch('/admin/check-google-drive-connection');
        if (!response.ok) {
            throw new Error('Failed to check Google Drive connection');
        }
        const data = await response.json();
        isDriveConnected = data.connected;

        // Update UI based on connection status
        updateDriveButtonsState();

        return isDriveConnected;
    } catch (error) {
        console.error('Error checking Google Drive connection:', error);
        isDriveConnected = false;
        updateDriveButtonsState();
        return false;
    }
}

// Function to update Drive buttons state
function updateDriveButtonsState() {
    const uploadDriveBtn = document.getElementById('upload-drive-btn');
    const editorUploadDriveBtn = document.getElementById('editor-upload-drive-btn');
    const uploadStorageDropdown = document.getElementById('uploadStorageDropdown');

    // Check image selection status for dropdown
    const hasImagesSelected = selectedImages.length > 0;

    // Update dropdown button status
    if (uploadStorageDropdown) {
        if (!hasImagesSelected) {
            // uploadStorageDropdown.classList.add('disabled');
            uploadStorageDropdown.setAttribute('title', 'No images selected');
        } else {
            // uploadStorageDropdown.classList.remove('disabled');
            uploadStorageDropdown.removeAttribute('title');
        }
    }

    // Update main upload button
    if (uploadDriveBtn) {
        if (!isDriveConnected) {
            uploadDriveBtn.classList.add('disabled');
            uploadDriveBtn.setAttribute('title', 'Google Drive not connected. Please connect in administration.');
        } else {
            uploadDriveBtn.classList.remove('disabled');
            uploadDriveBtn.removeAttribute('title');
        }
    }

    // Update editor upload button
    if (editorUploadDriveBtn) {
        if (!isDriveConnected) {
            editorUploadDriveBtn.classList.add('disabled');
            editorUploadDriveBtn.setAttribute('title', 'Google Drive not connected. Please connect in administration.');
        } else {
            editorUploadDriveBtn.classList.remove('disabled');
            editorUploadDriveBtn.removeAttribute('title');
        }
    }
}

// Function to handle DataStorage options
function handleStorageOptions() {
    // Add event listeners for "Coming Soon" options
    document.querySelectorAll('.dropdown-item.coming-soon').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            showStatus('This storage option is coming soon', 'info');
        });
    });

    // Add event listener for the dropdown toggle button
    const uploadStorageDropdown = document.getElementById('uploadStorageDropdown');
    if (uploadStorageDropdown) {
        uploadStorageDropdown.addEventListener('click', function(e) {
            if (selectedImages.length === 0) {
                e.preventDefault();
                e.stopPropagation();
                showStatus('No images selected', 'warning');
            }
        });
    }

    // Add event listener for main Google Drive option
    const uploadDriveBtn = document.getElementById('upload-drive-btn');
    if (uploadDriveBtn) {
        uploadDriveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (selectedImages.length === 0) {
                showStatus('No images selected', 'warning');
            } else if (!isDriveConnected) {
                showStatus('Google Drive not connected. Please connect in administration.', 'warning');
            } else {
                uploadSelectedImagesToDrive();
            }
        });
    }

    // Add event listener for editor Google Drive option
    const editorUploadDriveBtn = document.getElementById('editor-upload-drive-btn');
    if (editorUploadDriveBtn) {
        editorUploadDriveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (!isDriveConnected) {
                showStatus('Google Drive not connected. Please connect in administration.', 'warning');
            } else {
                saveAllCropsToGoogleDrive();
            }
        });
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check Google Drive connection status
    checkGoogleDriveConnection();
    
    // Initialize storage options
    handleStorageOptions();

    // Call loadDates first - it will handle showing empty message if needed
    loadDates().then(() => {
        // This will only execute if the channel has images
        console.log("Channel has images, ready for filtering");
    }).catch(error => {
        console.error("Error in initial load:", error);
    });

    const imagesGrid = document.getElementById('images-grid');
    imagesGrid.innerHTML = '';

    // Download button in modal
    document.getElementById('download-btn').addEventListener('click', function() {
        if (currentImageId && currentChannelId) {
            downloadImage(currentImageId, currentChannelId);

            const modalElement = document.getElementById('imageModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            modal.hide();
        }
    });

    // Selection controls
    document.getElementById('select-all-btn').addEventListener('click', selectAllImages);
    document.getElementById('deselect-all-btn').addEventListener('click', deselectAllImages);
    document.getElementById('download-selected-btn').addEventListener('click', downloadSelectedImages);

    // Add modal cleanup for image editor
    const imageEditorModal = document.getElementById('imageEditorModal');
    if (imageEditorModal) {
        imageEditorModal.addEventListener('hidden.bs.modal', function() {
            if (cropper) {
                cropper.destroy();
                cropper = null;
            }
            cropSelections = [];

            // Reset crop list UI
            const cropList = document.getElementById('crop-list');
            if (cropList) {
                cropList.innerHTML = '';
            }

            // Show empty state message
            const noCropsMessage = document.getElementById('no-crops-message');
            if (noCropsMessage) {
                noCropsMessage.style.display = 'block';
            }
        });
    }
});
