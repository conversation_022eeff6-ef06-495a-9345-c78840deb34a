document.addEventListener('DOMContentLoaded', function() {
    // Get data passed from the HTML template
    const images = ANNOTATION_DATA.images;
    const sessionId = ANNOTATION_DATA.session_id;
    const verificationMode = ANNOTATION_DATA.verification_mode;
    const batchName = ANNOTATION_DATA.batch_name;

    // Global variables
    let currentIndex = 0;
    let labels = {};
    let isDirty = false;
    let isSubmitting = false;
    let cropMode = false;
    let isInCropMode = false;

    // Initialize labels from pre-existing data if in verification mode
    if (verificationMode) {
        images.forEach(img => {
            if (img.label) {
                labels[img.name] = img.label;
            }
        });
    }

    // Get DOM elements based on mode
    // Verification mode elements
    const currentLabel = document.getElementById('currentLabel');
    const verifyLabelBtn = document.getElementById('verifyLabelBtn');
    const editLabelBtn = document.getElementById('editLabelBtn');
    const editLabelContainer = document.getElementById('editLabelContainer');
    const editLabelInput = document.getElementById('editLabelInput');
    const saveLabelBtn = document.getElementById('saveLabelBtn');
    const cancelEditBtn = document.getElementById('cancelEditBtn');

    // Manual labelling elements
    const labelInput = document.getElementById('labelInput');
    const saveBtn = document.getElementById('saveBtn');

            // Common elements
            const currentImageEl = document.getElementById('currentImage');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const imageCounter = document.getElementById('imageCounter');
            const saveIndicator = document.getElementById('saveIndicator');
            const saveAllBtn = document.getElementById('saveAllBtn');
            const getNextSetBtn = document.getElementById('getNextSetBtn');

            // Check if there's a batch completion in progress
            const completionKey = 'batch_completion_' + sessionId;
            const completionInProgress = localStorage.getItem(completionKey) === 'true';

            if (completionInProgress && saveAllBtn) {
                // Disable the Save All button permanently
                saveAllBtn.disabled = true;
                saveAllBtn.innerHTML = 'Completion in progress...';
                saveAllBtn.classList.add('btn-secondary');
                saveAllBtn.classList.remove('btn-primary');
                saveAllBtn.style.opacity = '0.5';
                saveAllBtn.style.cursor = 'not-allowed';
                saveAllBtn.title = 'This batch is being completed. Please wait or refresh the page.';

                // Show a notification
                setTimeout(() => {
                    showNotification('A batch completion is already in progress. The Save All button has been disabled to prevent duplicate submissions.', 'warning', 'Completion In Progress');
                }, 1000);

                // Set the submission flag
                isSubmitting = true;
            }

            // Initialize the UI
            updateUI();

            // Event listeners
            if (verificationMode) {
                // Verify button
                if (verifyLabelBtn) {
                    verifyLabelBtn.addEventListener('click', function() {
                        if (isInCropMode) return;
                        verifyLabel();
                    });
                }

                // Edit button
                if (editLabelBtn) {
                    editLabelBtn.addEventListener('click', function() {
                        if (isInCropMode) return;
                        showEditForm();
                    });
                }

                // Save edited label button
                if (saveLabelBtn) {
                    saveLabelBtn.addEventListener('click', function() {
                        if (isInCropMode) return;
                        saveEditedLabel();
                    });
                }

                // Cancel edit button
                if (cancelEditBtn) {
                    cancelEditBtn.addEventListener('click', function() {
                        if (isInCropMode) return;
                        hideEditForm();
                    });
                }
            } else {
                // Save button
                if (saveBtn) {
                    saveBtn.addEventListener('click', function(e) {
                        if(cropMode === true){
                            return;
                        }
                        e.preventDefault();
                        saveAndNavigate();
                        return false; // Prevent form submission
                    });

                    // Make sure the save button is properly styled and accessible
                    saveBtn.classList.add('btn-primary');
                    saveBtn.setAttribute('type', 'button');
                }
            }

            // Navigation buttons
            prevBtn.addEventListener('click', function() {
                if(cropMode === true){
                    return;
                }
                navigateToPrevious();
            });

            nextBtn.addEventListener('click', function() {
                if(cropMode === true){
                    return;
                }
                navigateToNext();
            });

            // Save All Labels button
            saveAllBtn.addEventListener('click', function() {
                if(cropMode === true){
                    return;
                }
                completeBatch();
            });

            // Get Next Set button (if present)
            if (getNextSetBtn) {
                getNextSetBtn.addEventListener('click', function() {
                    // Show loading spinner
                    this.disabled = true;
                    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading next set...';

                    // Navigate to next set
                    window.location.href = '/annotate/next-set';
                });
            }

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // If in crop mode, only handle Enter and Escape
                if (isInCropMode || cropMode) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        confirmCrop();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        destroyCropper();
                    }
                    return;
                }

                // Normal keyboard shortcuts when not in crop mode
                if (e.key === 'Enter' && !e.ctrlKey) {
                    e.preventDefault();
                    if (verificationMode) {
                        if (editLabelContainer && editLabelContainer.style.display !== 'none') {
                            saveEditedLabel();
                        } else {
                            verifyLabel();
                        }
                    } else {
                        saveAndNavigate();
                    }
                } else if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    navigateToPrevious();
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    navigateToNext();
                } else if (e.key === 'c' && e.ctrlKey) {
                    e.preventDefault();
                    initCropper();
                } else if (e.key === '+' || e.key === '=' || e.key === 'Add') {
                    e.preventDefault();
                    zoomIn();
                } else if (e.key === '-' || e.key === 'Subtract') {
                    e.preventDefault();
                    zoomOut();
                }// else if (e.key === '0') {
                //    e.preventDefault();
                //    resetZoom();
                //}
            });

            // Functions

            // Update the UI based on the current image
            function updateUI() {
                const img = images[currentIndex];

                // Update image
                currentImageEl.src = img.url;

                // Update counter
                imageCounter.textContent = `Image ${currentIndex + 1} of ${images.length}`;

                // Update label
                if (verificationMode) {
                    const label = labels[img.name] || '';
                    if (currentLabel) currentLabel.textContent = label || 'No label';
                    if (editLabelInput) editLabelInput.value = label;
                    if (typeof hideEditForm === 'function') hideEditForm();
                } else {
                    const currentLabelValue = labels[img.name] || '';
                    if (labelInput) labelInput.value = currentLabelValue;

                    // Disable next button if label is empty in manual mode
                    if (nextBtn) {
                        nextBtn.disabled = currentIndex === images.length - 1 || currentLabelValue === '';
                    }

                    // Show or hide the completed badge based on label status
                    const completedBadge = document.getElementById('completedBadge');
                    if (completedBadge) {
                        completedBadge.style.display = currentLabelValue ? 'block' : 'none';
                    }
                }

                // Update navigation buttons
                if (prevBtn) {
                    prevBtn.disabled = currentIndex === 0;
                }

                if (verificationMode && nextBtn) {
                    nextBtn.disabled = currentIndex === images.length - 1;
                }

                // Hide save indicator
                if (saveIndicator) {
                    saveIndicator.style.display = 'none';
                }

                // Update progress indicators
                updateProgressIndicators();
            }

            // Update progress indicators
            function updateProgressIndicators() {
                // Count labeled/verified images
                const labeledCount = Object.keys(labels).length;
                let verifiedCount = 0;

                if (verificationMode) {
                    verifiedCount = images.reduce((count, img) => {
                        return count + (labels[img.name] ? 1 : 0);
                    }, 0);
                }

                // Update progress bar
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    const percentage = Math.round(((verificationMode ? verifiedCount : labeledCount) / images.length) * 100);
                    progressBar.style.width = `${percentage}%`;
                    progressBar.setAttribute('aria-valuenow', percentage);
                }

                // Update counters
                const completedCounter = document.getElementById('completedCounter');
                const percentageCounter = document.getElementById('percentageCounter');
                const saveAllCounter = document.getElementById('saveAllCounter');
                const completionMessage = document.getElementById('completionMessage');

                const count = verificationMode ? verifiedCount : labeledCount;
                const percentage = Math.round((count / images.length) * 100);

                // Update counter displays
                if (completedCounter) completedCounter.textContent = count;
                if (percentageCounter) percentageCounter.textContent = `${percentage}%`;
                if (saveAllCounter) saveAllCounter.textContent = count;

                if (verificationMode) {
                    // In verification mode, require all images to be verified AND user to be on the last image
                    const isComplete = verifiedCount === images.length && currentIndex === images.length - 1;

                    if (isComplete) {
                        if (completionMessage) completionMessage.style.display = 'block';
                        if (saveAllBtn) {
                            saveAllBtn.disabled = false;
                            saveAllBtn.title = 'Save all verified labels';
                        }
                    } else {
                        if (completionMessage) completionMessage.style.display = 'none';
                        if (saveAllBtn) {
                            saveAllBtn.disabled = true;
                            saveAllBtn.title = currentIndex < images.length - 1 ?
                                'You must reach the last image to enable this button' :
                                'Complete verifying all images to enable this button';
                        }
                    }
                } else {
                    // In manual mode, just require all images to be labeled
                    const isComplete = labeledCount === images.length;

                    if (isComplete) {
                        if (completionMessage) completionMessage.style.display = 'block';
                        if (saveAllBtn) {
                            saveAllBtn.disabled = false;
                            saveAllBtn.title = 'Save all labeled images';
                        }
                    } else {
                        if (completionMessage) completionMessage.style.display = 'none';
                        if (saveAllBtn) {
                            saveAllBtn.disabled = true;
                            saveAllBtn.title = 'Complete labeling all images to enable this button';
                        }
                    }
                }

                // Update Get Next Set button state - always keep disabled until Save All is clicked
                if (getNextSetBtn) {
                    // Always keep disabled until labels are saved
                    getNextSetBtn.disabled = true;
                    getNextSetBtn.title = 'Save all labels first to enable this button';
                }
            }

            // Navigate to the previous image
            function navigateToPrevious() {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateUI();
                }
            }

            // Navigate to the next image
            function navigateToNext() {
                if (!verificationMode) {
                    // In manual mode, check if the current image has a label before navigating
                    const img = images[currentIndex];
                    const hasLabel = labels[img.name] && labels[img.name].trim() !== '';

                    if (!hasLabel) {
                        showNotification('Please enter and save a label before proceeding to the next image.', 'warning');
                        return;
                    }
                }

                if (currentIndex < images.length - 1) {
                    currentIndex++;
                    updateUI();
                }
            }

            if (verificationMode) {
            // Verify the current label (no changes)
            function verifyLabel() {
                // Move to next image if not the last one
                if (currentIndex < images.length - 1) {
                    // No more auto-saving after every 10 images

                    currentIndex++;
                    updateUI();
                } else {
                    // Last image, complete the batch
                    completeBatch();
                }
            }

            // Show the edit form
            function showEditForm() {
                editLabelContainer.style.display = 'block';
                editLabelInput.focus();
            }

            // Hide the edit form
            function hideEditForm() {
                editLabelContainer.style.display = 'none';
            }

            // Save the edited label
            function saveEditedLabel() {
                const img = images[currentIndex];
                const newLabel = editLabelInput.value.trim();

                if (newLabel !== '') {
                    labels[img.name] = newLabel;
                    currentLabel.textContent = newLabel;
                    isDirty = true;

                    // Hide edit form
                    hideEditForm();

                    // Update progress indicators
                    updateProgressIndicators();

                    // No more auto-saving after every 10 images

                    // Move to next image if not the last one
                    if (currentIndex < images.length - 1) {
                        currentIndex++;
                        updateUI();
                    }
                }
            }
            } else {
            // Save the current label
            function saveLabel() {
                const img = images[currentIndex];
                const label = labelInput.value.trim();

                if (label !== '') {
                    // Show saving state
                    if (saveBtn) {
                        saveBtn.disabled = true;
                        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
                    }

                    // Store the label
                    labels[img.name] = label;
                    isDirty = true;

                    // Reset button state and show success after a brief delay
                    setTimeout(() => {
                        if (saveBtn) {
                            saveBtn.disabled = false;
                            saveBtn.innerHTML = '<i class="bi bi-check-lg me-1"></i> Saved!';

                            // Reset after 1.5 seconds
                            setTimeout(() => {
                                saveBtn.innerHTML = '<i class="bi bi-check-lg me-1"></i> Save';
                            }, 1500);
                        }

                        // Show save indicator if exists
                        if (saveIndicator) {
                            saveIndicator.style.display = 'inline-block';
                            setTimeout(() => {
                                saveIndicator.style.display = 'none';
                            }, 1500);
                        }

                        // Update progress indicators
                        updateProgressIndicators();

                        // Enable next button now that the image is labeled
                        nextBtn.disabled = currentIndex === images.length - 1;

                        // Add visual feedback to the labeled image
                        const completedBadge = document.getElementById('completedBadge');
                        if (completedBadge) {
                            completedBadge.style.display = 'block';
                        }

                        // Update UI without navigating
                        updateUI();
                    }, 300); // Short delay to show saving state
                } else {
                    // Replace alert with validation feedback
                    labelInput.classList.add('is-invalid');
                    // Add a shake animation to the input field
                    labelInput.animate([
                        { transform: 'translateX(0px)' },
                        { transform: 'translateX(-5px)' },
                        { transform: 'translateX(5px)' },
                        { transform: 'translateX(-5px)' },
                        { transform: 'translateX(5px)' },
                        { transform: 'translateX(-5px)' },
                        { transform: 'translateX(0px)' }
                    ], {
                        duration: 300
                    });
                    // Focus back on the input field
                    labelInput.focus();
                    // Show notification instead of alert
                    showNotification('Please enter a label before saving.', 'warning');
                }
            }

            // Function to save the label and navigate to the next image
            function saveAndNavigate() {
                // First save the label
                saveLabel();
                // Then proceed to next image after a short delay to allow the save to complete
                setTimeout(() => {
                    if (currentIndex < images.length - 1) {
                        navigateToNext();
                    }
                }, 350); // Wait slightly longer than the saveLabel timeout (300ms)
            }
            }

            // Complete the batch
            function completeBatch() {
                // Prevent multiple submissions
                if (isSubmitting) {
                    console.log('Submission already in progress, ignoring click');
                    return;
                }

                // Set flag to prevent multiple submissions
                isSubmitting = true;

                // Store in localStorage that we've started a completion
                const completionKey = 'batch_completion_' + sessionId;
                localStorage.setItem(completionKey, 'true');

                // Show loading state and disable button permanently
                if (saveAllBtn) {
                    // Completely disable the button and make it very obvious
                    saveAllBtn.disabled = true;
                    saveAllBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
                    saveAllBtn.classList.add('btn-secondary');
                    saveAllBtn.classList.remove('btn-primary');
                    saveAllBtn.style.opacity = '0.5';
                    saveAllBtn.style.cursor = 'not-allowed';

                    // Also remove the event listener to prevent any possibility of clicking
                    saveAllBtn.removeEventListener('click', completeBatch);

                    // Add a tooltip explaining why it's disabled
                    saveAllBtn.title = 'This batch is being completed. Please wait or refresh the page.';
                }

                // Disable the Get Next Set button during submission
                if (getNextSetBtn) {
                    getNextSetBtn.disabled = true;
                }

                // Save all labels and mark as complete
                saveLabelsToServer(true);

                // Add a console log for debugging
                console.log('Batch completion request sent at:', new Date().toISOString());
            }

            // Save labels to the server
            function saveLabelsToServer(isComplete) {
                if (!isDirty && !isComplete) return;

                // Create a unique request ID to track this specific save request
                const requestId = Date.now().toString();
                console.log(`Starting save request ${requestId}, isComplete=${isComplete}`);

                // Only show UI indicators for manual "Save All" action
                if (isComplete) {
                    // Show loading state on the Save All button itself
                    if (saveAllBtn) {
                        saveAllBtn.disabled = true;
                        saveAllBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
                        saveAllBtn.classList.add('btn-secondary');
                        saveAllBtn.classList.remove('btn-primary');
                    }

                    // Also disable the Get Next Set button during submission
                    if (getNextSetBtn) {
                        getNextSetBtn.disabled = true;
                    }
                }

                fetch('/save-labels', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        session_id: sessionId,
                        labels: labels,
                        verification_mode: verificationMode,
                        is_complete: isComplete,
                        request_id: requestId,  // Add a unique ID to track this request
                        batch_name: batchName   // Add batch name to the request
                    }),
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`Save request ${requestId} completed successfully`);

                    if (data.success) {
                        isDirty = false;

                        if (isComplete) {
                            // Reset the Save All button
                            if (saveAllBtn) {
                                saveAllBtn.disabled = false;
                                saveAllBtn.innerHTML = '<i class="bi bi-save"></i> Save All Labels';
                                saveAllBtn.classList.remove('btn-secondary');
                                saveAllBtn.classList.add('btn-primary');
                            }

                            // Enable the Get Next Set button after successful save
                            if (getNextSetBtn) {
                                getNextSetBtn.disabled = false;
                                getNextSetBtn.title = 'Click to get the next set of images';
                            }

                            // Reset submission flag only after successful completion
                            isSubmitting = false;

                            // Clear the localStorage flag for this completion
                            const completionKey = 'batch_completion_' + sessionId;
                            localStorage.removeItem(completionKey);

                            // Show notification instead of alert
                            showNotification('All labels saved successfully! Click "Get Next Set" to continue.', 'success', 'Save Complete');
                        }
                    } else {
                        console.error(`Save request ${requestId} failed:`, data.error);

                        if (isComplete) {
                            // Reset the Save All button
                            if (saveAllBtn) {
                                saveAllBtn.disabled = false;
                                saveAllBtn.innerHTML = '<i class="bi bi-save"></i> Save All Labels';
                                saveAllBtn.classList.remove('btn-secondary');
                                saveAllBtn.classList.add('btn-primary');
                            }

                            // Keep Get Next Set button disabled on error
                            if (getNextSetBtn) {
                                getNextSetBtn.disabled = true;
                                getNextSetBtn.title = 'Save all labels first to enable this button';
                            }

                            // Reset submission flag
                            isSubmitting = false;

                            // Clear the localStorage flag for this completion
                            const completionKey = 'batch_completion_' + sessionId;
                            localStorage.removeItem(completionKey);

                            // Show notification instead of alert
                            showNotification('Error saving labels: ' + (data.error || 'Unknown error'), 'error', 'Save Error');

                            // Re-enable the Save All button after error
                            if (saveAllBtn) {
                                saveAllBtn.disabled = false;
                                saveAllBtn.innerHTML = '<i class="bi bi-save"></i> Save All Labels';
                                saveAllBtn.classList.remove('btn-secondary');
                                saveAllBtn.classList.add('btn-primary');
                                saveAllBtn.style.opacity = '1';
                                saveAllBtn.style.cursor = 'pointer';
                                saveAllBtn.title = 'Save all labels and complete the batch';

                                // Re-add the event listener
                                saveAllBtn.addEventListener('click', completeBatch);
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error(`Save request ${requestId} failed with exception:`, error);

                    if (isComplete) {
                        // Reset the Save All button
                        if (saveAllBtn) {
                            saveAllBtn.disabled = false;
                            saveAllBtn.innerHTML = '<i class="bi bi-save"></i> Save All Labels';
                            saveAllBtn.classList.remove('btn-secondary');
                            saveAllBtn.classList.add('btn-primary');
                            saveAllBtn.style.opacity = '1';
                            saveAllBtn.style.cursor = 'pointer';
                            saveAllBtn.title = 'Save all labels and complete the batch';

                            // Re-add the event listener
                            saveAllBtn.addEventListener('click', completeBatch);
                        }

                        // Keep Get Next Set button disabled on error
                        if (getNextSetBtn) {
                            getNextSetBtn.disabled = true;
                            getNextSetBtn.title = 'Save all labels first to enable this button';
                        }

                        // Always reset submission flag on error
                        isSubmitting = false;

                        // Clear the localStorage flag for this completion
                        const completionKey = 'batch_completion_' + sessionId;
                        localStorage.removeItem(completionKey);

                        // Show notification instead of alert
                        showNotification('Error saving labels. Please try again.', 'error', 'Save Error');
                    }
                });
            }

            // Zoom functionality
            let currentZoom = 1.0;
            const zoomStep = 0.1;
            const minZoom = 0.5;
            const maxZoom = 3.0;

            // Initialize zoom controls
            const zoomInBtn = document.getElementById('zoomIn');
            const zoomOutBtn = document.getElementById('zoomOut');
            const zoomResetBtn = document.getElementById('zoomReset');
            const zoomLevel = document.getElementById('zoomLevel');
            const imageDisplay = document.getElementById('imageDisplay');

            // Add event listeners for zoom controls
            if (zoomInBtn) {
                zoomInBtn.addEventListener('click', function() {
                    zoomIn();
                });
            }

            if (zoomOutBtn) {
                zoomOutBtn.addEventListener('click', function() {
                    zoomOut();
                });
            }

            if (zoomResetBtn) {
                zoomResetBtn.addEventListener('click', function() {
                    resetZoom();
                });
            }

            // Add mouse wheel zoom
            if (imageDisplay) {
                imageDisplay.addEventListener('wheel', function(e) {
                    // Don't zoom if in crop mode
                    if (cropMode) return;

                    // Prevent the default scroll behavior
                    e.preventDefault();

                    // Determine zoom direction
                    if (e.deltaY < 0) {
                        // Scroll up = zoom in
                        zoomIn();
                    } else {
                        // Scroll down = zoom out
                        zoomOut();
                    }
                });
            }

            function zoomIn() {
                if (currentZoom < maxZoom) {
                    currentZoom += zoomStep;
                    applyZoom();
                }
            }

            function zoomOut() {
                if (currentZoom > minZoom) {
                    currentZoom -= zoomStep;
                    applyZoom();
                }
            }

            function resetZoom() {
                currentZoom = 1.0;
                resetImagePosition();
                applyZoom();
            }

            function applyZoom() {
                currentImageEl.style.transform = `scale(${currentZoom})`;

                // Enable or disable draggable feature based on zoom level
                if (currentZoom > 1.0) {
                    // Only add draggable class if zoom is greater than 1
                    currentImageEl.classList.add('draggable');
                    // Enable pan when zoomed in
                    setupImagePan();
                    // Show pan hint
                    const panHint = document.getElementById('panHint');
                    if (panHint) {
                        panHint.style.display = 'block';
                        // Auto-hide after 3 seconds
                        setTimeout(() => {
                            panHint.style.display = 'none';
                        }, 3000);
                    }
                } else {
                    // Remove draggable class and reset position when at normal zoom
                    currentImageEl.classList.remove('draggable');
                    currentImageEl.style.transformOrigin = 'center center';
                    // Reset transform position if any
                    resetImagePosition();
                    // Disable pan events
                    disableImagePan();
                    // Hide pan hint
                    const panHint = document.getElementById('panHint');
                    if (panHint) {
                        panHint.style.display = 'none';
                    }
                }

                // Update zoom indicator if it exists
                const zoomIndicator = document.getElementById('zoomIndicator');
                if (zoomIndicator) {
                    zoomIndicator.textContent = `${Math.round(currentZoom * 100)}%`;
                }

                // Update zoom level display
                if (zoomLevel) {
                    zoomLevel.textContent = `${Math.round(currentZoom * 100)}%`;
                }

                // Show zoom indicator temporarily
                if (zoomIndicator) {
                    zoomIndicator.classList.add('show');
                    setTimeout(() => {
                        zoomIndicator.classList.remove('show');
                    }, 1500);
                }
            }

            // Variables for image panning
            let isDragging = false;
            let dragStartX = 0;
            let dragStartY = 0;
            let currentX = 0;
            let currentY = 0;

            // Setup pan/drag functionality for the image
            function setupImagePan() {
                if (!currentImageEl) return;

                // Add mouse down event listener
                currentImageEl.addEventListener('mousedown', startDrag);
                // Add mouse move event listener to the document
                document.addEventListener('mousemove', dragImage);
                // Add mouse up event listener to the document
                document.addEventListener('mouseup', stopDrag);

                // Add touch events for mobile
                currentImageEl.addEventListener('touchstart', startDragTouch);
                document.addEventListener('touchmove', dragImageTouch);
                document.addEventListener('touchend', stopDrag);
            }

            // Disable pan functionality
            function disableImagePan() {
                if (!currentImageEl) return;

                // Remove mouse event listeners
                currentImageEl.removeEventListener('mousedown', startDrag);
                document.removeEventListener('mousemove', dragImage);
                document.removeEventListener('mouseup', stopDrag);

                // Remove touch event listeners
                currentImageEl.removeEventListener('touchstart', startDragTouch);
                document.removeEventListener('touchmove', dragImageTouch);
                document.removeEventListener('touchend', stopDrag);
            }

            // Reset image position
            function resetImagePosition() {
                if (!currentImageEl) return;

                // Reset position variables
                currentX = 0;
                currentY = 0;

                // Reset CSS transform for position
                updateImagePosition();
            }

            // Start dragging on mouse down
            function startDrag(e) {
                // Don't drag if in crop mode
                if (cropMode) return;

                isDragging = true;
                dragStartX = e.clientX - currentX;
                dragStartY = e.clientY - currentY;

                // Prevent default behavior
                e.preventDefault();
            }

            // Handle touch start event
            function startDragTouch(e) {
                // Don't drag if in crop mode
                if (cropMode) return;

                if (e.touches.length === 1) {
                    isDragging = true;
                    const touch = e.touches[0];
                    dragStartX = touch.clientX - currentX;
                    dragStartY = touch.clientY - currentY;

                    // Prevent default behavior
                    e.preventDefault();
                }
            }

            // Drag the image on mouse move
            function dragImage(e) {
                if (!isDragging) return;

                // Calculate new position
                currentX = e.clientX - dragStartX;
                currentY = e.clientY - dragStartY;

                // Update image position
                updateImagePosition();

                // Prevent default behavior
                e.preventDefault();
            }

            // Handle touch move event
            function dragImageTouch(e) {
                if (!isDragging) return;

                if (e.touches.length === 1) {
                    const touch = e.touches[0];

                    // Calculate new position
                    currentX = touch.clientX - dragStartX;
                    currentY = touch.clientY - dragStartY;

                    // Update image position
                    updateImagePosition();

                    // Prevent default behavior
                    e.preventDefault();
                }
            }

            // Stop dragging on mouse up
            function stopDrag() {
                isDragging = false;
            }

            // Update the image position
            function updateImagePosition() {
                if (!currentImageEl) return;

                // Apply the transform with scale and translation
                currentImageEl.style.transform = `scale(${currentZoom}) translate(${currentX / currentZoom}px, ${currentY / currentZoom}px)`;
            }

            // Crop functionality
             cropMode = false;
            let cropper = null;
            const cropModeIndicator = document.getElementById('cropMode');

            function initCropper() {
                isInCropMode = true;
                cropMode = true;
                updateButtonStates();

                if (cropModeIndicator) cropModeIndicator.style.display = 'block';

                // Destroy existing cropper if any
                if (cropper) {
                    cropper.destroy();
                }

                // Initialize Cropper.js
                cropper = new Cropper(currentImageEl, {
                    aspectRatio: NaN,
                    viewMode: 1,
                    dragMode: 'crop',
                    autoCropArea: 0.8,
                    restore: false,
                    guides: true,
                    center: true,
                    highlight: true,
                    cropBoxMovable: true,
                    cropBoxResizable: true,
                    toggleDragModeOnDblclick: false,
                    ready: function() {
                        // Enable cropping mode immediately
                        this.cropper.crop();
                    }
                });
            }

            function destroyCropper() {
                isInCropMode = false;
                cropMode = false;
                updateButtonStates();

                if (cropModeIndicator) cropModeIndicator.style.display = 'none';

                if (cropper) {
                    cropper.destroy();
                    cropper = null;
                }
            }

            // Add a function to show toast notifications instead of alerts
            function showNotification(message, type = 'info', title = 'Notification') {
                const toast = document.getElementById('notificationToast');
                const toastTitle = document.getElementById('toastTitle');
                const toastMessage = document.getElementById('toastMessage');
                const toastIcon = document.getElementById('toastIcon');

                // Set icon based on type
                if (type === 'success') {
                    toastIcon.className = 'bi bi-check-circle-fill me-2 text-success';
                } else if (type === 'error') {
                    toastIcon.className = 'bi bi-exclamation-triangle-fill me-2 text-danger';
                } else if (type === 'warning') {
                    toastIcon.className = 'bi bi-exclamation-circle-fill me-2 text-warning';
                } else {
                    toastIcon.className = 'bi bi-info-circle-fill me-2 text-info';
                }

                // Set title and message
                toastTitle.textContent = title;
                toastMessage.textContent = message;

                // Show the toast
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
            }

            // Add a variable to track crop mode state
             isInCropMode = false;

            // Function to update button states based on crop mode
            function updateButtonStates() {
                const saveBtn = document.getElementById('saveBtn');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const saveAllBtn = document.getElementById('saveAllBtn');
                const labelInput = document.getElementById('labelInput');

                if (isInCropMode || cropMode) {
                    // Disable all navigation and save buttons in crop mode
                    if (saveBtn) saveBtn.disabled = true;
                    if (prevBtn) prevBtn.disabled = true;
                    if (nextBtn) nextBtn.disabled = true;
                    if (saveAllBtn) saveAllBtn.disabled = true;
                    if (labelInput) labelInput.disabled = true;
                } else {
                    // Re-enable buttons based on normal conditions
                    if (saveBtn) saveBtn.disabled = false;
                    if (prevBtn) prevBtn.disabled = currentIndex === 0;
                    if (labelInput) labelInput.disabled = false;

                    // In manual mode, next button is enabled only if there's a label
                    if (nextBtn) {
                        const img = images[currentIndex];
                        const hasLabel = labels[img.name] && labels[img.name].trim() !== '';
                        nextBtn.disabled = currentIndex === images.length - 1 || !hasLabel;
                    }

                    // Update save all button state
                    if (saveAllBtn) {
                        const labeledCount = Object.keys(labels).length;
                        if (verificationMode) {
                            // In verification mode, require user to be on the last image
                            saveAllBtn.disabled = labeledCount !== images.length || currentIndex !== images.length - 1;
                        } else {
                            // In manual mode, just require all images to be labeled
                            saveAllBtn.disabled = labeledCount !== images.length;
                        }
                    }
                }
            }

            // Confirm and save the crop
            function confirmCrop() {
                if (!cropper) return;

                try {
                    // Get the cropped canvas
                    const canvas = cropper.getCroppedCanvas({
                        maxWidth: 4096,
                        maxHeight: 4096,
                        fillColor: '#fff'
                    });

                    if (!canvas) {
                        showNotification('Failed to crop image. Please try again.', 'error', 'Crop Error');
                        return;
                    }

                    // Convert canvas to blob
                    canvas.toBlob(function(blob) {
                        // Create form data
                        const formData = new FormData();
                        formData.append('image', blob);
                        formData.append('image_path', images[currentIndex].path);
                        formData.append('session_id', sessionId);

                        // Show loading spinner in place of the image
                        const loadingSpinner = `
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="mt-2 text-primary">Saving crop...</div>
                            </div>
                        `;
                        const imageContainer = currentImageEl.parentElement;
                        imageContainer.insertAdjacentHTML('beforeend', loadingSpinner);

                        // Send to server
                        fetch('/api/save-cropped-image', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Remove loading spinner
                                const spinner = imageContainer.querySelector('.position-absolute');
                                if (spinner) spinner.remove();

                                // Exit crop mode
                                destroyCropper();

                                // Wait a brief moment to ensure the server has processed the save
                                setTimeout(() => {
                                    // Refresh the image with a new timestamp to force reload
                                    refreshImage();
                                    showNotification('Image cropped successfully!', 'success', 'Crop Success');
                                }, 500);
                            } else {
                                // Remove loading spinner
                                const spinner = imageContainer.querySelector('.position-absolute');
                                if (spinner) spinner.remove();

                                showNotification('Failed to save cropped image: ' + data.error, 'error', 'Crop Error');
                            }
                        })
                        .catch(error => {
                            // Remove loading spinner
                            const spinner = imageContainer.querySelector('.position-absolute');
                            if (spinner) spinner.remove();

                            console.error('Error saving cropped image:', error);
                            showNotification('Error saving cropped image. Please try again.', 'error', 'Crop Error');
                        });
                    }, 'image/jpeg', 0.95);
                } catch (error) {
                    console.error('Error during crop:', error);
                    showNotification('Error during crop. Please try again.', 'error', 'Crop Error');
                    destroyCropper();
                }
            }

            // Initialize UI controls
            const refreshImageBtn = document.getElementById('refreshImageBtn');

            // Add event listener for refresh image button
            if (refreshImageBtn) {
                refreshImageBtn.addEventListener('click', function() {
                    refreshImage();
                });
            }

            // Remove any cache status elements that might be present
            const cacheStatusEl = document.querySelector('.cache-status');
            if (cacheStatusEl) {
                cacheStatusEl.remove();
            }

            // Function to refresh the current image
            function refreshImage() {
                const img = images[currentIndex];
                const timestamp = new Date().getTime();
                currentImageEl.src = img.url + '?t=' + timestamp;

                // Add an onload handler to ensure the image is fully loaded
                currentImageEl.onload = function() {
                    // Clear the onload handler
                    currentImageEl.onload = null;
                };
            }

            // Add validation feedback removal on input
            if (labelInput) {
                labelInput.addEventListener('input', function() {
                    // Remove validation classes when user starts typing
                    this.classList.remove('is-invalid');

                    // Enable next button if there's text in the input field
                    nextBtn.disabled = currentIndex === images.length - 1 || this.value.trim() === '';
                });
            }
        });