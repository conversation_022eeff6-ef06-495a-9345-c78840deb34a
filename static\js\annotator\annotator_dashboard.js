document.addEventListener('DOMContentLoaded', function() {
    // Initialize typing animation
    startTypingAnimation();

    // Setup event listeners for buttons
    setupButtonListeners();
});

// Typing animation function
function startTypingAnimation() {
    // Define all phrases to cycle through
    const phrases = [
        "Teacher of AI",
        "Guiding the model to learn",
        "One label at a time"
    ];
    
    const textPart1 = document.getElementById('text-part1');
    
    if (!textPart1) {
        return; // Exit if element doesn't exist
    }
    
    // Initialize with first phrase
    let currentPhraseIndex = 0;
    
    // Start the animation cycle
    animateNextPhrase();
    
    function animateNextPhrase() {
        // Clear previous text
        textPart1.textContent = '';
        textPart1.classList.add('blinking-cursor');
        
        // Get the current phrase
        const currentPhrase = phrases[currentPhraseIndex];
        
        // Type the current phrase
        setTimeout(() => {
            textPart1.classList.remove('blinking-cursor');
            typeWriter(textPart1, currentPhrase, 0, 100, function() {
                // After typing is complete, add blinking cursor
                textPart1.classList.add('blinking-cursor');
                
                // Wait before showing next phrase
                setTimeout(() => {
                    // Move to next phrase (or back to first if at the end)
                    currentPhraseIndex = (currentPhraseIndex + 1) % phrases.length;
                    
                    // Start next phrase animation
                    animateNextPhrase();
                }, 2000); // Show each phrase for 2 seconds before transitioning
            });
        }, 500); // Brief pause before starting to type
    }
}

// Helper function for typing animation
function typeWriter(element, text, index, speed, callback) {
    if (index < text.length) {
        element.textContent += text.charAt(index);
        index++;
        setTimeout(function() {
            typeWriter(element, text, index, speed, callback);
        }, speed);
    } else if (callback) {
        callback();
    }
}

// Setup event listeners for dashboard buttons
function setupButtonListeners() {
    // Manual Labelling button
    const manualBtn = document.getElementById('manualLabellingBtn');
    if (manualBtn) {
        manualBtn.addEventListener('click', function() {
            window.location.href = this.getAttribute('data-href');
        });
    }
    
    // Verification button
    const verifyBtn = document.getElementById('verificationBtn');
    if (verifyBtn) {
        verifyBtn.addEventListener('click', function() {
            window.location.href = this.getAttribute('data-href');
        });
    }
    
    // Supervision button
    const superviseBtn = document.getElementById('supervisionBtn');
    if (superviseBtn) {
        superviseBtn.addEventListener('click', function() {
            window.location.href = this.getAttribute('data-href');
        });
    }
}

// Handle button actions
const actionButtons = document.querySelectorAll('.action-button');

actionButtons.forEach(button => {
    button.addEventListener('click', function() {
        const href = this.getAttribute('data-href');
        if (href) {
            window.location.href = href;
        }
    });
});

// Handle info buttons for instructions modal
const infoButtons = document.querySelectorAll('.info-button');

infoButtons.forEach(button => {
    button.addEventListener('click', function() {
        const mode = this.getAttribute('data-mode');
        const modal = document.getElementById('instructionsModal');
        
        if (modal) {
            // Update modal title based on mode
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle) {
                modalTitle.textContent = `${mode.charAt(0).toUpperCase() + mode.slice(1)} Mode Guidelines`;
            }
        }
    });
});

// Special handling for normal mode button
const normalModeBtn = document.getElementById('normalModeBtn');
if (normalModeBtn) {
    normalModeBtn.addEventListener('click', function() {
        const href = this.getAttribute('data-href');
        if (href) {
            window.location.href = href;
        }
    });
}

// Optional: Add hover effect to user avatar
const avatarCircle = document.querySelector('.avatar-circle');
if (avatarCircle) {
    avatarCircle.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
    });
    
    avatarCircle.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
}


