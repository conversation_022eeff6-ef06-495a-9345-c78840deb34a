$(document).ready(function() {
    const documentType = window.DOCUMENT_TYPE;
    const defaultModelType = window.MODEL_TYPE || 'standard';
    let currentDocIndex = 0;
    let processedCount = 0;
    let failedCount = 0;
    let documentsProcessing = true;
    let documentQueue = [];
    let corrections = {};
    
    // Define global variables for storing document and form data
    window.originalData = {};
    window.editedData = {};
    window.hasBeenEdited = false;
    window.driveLinks = window.driveLinks || {};

    // Function to download CSV data
    function downloadCSV() {
        // Get current document ID
        const currentDocId = document.getElementById('currentDocumentId').value;
        if (!currentDocId) {
            alert('No document currently selected');
            return;
        }
        
        // Create a timestamp for the filename
        const now = new Date();
        const timestamp = now.toISOString().replace(/[-:\.T]/g, '').substring(0, 14);
        
        // Get the current document name from the sidebar
        const documentItem = document.querySelector(`.document-item[data-id="${currentDocId}"]`);
        const fileName = documentItem ? documentItem.getAttribute('data-name') : 'document';
        
        // Clean up the filename to remove any invalid characters
        const cleanFileName = fileName.replace(/[^a-zA-Z0-9_\-\.]/g, '_');
        
        // Create a descriptive filename
        const downloadFilename = `${window.DOCUMENT_TYPE}_${cleanFileName}_${timestamp}.csv`;
        
        // Get form data from input fields
        const formData = {};
        document.querySelectorAll('#extractedFields input, #extractedFields select').forEach(input => {
            formData[input.getAttribute('name')] = input.value;
        });
        
        // Prepare data to send to server
        const dataToSend = {
            edited_data: formData,
            document_type: window.DOCUMENT_TYPE,
            document_id: currentDocId,
            filename: downloadFilename
        };
        
        console.log('Downloading with filename:', downloadFilename);
        
        // Send data to server
        fetch('/supervision/download_csv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataToSend)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = downloadFilename; // Set the filename here
            
            // Append to document, click and remove
            document.body.appendChild(a);
            a.click();
            
            // Small delay before removing to ensure download starts
            setTimeout(() => {
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
            }, 100);
            
            console.log('Download initiated with filename:', downloadFilename);
        })
        .catch(error => {
            console.error('Error downloading CSV:', error);
            alert('Error downloading CSV file. Please try again.');
        });
    }

    // Display model types in the UI
    function updateModelTypeDisplay(modelType) {
        // Update the model type display in the header
        const modelTypeEl = $('#current-model-type');
        modelTypeEl.text(`Processing Power: ${getModelTypeLabel(modelType)}`);
        modelTypeEl.removeClass('standard enhanced premium');
        modelTypeEl.addClass(modelType);
    }

    // Get human-readable model type label
    function getModelTypeLabel(modelType) {
        switch(modelType) {
            case 'standard': return 'Standard';
            case 'enhanced': return 'Enhanced';
            case 'premium': return 'Premium';
            default: return 'Standard';
        }
    }

    // Create field templates based on document type
    function getFieldsTemplate(data) {
        let fieldsHtml = '';

        if (documentType === 'passport') {
            fieldsHtml = `
                <div class="row mb-3">
                    <div class="col-12">
                        <label class="form-label">Link to File</label>
                        <input type="text" class="form-control editable-field" name="Link to File" value="${data['Link to File'] || ''}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Passport Country Code</label>
                        <input type="text" class="form-control editable-field" name="Passport Country Code" value="${data['Passport Country Code'] || ''}" maxlength="3">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Passport Type</label>
                        <input type="text" class="form-control editable-field" name="Passport Type" value="${data['Passport Type'] || ''}" maxlength="1">
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Passport Number</label>
                        <input type="text" class="form-control editable-field" name="Passport Number" value="${data['Passport Number'] || ''}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">First Name</label>
                        <input type="text" class="form-control editable-field" name="First Name" value="${data['First Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Family Name</label>
                        <input type="text" class="form-control editable-field" name="Family Name" value="${data['Family Name'] || ''}">
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Date of Birth</label>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Day</label>
                            <input type="text" class="form-control editable-field" name="Date of Birth Day" value="${data['Date of Birth Day'] || ''}" maxlength="2" placeholder="DD">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Month</label>
                            <input type="text" class="form-control editable-field" name="Date of Birth Month" value="${data['Date of Birth Month'] || ''}" maxlength="2" placeholder="MM">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Year</label>
                            <input type="text" class="form-control editable-field" name="Date of Birth Year" value="${data['Date of Birth Year'] || ''}" maxlength="4" placeholder="YYYY">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Place of Birth</label>
                        <input type="text" class="form-control editable-field" name="Place of Birth" value="${data['Place of Birth'] || ''}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Gender</label>
                        <select class="form-control editable-field" name="Gender">
                            <option value="">Select Gender</option>
                            <option value="M" ${data['Gender'] === 'M' ? 'selected' : ''}>M</option>
                            <option value="F" ${data['Gender'] === 'F' ? 'selected' : ''}>F</option>
                        </select>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Date of Issue</label>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Day</label>
                            <input type="text" class="form-control editable-field" name="Date of Issue Day" value="${data['Date of Issue Day'] || ''}" maxlength="2" placeholder="DD">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Month</label>
                            <input type="text" class="form-control editable-field" name="Date of Issue Month" value="${data['Date of Issue Month'] || ''}" maxlength="2" placeholder="MM">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Year</label>
                            <input type="text" class="form-control editable-field" name="Date of Issue Year" value="${data['Date of Issue Year'] || ''}" maxlength="4" placeholder="YYYY">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Date of Expiration</label>
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Day</label>
                            <input type="text" class="form-control editable-field" name="Date of Expiration Day" value="${data['Date of Expiration Day'] || ''}" maxlength="2" placeholder="DD">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Month</label>
                            <input type="text" class="form-control editable-field" name="Date of Expiration Month" value="${data['Date of Expiration Month'] || ''}" maxlength="2" placeholder="MM">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label small text-muted">Year</label>
                            <input type="text" class="form-control editable-field" name="Date of Expiration Year" value="${data['Date of Expiration Year'] || ''}" maxlength="4" placeholder="YYYY">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label">Authority</label>
                        <input type="text" class="form-control editable-field" name="Authority" value="${data['Authority'] || ''}">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Signature</label>
                        <select class="form-control editable-field" name="Signature">
                            <option value="">Select Option</option>
                            <option value="Yes" ${data['Signature'] === 'Yes' ? 'selected' : ''}>Yes</option>
                            <option value="No" ${data['Signature'] === 'No' ? 'selected' : ''}>No</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Stamp</label>
                        <select class="form-control editable-field" name="Stamp">
                            <option value="">Select Option</option>
                            <option value="Yes" ${data['Stamp'] === 'Yes' ? 'selected' : ''}>Yes</option>
                            <option value="No" ${data['Stamp'] === 'No' ? 'selected' : ''}>No</option>
                        </select>
                    </div>
                </div>
            `;
        } else if (documentType === 'check') {
            fieldsHtml = `
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Link to File</label>
                        <input type="text" class="form-control" name="Link to File" value="${data['Link to File'] || ''}">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Pic Date</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" name="Pic Date" value="${data['Pic Date'] || ''}" readonly>
                            <span class="input-group-text">
                                <i class="fas fa-calendar-alt"></i>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Download Date</label>
                        <div class="input-group">
                            <input type="text" class="form-control datepicker" name="Download Date" value="${data['Download Date'] || ''}" readonly>
                            <span class="input-group-text">
                                <i class="fas fa-calendar-alt"></i>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Bank Name</label>
                        <input type="text" class="form-control" name="Bank Name" value="${data['Bank Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Check Type</label>
                        <select class="form-control" name="Check Type">
                            <option value="" ${!data['Check Type'] ? 'selected' : ''}>Select Check Type</option>
                            <option value="Business" ${data['Check Type'] === 'Business' ? 'selected' : ''}>Business</option>
                            <option value="Government" ${data['Check Type'] === 'Government' ? 'selected' : ''}>Government</option>
                            <option value="Cashier" ${data['Check Type'] === 'Cashier' ? 'selected' : ''}>Cashier</option>
                            <option value="Personal" ${data['Check Type'] === 'Personal' ? 'selected' : ''}>Personal</option>
                            <option value="Money Order" ${data['Check Type'] === 'Money Order' ? 'selected' : ''}>Money Order</option>
                        </select>
                    </div>
                </div>

                <hr class="my-3">
                <h6 class="mb-2">Payor Information</h6>

                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">1st Payor First Name</label>
                        <input type="text" class="form-control" name="1st Payor First Name" value="${data['1st Payor First Name'] || data['Payor Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">1st Payor Family Name</label>
                        <input type="text" class="form-control" name="1st Payor Family Name" value="${data['1st Payor Family Name'] || ''}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">2nd Payor First Name</label>
                        <input type="text" class="form-control" name="2nd Payor First Name" value="${data['2nd Payor First Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">2nd Payor Family Name</label>
                        <input type="text" class="form-control" name="2nd Payor Family Name" value="${data['2nd Payor Family Name'] || ''}">
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">Payor Street Address</label>
                    <input type="text" class="form-control" name="Payor Street Address" value="${data['Payor Street Address'] || data['Payor Address'] || ''}">
                </div>
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payor City</label>
                        <input type="text" class="form-control" name="Payor City" value="${data['Payor City'] || ''}">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payor State</label>
                        <input type="text" class="form-control" name="Payor State" value="${data['Payor State'] || ''}">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payor Zip code</label>
                        <input type="text" class="form-control" name="Payor Zip code" value="${data['Payor Zip code'] || ''}">
                    </div>
                </div>

                <hr class="my-3">
                <h6 class="mb-2">Payee Information</h6>

                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">1st Payee First Name</label>
                        <input type="text" class="form-control" name="1st Payee First Name" value="${data['1st Payee First Name'] || data['Payee Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">1st Payee Family Name</label>
                        <input type="text" class="form-control" name="1st Payee Family Name" value="${data['1st Payee Family Name'] || ''}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">2nd Payee First Name</label>
                        <input type="text" class="form-control" name="2nd Payee First Name" value="${data['2nd Payee First Name'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">2nd Payee Family Name</label>
                        <input type="text" class="form-control" name="2nd Payee Family Name" value="${data['2nd Payee Family Name'] || ''}">
                    </div>
                </div>
                <div class="mb-2">
                    <label class="form-label">Payee Street Address</label>
                    <input type="text" class="form-control" name="Payee Street Address" value="${data['Payee Street Address'] || data['Payee Address'] || ''}">
                </div>
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payee City</label>
                        <input type="text" class="form-control" name="Payee City" value="${data['Payee City'] || ''}">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payee State</label>
                        <input type="text" class="form-control" name="Payee State" value="${data['Payee State'] || ''}">
                    </div>
                    <div class="col-md-4 mb-2">
                        <label class="form-label">Payee Zip Code</label>
                        <input type="text" class="form-control" name="Payee Zip Code" value="${data['Payee Zip Code'] || ''}">
                    </div>
                </div>

                <hr class="my-3">
                <h6 class="mb-2">Additional Information</h6>

                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Account Number</label>
                        <input type="text" class="form-control" name="Account Number" value="${data['Account Number'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Routing Number</label>
                        <input type="text" class="form-control" name="Routing Number" value="${data['Routing Number'] || ''}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Check Amount</label>
                        <input type="text" class="form-control" name="Check Amount" value="${data['Check Amount'] || data['Amount'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Check Number</label>
                        <input type="text" class="form-control" name="Check Number" value="${data['Check Number'] || ''}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Payee Type</label>
                        <input type="text" class="form-control" name="Payee Type" value="${data['Payee Type'] || ''}">
                    </div>
                    <div class="col-md-6 mb-2">
                        <label class="form-label">Market</label>
                        <input type="text" class="form-control" name="Market" value="${data['Market'] || ''}">
                    </div>
                </div>
            `;
        } else if (documentType === 'invoice') {
            fieldsHtml = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Invoice Number</label>
                        <input type="text" class="form-control" name="Invoice Number" value="${data['Invoice Number'] || ''}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Invoice Date</label>
                        <input type="text" class="form-control" name="Invoice Date" value="${data['Invoice Date'] || ''}">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Due Date</label>
                        <input type="text" class="form-control" name="Due Date" value="${data['Due Date'] || ''}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Payment Terms</label>
                        <input type="text" class="form-control" name="Payment Terms" value="${data['Payment Terms'] || ''}">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Vendor/Seller</label>
                    <input type="text" class="form-control" name="Vendor/Seller" value="${data['Vendor/Seller'] || ''}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Customer</label>
                    <input type="text" class="form-control" name="Customer" value="${data['Customer'] || ''}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Total Amount</label>
                    <input type="text" class="form-control" name="Total Amount" value="${data['Total Amount'] || ''}">
                </div>
            `;
        }

        return fieldsHtml;
    }

    // Initialize zoom and pan functionality
    function initializeImageControls() {
        const container = document.querySelector('.image-container');
        const img = document.getElementById('documentImage');
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const resetZoomBtn = document.getElementById('resetZoomBtn');
        const fitToScreenBtn = document.getElementById('fitToScreenBtn');
        const zoomLevelDisplay = document.querySelector('.zoom-level');

        let scale = 1;
        let isDragging = false;
        let startX, startY, translateX = 0, translateY = 0;

        // Update transform
        function updateTransform() {
            img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
            zoomLevelDisplay.textContent = `${Math.round(scale * 100)}%`;
        }

        // Reset zoom and position
        function resetZoom() {
            scale = 1;
            translateX = 0;
            translateY = 0;
            updateTransform();
        }

        // Fit image to screen
        function fitToScreen() {
            const containerRect = container.getBoundingClientRect();
            const imgRect = img.getBoundingClientRect();

            const scaleX = containerRect.width / img.naturalWidth;
            const scaleY = containerRect.height / img.naturalHeight;
            scale = Math.min(scaleX, scaleY);

            translateX = (containerRect.width - (img.naturalWidth * scale)) / 2;
            translateY = (containerRect.height - (img.naturalHeight * scale)) / 2;

            updateTransform();
        }

        // Zoom with mouse wheel
        container.addEventListener('wheel', (e) => {
            e.preventDefault();
            const rect = container.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Calculate zoom
            const delta = -Math.sign(e.deltaY) * 0.1;
            const newScale = Math.min(Math.max(0.1, scale + delta), 5);

            if (newScale !== scale) {
                // Calculate cursor position on image
                const mouseX = (x - translateX) / scale;
                const mouseY = (y - translateY) / scale;

                // Calculate new position to zoom into cursor
                translateX = x - mouseX * newScale;
                translateY = y - mouseY * newScale;

                scale = newScale;
                updateTransform();
            }
        });

        // Pan with mouse drag
        container.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left click only
                isDragging = true;
                container.classList.add('grabbing');
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            translateX = e.clientX - startX;
            translateY = e.clientY - startY;
            updateTransform();
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            container.classList.remove('grabbing');
        });

        // Zoom buttons
        zoomInBtn.addEventListener('click', () => {
            scale = Math.min(scale + 0.2, 5);
            updateTransform();
        });

        zoomOutBtn.addEventListener('click', () => {
            scale = Math.max(scale - 0.2, 0.1);
            updateTransform();
        });

        resetZoomBtn.addEventListener('click', resetZoom);
        fitToScreenBtn.addEventListener('click', fitToScreen);

        // Initialize image
        img.addEventListener('load', () => {
            fitToScreen();
            container.style.cursor = 'grab';
        });
    }

    // Initialize document list
    function initDocumentList() {
        $('.document-item').click(function() {
            const documentId = $(this).data('id');
            const modelType = $(this).data('model-type') || 'standard';

            // Set active state
            $('.document-item').removeClass('active');
            $(this).addClass('active');

            // Update model type display
            updateModelTypeDisplay(modelType);

            // Load document data
            loadDocumentData(documentId);
        });

        documentQueue = Array.from(document.querySelectorAll('.document-item'))
            .map(item => ({
                id: item.getAttribute('data-id'),
                name: item.getAttribute('data-name')
            }));

        updateProgressCounter();
        initializeImageControls();
    }

    // Update progress counter
    function updateProgressCounter() {
        const total = documentQueue.length;
        $('#progress-counter').text(`${processedCount + failedCount} of ${total} processed`);
    }

    // Poll server for document status
    function pollDocumentStatus() {
        if (!documentsProcessing) {
            return;
        }

        // Check status for all documents, not just current
        documentQueue.forEach((doc, index) => {
            const documentId = doc.id;
            const statusBadge = $(`.document-item[data-id="${documentId}"] .processing-status`);

            // Only check if status is still "Processing"
            if (statusBadge.text() === 'Processing') {
                $.ajax({
                    url: `/supervision/api/check-status/${documentId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.status === 'completed') {
                            // Update status to Ready
                            statusBadge.removeClass('bg-secondary').addClass('bg-success').text('Ready');

                            // If this is the first document and it's not displayed yet
                            if (index === 0 && $('#initialMessage').is(':visible')) {
                                displayDocument(documentId, response.data);
                            }

                            // If this is the next document after current, show notification
                            if (index === currentDocIndex + 1) {
                                // No notification needed
                            }
                        } else if (response.status === 'error') {
                            statusBadge.removeClass('bg-secondary').addClass('bg-danger').text('Error');
                            failedCount++;
                            updateProgressCounter();
                        }
                    }
                });
            }
        });

        // Continue polling if documents are still processing
        if (documentsProcessing) {
            setTimeout(pollDocumentStatus, 2000);
        }
    }

    // Display document data
    function displayDocument(documentId, data) {
        // Hide initial message and show document container
        $('#initialMessage').addClass('d-none');
        $('#documentDataContainer').removeClass('d-none');
        $('#completionMessage').addClass('d-none');

        // Set current document ID
        $('#currentDocumentId').val(documentId);

        // Update model type display using the model type from the data
        const modelType = data.model_type || window.modelTypes[documentId] || 'standard';
        updateModelTypeDisplay(modelType);

        // Display image with loading indicator
        const img = $('#documentImage');
        img.attr('src', `/supervision/document-image/${documentId}`);

        // Generate fields based on document type
        $('#extractedFields').html(getFieldsTemplate(data));

        // Initialize datepickers if document type is check
        if (documentType === 'check') {
            initializeDatepickers();
        }

        // Set the Drive link if available and make it editable
        const linkInput = $('[name="Link to File"]');
        if (linkInput.length) {
            // Remove readonly attribute to make it editable
            linkInput.removeAttr('readonly');
            linkInput.addClass('editable-field');
            
            // If we have a stored link, use it
            if (window.driveLinks && window.driveLinks[documentId]) {
                linkInput.val(window.driveLinks[documentId]);
            } 
            // Otherwise, if data has a link, use and store it
            else if (data['Link to File']) {
                linkInput.val(data['Link to File']);
                
                // Store for future use
                if (!window.driveLinks) window.driveLinks = {};
                window.driveLinks[documentId] = data['Link to File'];
            }
        }

        // Store the document data in corrections for later use
        corrections[documentId] = JSON.parse(JSON.stringify(data));

        // Update document list highlighting
        $('.document-item').removeClass('active');
        $(`.document-item[data-id="${documentId}"]`).addClass('active');

        // Scroll the active document into view in the sidebar
        const activeItem = $(`.document-item[data-id="${documentId}"]`);
        if (activeItem.length) {
            activeItem[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Add this function to validate dates
        function validateDateFields() {
            const dayInput = document.querySelector('[name="Date of Birth Day"]');
            const monthInput = document.querySelector('[name="Date of Birth Month"]');
            const yearInput = document.querySelector('[name="Date of Birth Year"]');
            
            // Also get expiration and issue date fields
            const expDayInput = document.querySelector('[name="Date of Expiration Day"]');
            const expMonthInput = document.querySelector('[name="Date of Expiration Month"]');
            const expYearInput = document.querySelector('[name="Date of Expiration Year"]');
            
            const issueDayInput = document.querySelector('[name="Date of Issue Day"]');
            const issueMonthInput = document.querySelector('[name="Date of Issue Month"]');
            const issueYearInput = document.querySelector('[name="Date of Issue Year"]');
            
            // Function to make date field editable and validate on change
            function setupDateField(dayField, monthField, yearField) {
                if (!dayField || !monthField || !yearField) return;
                
                // Remove any readonly attributes
                dayField.removeAttribute('readonly');
                monthField.removeAttribute('readonly');
                yearField.removeAttribute('readonly');
                
                // Make sure they're editable
                dayField.classList.add('editable-field');
                monthField.classList.add('editable-field');
                yearField.classList.add('editable-field');
                
                // Validate day (only on blur to allow typing)
                dayField.addEventListener('blur', function() {
                    let val = parseInt(this.value, 10);
                    if (isNaN(val) || val < 1) {
                        this.value = '01';
                    } else if (val > 31) {
                        this.value = '31';
                    } else if (this.value.length === 1) {
                        this.value = '0' + this.value;
                    }
                });
                
                // Allow typing any number
                dayField.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                // Validate month (only on blur to allow typing)
                monthField.addEventListener('blur', function() {
                    let val = parseInt(this.value, 10);
                    if (isNaN(val) || val < 1) {
                        this.value = '01';
                    } else if (val > 12) {
                        this.value = '12';
                    } else if (this.value.length === 1) {
                        this.value = '0' + this.value;
                    }
                });
                
                // Allow typing any number
                monthField.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                // Validate year (only on blur to allow typing)
                yearField.addEventListener('blur', function() {
                    let val = parseInt(this.value, 10);
                    const currentYear = new Date().getFullYear();
                    if (isNaN(val) || val < 1900) {
                        this.value = '1900';
                    } else if (val > currentYear + 100) { // Allow future dates for expiration
                        this.value = currentYear.toString();
                    }
                });
                
                // Allow typing any number
                yearField.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
            }
            
            // Setup all date fields
            setupDateField(dayInput, monthInput, yearInput);
            setupDateField(expDayInput, expMonthInput, expYearInput);
            setupDateField(issueDayInput, issueMonthInput, issueYearInput);
        }

        // Call this function after generating the form
        validateDateFields();

        // Enhance form fields
        enhanceFormFields();
        documentLoaded();
    }

    // Save document data
    function saveDocumentData() {
        const documentId = $('#currentDocumentId').val();
        if (!documentId) return;

        // Collect form data
        const formData = {};
        
        // Get ALL form fields including those outside #extractedDataForm
        $('#extractedFields input, #extractedFields select').each(function() {
            const name = $(this).attr('name');
            if (name) {
                formData[name] = $(this).val();
            }
        });
        
        // Ensure Drive link is included
        const linkInput = $('[name="Link to File"]');
        if (linkInput.length) {
            formData["Link to File"] = linkInput.val();
            
            // Make sure to store the link in window.driveLinks for future use
            if (!window.driveLinks) window.driveLinks = {};
            window.driveLinks[documentId] = linkInput.val();
        }
        
        // Check for corrections
        const originalItem = $(`.document-item[data-id="${documentId}"]`).data('original-data') || {};
        const correctionsList = {};

        Object.keys(formData).forEach(key => {
            if (originalItem[key] && formData[key] !== originalItem[key]) {
                correctionsList[key] = {
                    original: originalItem[key],
                    corrected: formData[key]
                };
            }
        });

        // Send to server
        $.ajax({
            url: '/supervision/api/save-document',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                image_id: documentId,
                data: formData,
                corrections: Object.keys(correctionsList).length > 0 ? correctionsList : null,
                verified: true
            }),
            success: function(response) {
                if (response.success) {
                    // Mark document as processed
                    $(`.document-item[data-id="${documentId}"] .processing-status`)
                        .removeClass('bg-success')
                        .addClass('bg-info')
                        .text('Saved');

                    processedCount++;
                    updateProgressCounter();

                    // Move to next document
                    moveToNextDocument();
                }
            },
            error: function() {
                alert('Error saving document data. Please try again.');
            }
        });
    }

    // Function to move to the next document
    function moveToNextDocument() {
        const documentId = $('#currentDocumentId').val();
        if (!documentId) return;

        if (currentDocIndex < documentQueue.length - 1) {
            currentDocIndex++;
            const nextDocId = documentQueue[currentDocIndex].id;
            const nextStatusBadge = $(`.document-item[data-id="${nextDocId}"] .processing-status`);

            // If next document is already ready or saved, display it immediately
            if (nextStatusBadge.text() === 'Ready' || nextStatusBadge.text() === 'Saved') {
                $.ajax({
                    url: `/supervision/api/check-status/${nextDocId}`,
                    type: 'GET',
                    success: function(nextResponse) {
                        if (nextResponse.status === 'completed') {
                            displayDocument(nextDocId, nextResponse.data);
                        }
                    }
                });
            } else if (nextStatusBadge.text() === 'Error') {
                // Skip error documents
                moveToNextDocument();
            } else {
                // If still processing, show loading
                $('#documentDataContainer').addClass('d-none');
                $('#initialMessage').removeClass('d-none');
            }
        } else {
            // All documents processed
            $('#documentDataContainer').addClass('d-none');
            $('#initialMessage').addClass('d-none');
            
            // Add centered-completion-layout class to container
            $('.ms-auto.w-100').addClass('centered-completion-layout');
            
            // Show completion message
            $('#completionMessage').removeClass('d-none');
            
            // Hide sidebar but keep main container visible
            $('.position-fixed.start-0.top-0.bottom-0.pt-5').addClass('d-none');
            $('nav.navbar').addClass('d-none');
            
            documentsProcessing = false;

            // Update only the first button (Process More Documents)
            $('#completionMessage .btn-primary').eq(0).attr('href', '/supervision/dashboard');
        }
    }

    // Event handler for document item click
    $(document).on('click', '.document-item', function() {
        const documentId = $(this).data('id');
        const statusText = $(this).find('.processing-status').text();

        // Only show ready or saved documents
        if (statusText === 'Ready' || statusText === 'Saved') {
            $.ajax({
                url: `/supervision/api/check-status/${documentId}`,
                type: 'GET',
                success: function(response) {
                    if (response.status === 'completed') {
                        displayDocument(documentId, response.data);

                        // Update current document index
                        currentDocIndex = documentQueue.findIndex(doc => doc.id === documentId);
                    }
                }
            });
        }
    });

    // Save button click handler
    $('#saveButton').click(saveDocumentData);

    // Save All button click handler
    $('#saveAllButton').click(function() {
        saveAllDocuments();
    });

    // Add event listeners for download buttons
    // Download buttons handlers - completely replace these handlers
    $('#downloadCsvBtn').off('click').on('click', function(event) {
        event.preventDefault();
        console.log('Download CSV button clicked');
        downloadCSV();
    });
    
    $('#downloadTxtBtn').off('click').on('click', function() {
        const documentId = $('#currentDocumentId').val();
        if (documentId) {
            window.location.href = `/supervision/download_txt/${documentId}`;
        }
    });

    // Add Next button click handler
    $('#nextButton').click(function() {
        moveToNextDocument();
    });

    // Add keyboard shortcuts for navigation
    $(document).keydown(function(e) {
        // Right arrow key for next document
        if (e.keyCode === 39) {
            $('#nextButton').click();
        }
        // Ctrl/Cmd + S for save and next
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
            e.preventDefault();
            $('#saveButton').click();
        }
    });

    // Initialize
    initDocumentList();
    pollDocumentStatus();

    // Track form field changes
    $(document).on('input', '.editable-field', function() {
        const fieldName = $(this).attr('name');
        if (!fieldName) return;
        
        const originalValue = window.originalData[fieldName];
        const currentValue = $(this).val();
        
        // Update the edited data
        window.editedData[fieldName] = currentValue;

        // Store the current document's form data in corrections
        const documentId = $('#currentDocumentId').val();
        if (documentId) {
            // Initialize if it doesn't exist
            if (!corrections[documentId]) {
                corrections[documentId] = {};
            }
            
            // Update the field in the corrections object
            corrections[documentId][fieldName] = currentValue;
        }
        
        // Check if value changed from original
        if (currentValue !== originalValue) {
            $(this).addClass('form-field-changed');
            window.hasBeenEdited = true;
            $('.edit-status').show();
        } else {
            $(this).removeClass('form-field-changed');
            // Check if any other fields are still edited
            const stillEdited = Object.keys(window.editedData).some(
                key => window.editedData[key] !== window.originalData[key]
            );
            if (!stillEdited) {
                window.hasBeenEdited = false;
                $('.edit-status').hide();
            }
        }
    });

    // Check status for all files in document queue
    document.querySelectorAll('.document-item').forEach(item => {
        const fileId = item.getAttribute('data-id');
        if (fileId) {
            checkStatus(fileId);
        }
    });

    // Add this function to check for available Bootstrap Modal
    function showSaveAllModal(html, callback) {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            // Bootstrap 5
            if (!$('#saveAllModal').length) {
                $('body').append(html);
            }
            const modal = new bootstrap.Modal(document.getElementById('saveAllModal'));
            modal.show();
            if (callback) callback(modal);
        } else {
            // Fallback if bootstrap is not available
            if (!$('#saveAllModal').length) {
                $('body').append(html);
            }
            $('#saveAllModal').addClass('show').css('display', 'block');
            if (callback) callback({
                hide: function() {
                    $('#saveAllModal').removeClass('show').css('display', 'none');
                }
            });
        }
    }
    
    // Save all documents function
    function saveAllDocuments() {
        const totalDocs = documentQueue.length;
        let savedCount = 0;
        let errorCount = 0;
        
        // Create a modal for showing progress - with transparent backdrop
        const modalHtml = `
            <div class="modal fade" id="saveAllModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Saving All Documents</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Saving <span id="saveProgress">0</span> of ${totalDocs} documents...</p>
                            <div class="save-progress">
                                <div class="save-progress-bar" style="width: 0%"></div>
                            </div>
                            <div id="saveStatus" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove any existing modal first to prevent duplicates
        $('#saveAllModal').remove();
        
        // Append new modal to body
        $('body').append(modalHtml);
        
        // Manually add the modal-open class to body but without the dark backdrop
        $('body').addClass('modal-open');
        
        // Show the modal using jQuery instead of Bootstrap's modal
        $('#saveAllModal').addClass('show').css('display', 'block');
        
        // Create a semi-transparent backdrop that doesn't cause blackout
        if (!$('.modal-backdrop-light').length) {
            $('body').append('<div class="modal-backdrop-light"></div>');
        }
        
        // Store the current document ID to return to it later
        const currentDocId = $('#currentDocumentId').val();
        
        // Save current document form data first
        if (currentDocId) {
            const formData = {};
            $('#extractedDataForm input[type="text"], #extractedDataForm select').each(function() {
                formData[$(this).attr('name')] = $(this).val();
            });
            
            // Add to documents to save
            corrections[currentDocId] = formData;
        }
        
        // Function to clean up the modal
        function closeModal() {
            $('#saveAllModal').removeClass('show').css('display', 'none');
            $('.modal-backdrop-light').remove();
            $('body').removeClass('modal-open');
        }
        
        // Close button event
        $(document).on('click', '#saveAllModal .btn-close, #saveAllModal .btn-secondary', function() {
            closeModal();
        });
        
        // Function to save each document
        function saveNextDocument(index) {
            if (index >= totalDocs) {
                // All documents processed
                $('#saveStatus').html(`<div class="alert alert-success">All documents saved successfully!</div>`);
                $('.save-progress-bar').css('width', '100%');
                
                // Show completion message and close modal after short delay
                setTimeout(function() {
                    closeModal();
                    // Hide document container and show completion message
                    $('#documentDataContainer').addClass('d-none');
                    $('#initialMessage').addClass('d-none');
                    
                    // Add centered-completion-layout class to container
                    $('.ms-auto.w-100').addClass('centered-completion-layout');
                    
                    // Show completion message
                    $('#completionMessage').removeClass('d-none');
                    
                    // Hide sidebar but keep main container visible
                    $('.position-fixed.start-0.top-0.bottom-0.pt-5').addClass('d-none');
                    $('nav.navbar').addClass('d-none');
                    
                    documentsProcessing = false;
                }, 1000);
                
                return;
            }
            
            const docId = documentQueue[index].id;
            const statusBadge = $(`.document-item[data-id="${docId}"] .processing-status`);
            
            // Skip documents that are still processing, have errors, or already saved
            if (statusBadge.text() === 'Processing' || statusBadge.text() === 'Error' || statusBadge.text() === 'Saved') {
                $('#saveStatus').append(`<p>Skipping ${documentQueue[index].name} (${statusBadge.text()})</p>`);
                
                // Update progress bar for skipped documents too
                $('#saveProgress').text(index + 1);
                $('.save-progress-bar').css('width', `${((index + 1) / totalDocs) * 100}%`);
                
                saveNextDocument(index + 1);
                return;
            }
            
            // Update progress
            $('#saveProgress').text(index + 1);
            $('.save-progress-bar').css('width', `${((index + 1) / totalDocs) * 100}%`);
            
            // Get form data for this document
            let formData = corrections[docId];
            
            // If we don't have form data for this document yet, fetch it
            if (!formData) {
                $.ajax({
                    url: `/supervision/api/check-status/${docId}`,
                    type: 'GET',
                    success: function(response) {
                        if (response.status === 'completed') {
                            formData = response.data;
                            
                            // Save the document
                            saveDocument(docId, formData, index);
                        } else {
                            // Skip this document
                            $('#saveStatus').append(`<p>Skipping ${documentQueue[index].name} (Not ready)</p>`);
                            saveNextDocument(index + 1);
                        }
                    },
                    error: function() {
                        errorCount++;
                        $('#saveStatus').append(`<p class="text-danger">Error loading ${documentQueue[index].name}</p>`);
                        saveNextDocument(index + 1);
                    }
                });
            } else {
                // Save the document with existing form data
                saveDocument(docId, formData, index);
            }
        }
        
        // Function to save a single document
        function saveDocument(docId, formData, index) {
            // Ensure we have all form data including Drive links
            if (window.driveLinks && window.driveLinks[docId]) {
                formData["Link to File"] = window.driveLinks[docId];
            }
            
            $.ajax({
                url: '/supervision/api/save-document',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    image_id: docId,
                    data: formData,
                    corrections: null, // No corrections when batch saving
                    verified: true
                }),
                success: function() {
                    savedCount++;
                    $(`.document-item[data-id="${docId}"] .processing-status`)
                        .removeClass('bg-success')
                        .addClass('bg-info')
                        .text('Saved');
                    
                    $('#saveStatus').append(`<p class="text-success">Saved ${documentQueue[index].name}</p>`);
                    // Auto-scroll to bottom of status area
                    const statusDiv = document.getElementById('saveStatus');
                    if (statusDiv) {
                        statusDiv.scrollTop = statusDiv.scrollHeight;
                    }
                    
                    // Update progress counter
                    processedCount++;
                    updateProgressCounter();
                    
                    // Process next document
                    saveNextDocument(index + 1);
                },
                error: function() {
                    errorCount++;
                    $('#saveStatus').append(`<p class="text-danger">Error saving ${documentQueue[index].name}</p>`);
                    
                    // Update progress bar even on error
                    $('#saveProgress').text(index + 1);
                    $('.save-progress-bar').css('width', `${((index + 1) / totalDocs) * 100}%`);
                    
                    // Continue with next document
                    saveNextDocument(index + 1);
                }
            });
        }
        
        // Start saving documents
        saveNextDocument(0);
    }
});

async function loadDocumentData(imageId) {
    const response = await fetch(`/supervision/api/check-status/${imageId}`);
    const data = await response.json();

    if (data.status === 'completed' && data.extracted_data) {
        // Parse the extracted data
        let extractedData = {};
        if (typeof data.extracted_data === 'string') {
            // Parse string data
            const lines = data.extracted_data.split('\n');
            lines.forEach(line => {
                if (line.includes(':')) {
                    const [key, value] = line.split(':').map(str => str.trim());
                    extractedData[key] = value;
                }
            });
        } else {
            extractedData = data.extracted_data;
        }

        // Fill in form fields
        document.querySelectorAll('input[type="text"]').forEach(input => {
            const fieldName = input.name;
            if (extractedData[fieldName]) {
                input.value = extractedData[fieldName];
            }
        });
    }
}

async function checkStatus(imageId) {
    try {
        const response = await fetch(`/supervision/api/check-status/${imageId}`);
        const data = await response.json();
        const statusBadge = $(`.document-item[data-id="${imageId}"] .processing-status`);

        if (data.status === 'completed') {
            statusBadge.removeClass('bg-secondary').addClass('bg-success').text('Ready');
        } else if (data.status === 'error') {
            statusBadge.removeClass('bg-secondary').addClass('bg-danger').text('Error');
        }
    } catch (error) {
        console.error('Error checking status:', error);
    }
}

// Update the initializeDatepickers function
function initializeDatepickers() {
    $('.datepicker').datepicker({
        format: 'dd/mm/yyyy',
        autoclose: true,
        todayHighlight: true,
        clearBtn: true,
        orientation: "bottom auto",
        endDate: new Date(), // This will disable all future dates
        templates: {
            leftArrow: '<i class="fas fa-chevron-left"></i>',
            rightArrow: '<i class="fas fa-chevron-right"></i>'
        }
    });

    // Add click handler for calendar icons
    $('.input-group-text').on('click', function() {
        $(this).closest('.input-group').find('.datepicker').datepicker('show');
    });
}

// Function to enhance form fields with editing capabilities
function enhanceFormFields() {
    // Make form fields editable
    const formInputs = document.querySelectorAll('#extractedFields input, #extractedFields select');
    formInputs.forEach(input => {
        input.classList.add('editable-field');
        
        // Store original value when first loaded
        const fieldName = input.getAttribute('name');
        if (fieldName) {
            window.originalData[fieldName] = input.value;
            window.editedData[fieldName] = input.value;
        }
    });
}

// Function called after document is loaded - moved from HTML
function documentLoaded() {
    // Reset edited state for new document
    window.hasBeenEdited = false;
    document.querySelector('.edit-status').style.display = 'none';
}
