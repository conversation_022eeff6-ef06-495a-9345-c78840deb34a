document.addEventListener('DOMContentLoaded', function() {
    // Local file upload handling
    const localUploadForm = document.getElementById('localUploadForm');
    localUploadForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(this);

        try {
            // Directly redirect to review page
            const response = await fetch('/supervision/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                window.location.href = '/supervision/review';  // Direct redirect to review page
            } else {
                alert('Error: ' + result.error);
            }
        } catch (error) {
            alert('Error uploading files: ' + error.message);
        }
    });

    const driveForm = document.getElementById('driveForm');
    const loadDriveFilesBtn = document.getElementById('loadDriveFiles');
    const driveFilesList = document.getElementById('driveFilesList');
    const selectAllBtn = document.getElementById('selectAllFiles');
    const deselectAllBtn = document.getElementById('deselectAllFiles');
    const processDriveFilesBtn = document.getElementById('processDriveFiles');
    let selectedFiles = new Set();

    // Track navigation state
    let currentFolderId = null;
    let folderHistory = [];
    let breadcrumbData = [];

    // Load Drive Files button handler
    loadDriveFilesBtn.addEventListener('click', async function() {
        const documentType = document.getElementById('driveDocumentType').value;
        if (!documentType) {
            showProcessingStatus('Please select a document type first', 0, true);
            return;
        }

        try {
            showProcessingStatus('Loading folders from Google Drive...', 0);
            // Reset navigation state
            currentFolderId = null;
            folderHistory = [];
            breadcrumbData = [];

            const response = await fetch('/supervision/list-drive-folders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ document_type: documentType })
            });

            const result = await response.json();
            if (result.success) {
                // Store breadcrumb data
                breadcrumbData = result.breadcrumb || [];
                currentFolderId = result.current_folder_id;

                // Display breadcrumb
                displayBreadcrumb(breadcrumbData);

                // Display folders and files separately
                displayDriveContents(result.folders, result.files);
                driveFilesList.style.display = 'block';
                showProcessingStatus('Folders loaded successfully', 100);
            } else {
                showProcessingStatus(result.error || 'Error loading folders', 0, true);
            }
        } catch (error) {
            showProcessingStatus('Error loading Drive folders: ' + error.message, 0, true);
        }
    });

    // Select All button handler
    selectAllBtn.addEventListener('click', function() {
        const checkboxes = driveFilesList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedFiles.add(checkbox.value);
        });
        updateProcessButton();
    });

    // Deselect All button handler
    deselectAllBtn.addEventListener('click', function() {
        const checkboxes = driveFilesList.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            selectedFiles.delete(checkbox.value);
        });
        updateProcessButton();
    });

    // Process Drive Files form submission
    driveForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        if (selectedFiles.size === 0) {
            alert('Please select at least one file to process');
            return;
        }

        const formData = new FormData(this);
        formData.append('selected_files', JSON.stringify(Array.from(selectedFiles)));

        // Add file links to form data
        const fileLinks = {};
        selectedFiles.forEach(fileId => {
            const checkbox = document.getElementById(`file_${fileId}`);
            if (checkbox) {
                fileLinks[fileId] = checkbox.dataset.link;
            }
        });
        formData.append('file_links', JSON.stringify(fileLinks));
        formData.append('current_folder_id', currentFolderId);
        try {
            // Directly redirect to review page
            const response = await fetch('/supervision/process-drive-files', {
                method: 'POST',
                body: formData
            });
            const result = await response.json();
            if (result.success) {
                window.location.href = '/supervision/review';  // Direct redirect to review page
            } else {
                alert('Error: ' + result.error);
            }
        } catch (error) {
            alert('Error processing Drive files: ' + error.message);
        }
    });

    // Display breadcrumb navigation
    function displayBreadcrumb(breadcrumbItems) {
        // Create breadcrumb container if it doesn't exist
        let breadcrumbContainer = document.getElementById('driveBreadcrumb');
        if (!breadcrumbContainer) {
            breadcrumbContainer = document.createElement('nav');
            breadcrumbContainer.id = 'driveBreadcrumb';
            breadcrumbContainer.className = 'mb-3 breadcrumb-container';
            breadcrumbContainer.setAttribute('aria-label', 'breadcrumb');

            // Insert before the list-group
            const listGroup = driveFilesList.querySelector('.list-group');
            driveFilesList.insertBefore(breadcrumbContainer, listGroup);
        }

        // Create the breadcrumb
        const ol = document.createElement('ol');
        ol.className = 'breadcrumb mb-0';

        // Add breadcrumb items
        breadcrumbItems.forEach((item, index) => {
            const li = document.createElement('li');
            li.className = 'breadcrumb-item';

            if (index === breadcrumbItems.length - 1) {
                // Last item is active
                li.classList.add('active');
                li.setAttribute('aria-current', 'page');
                li.textContent = item.name;
            } else {
                // Clickable items
                const a = document.createElement('a');
                a.href = '#';
                a.textContent = item.name;
                a.addEventListener('click', (e) => {
                    e.preventDefault();
                    navigateToFolder(item.id);
                });
                li.appendChild(a);
            }

            ol.appendChild(li);
        });

        breadcrumbContainer.innerHTML = '';
        breadcrumbContainer.appendChild(ol);
    }

    // Display both folders and files
    function displayDriveContents(folders, files) {
        const filesList = driveFilesList.querySelector('.list-group');
        filesList.innerHTML = '';

        // Add back button if we're in a subfolder
        if (currentFolderId) {
            addBackButton();
        }

        // First add folders
        if (folders && folders.length > 0) {
            // Add section header for folders
            const folderHeader = document.createElement('div');
            folderHeader.className = 'list-group-item folder-section-header';
            folderHeader.innerHTML = `<strong><i class="fas fa-folder me-2"></i>Folders</strong>`;
            filesList.appendChild(folderHeader);

            // Add folders
            folders.forEach(folder => {
                const div = document.createElement('div');
                div.className = 'list-group-item d-flex align-items-center p-2 folder-item';

                div.innerHTML = `
                    <div class="d-flex align-items-center flex-grow-1 cursor-pointer">
                        <div class="folder-icon me-3">
                            <i class="fas fa-folder fa-lg text-warning"></i>
                        </div>
                        <div class="d-flex flex-column flex-grow-1">
                            <div class="folder-name">
                                ${folder.name}
                            </div>
                        </div>
                    </div>
                `;

                // Add click handler for folder
                div.addEventListener('click', () => navigateToFolder(folder.id));

                filesList.appendChild(div);
            });
        }

        // Then add files
        if (files && files.length > 0) {
            // Add section header for files
            const fileHeader = document.createElement('div');
            fileHeader.className = 'list-group-item file-section-header';
            fileHeader.innerHTML = `<strong><i class="fas fa-file me-2"></i>Files</strong>`;
            filesList.appendChild(fileHeader);

            // Add files
            files.forEach(file => displayDriveFile(file, filesList));
        }

        // If no folders and no files, show empty message
        if ((!folders || folders.length === 0) && (!files || files.length === 0)) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'list-group-item text-center text-muted';
            emptyMessage.innerHTML = 'No items found in this folder';
            filesList.appendChild(emptyMessage);
        }
    }

    // Navigate to a specific folder
    async function navigateToFolder(folderId) {
        try {
            // Save current folder to history if we're navigating to a new folder
            if (currentFolderId !== null && currentFolderId !== folderId) {
                folderHistory.push(currentFolderId);
            }

            showProcessingStatus('Loading folder contents...', 0);
            const documentType = document.getElementById('driveDocumentType').value;

            const response = await fetch('/supervision/list-drive-folders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_id: folderId,
                    document_type: documentType
                })
            });

            const result = await response.json();
            if (result.success) {
                // Update current folder ID
                currentFolderId = result.current_folder_id;

                // Update breadcrumb
                breadcrumbData = result.breadcrumb || [];
                displayBreadcrumb(breadcrumbData);

                // Display folders and files
                displayDriveContents(result.folders, result.files);
                showProcessingStatus('Folder contents loaded successfully', 100);
            } else {
                showProcessingStatus(result.error || 'Error loading folder contents', 0, true);
            }
        } catch (error) {
            showProcessingStatus('Error loading folder contents: ' + error.message, 0, true);
        }
    }

    // Add back button to navigate to parent folder
    function addBackButton() {
        const filesList = driveFilesList.querySelector('.list-group');
        const backButton = document.createElement('div');
        backButton.className = 'list-group-item d-flex align-items-center p-2 back-button mb-2';
        backButton.innerHTML = `
            <div class="d-flex align-items-center cursor-pointer">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Parent Folder
            </div>
        `;

        backButton.addEventListener('click', async () => {
            // Get parent folder ID from breadcrumb
            if (breadcrumbData.length > 1) {
                // Navigate to parent folder (second to last item in breadcrumb)
                const parentIndex = breadcrumbData.length - 2;
                const parentFolder = breadcrumbData[parentIndex];
                navigateToFolder(parentFolder.id);
            } else {
                // If no parent in breadcrumb, go to root
                navigateToFolder(null);
            }
        });

        filesList.insertBefore(backButton, filesList.firstChild);
    }

    // Display a single file item
    function displayDriveFile(file, container) {
        const div = document.createElement('div');
        div.className = 'list-group-item d-flex align-items-center p-2 file-item';

        // Determine file type and icon
        const fileType = file.mimeType ? file.mimeType.split('/')[1].toLowerCase() : 'unknown';
        const isImage = file.mimeType && file.mimeType.startsWith('image/');

        // Create thumbnail container with fixed dimensions
        const thumbnailHtml = `
            <div class="file-preview me-3">
                ${isImage && file.thumbnailLink ?
                    `<img src="${file.thumbnailLink.replace('=s220', '=s120')}" alt="${file.name}"
                         style="width: 100%; height: 100%; object-fit: cover;"
                         onerror="this.onerror=null; this.src='data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDE1IDEwIDIxIDMgMTUiPjwvcG9seWxpbmU+PC9zdmc+'; this.style.padding='10px';">` :
                    `<i class="fas ${getFileIcon(fileType)} fa-lg text-secondary"></i>`
                }
            </div>
        `;

        div.innerHTML = `
            <div class="d-flex align-items-center flex-grow-1">
                ${thumbnailHtml}
                <div class="d-flex flex-column flex-grow-1">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${file.id}"
                               data-link="${file.webViewLink || ''}" id="file_${file.id}">
                        <label class="form-check-label text-truncate" for="file_${file.id}" style="max-width: 300px;">
                            ${file.name}
                        </label>
                    </div>
                    <small class="text-muted">${fileType.toUpperCase()}</small>
                </div>
                ${file.webViewLink ?
                    `<a href="${file.webViewLink}" target="_blank" class="btn btn-sm btn-outline-secondary ms-2"
                        title="Open in Google Drive">
                        <i class="fas fa-external-link-alt"></i>
                    </a>` : ''
                }
            </div>
        `;

        const checkbox = div.querySelector('input[type="checkbox"]');
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedFiles.add(this.value);
            } else {
                selectedFiles.delete(this.value);
            }
            updateProcessButton();
        });

        container.appendChild(div);
    }
    // Helper function to get appropriate icon based on file type
    function getFileIcon(fileType) {
        const iconMap = {
            'pdf': 'fa-file-pdf',
            'jpeg': 'fa-file-image',
            'jpg': 'fa-file-image',
            'png': 'fa-file-image',
            'gif': 'fa-file-image',
            'doc': 'fa-file-word',
            'docx': 'fa-file-word',
            'xls': 'fa-file-excel',
            'xlsx': 'fa-file-excel',
            'default': 'fa-file-alt'
        };
        return iconMap[fileType] || iconMap.default;
    }

    function updateProcessButton() {
        processDriveFilesBtn.disabled = selectedFiles.size === 0;
    }

    function showProcessingStatus(message, progress, isError = false) {
        const statusDiv = document.getElementById('processingStatus');
        const progressBar = statusDiv.querySelector('.progress-bar');
        const statusMessage = document.getElementById('statusMessage');

        statusDiv.style.display = 'block';
        progressBar.style.width = progress + '%';
        statusMessage.textContent = message;

        if (isError) {
            statusMessage.classList.add('text-danger');
        } else {
            statusMessage.classList.remove('text-danger');
        }
    }

    // Add CSS styles
    const style = document.createElement('style');
    style.textContent = `
        .cursor-pointer {
            cursor: pointer;
        }

        .folder-item:hover, .file-item:hover {
            background-color: rgba(0,0,0,0.05);
        }

        .back-button {
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .back-button:hover {
            background-color: #e9ecef;
        }

        .breadcrumb-container {
            background-color: #f8f9fa;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            border: 1px solid rgba(0,0,0,0.125);
        }

        .breadcrumb {
            margin-bottom: 0;
        }

        .folder-section-header, .file-section-header {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }

        .folder-section-header {
            border-bottom: 1px solid rgba(0,0,0,0.125);
            margin-top: 0.5rem;
        }

        .file-section-header {
            border-bottom: 1px solid rgba(0,0,0,0.125);
            margin-top: 1rem;
        }

        .file-preview {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            border-radius: 4px;
            background-color: #f8f9fa;
        }

        .folder-name {
            font-weight: 500;
        }
    `;
    document.head.appendChild(style);

    // Update file count display when files are selected
    const filesInput = document.getElementById('files');
    if (filesInput) {
        filesInput.addEventListener('change', function() {
            const fileCount = this.files.length;
            const fileCountDisplay = document.getElementById('selectedFileCount').querySelector('span');
            const uploadWrapper = document.querySelector('.file-upload-wrapper');

            if (fileCount > 0) {
                fileCountDisplay.textContent = fileCount + (fileCount === 1 ? ' File Selected' : ' Files Selected');
                fileCountDisplay.classList.remove('bg-primary');
                fileCountDisplay.classList.add('bg-success');

                // Show the file count and update the upload wrapper appearance
                uploadWrapper.classList.add('files-selected');
            } else {
                fileCountDisplay.textContent = '0 Files Selected';
                fileCountDisplay.classList.remove('bg-success');
                fileCountDisplay.classList.remove('bg-primary');

                // Hide the file count and reset the upload wrapper appearance
                uploadWrapper.classList.remove('files-selected');
            }
        });
    }

    // Also add file counter for Drive files
    let selectedDriveFilesCount = 0;

    // Function to update drive files count
    function updateDriveFilesCount() {
        const selectedFiles = document.querySelectorAll('#driveFilesList .list-group-item.active');
        selectedDriveFilesCount = selectedFiles.length;

        // Update button text to show count
        const processButton = document.getElementById('processDriveFiles');
        if (selectedDriveFilesCount > 0) {
            processButton.innerHTML = `<i class="fas fa-cloud-download-alt me-2"></i>Process ${selectedDriveFilesCount} Selected Files`;
            processButton.disabled = false;
        } else {
            processButton.innerHTML = `<i class="fas fa-cloud-download-alt me-2"></i>Process Selected Files`;
            processButton.disabled = true;
        }
    }

    // Handle file selection in drive list
    if (driveFilesList) {
        driveFilesList.addEventListener('click', function(e) {
            if (e.target.closest('.list-group-item')) {
                // Toggle active class for clicked item
                e.target.closest('.list-group-item').classList.toggle('active');
                updateDriveFilesCount();
            }
        });

        // Handle select all/deselect all buttons
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                document.querySelectorAll('#driveFilesList .list-group-item').forEach(item => {
                    item.classList.add('active');
                });
                updateDriveFilesCount();
            });
        }

        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                document.querySelectorAll('#driveFilesList .list-group-item').forEach(item => {
                    item.classList.remove('active');
                });
                updateDriveFilesCount();
            });
        }
    }

    // Processing Power Guide toggle functionality
    const toggleBtn = document.getElementById('toggleGuideBtn');
    const guideContent = document.getElementById('powerGuideContent');

    if (toggleBtn && guideContent) {
        // Initialize button text based on collapse state
        function updateButtonText() {
            if (guideContent.classList.contains('show')) {
                toggleBtn.innerHTML = '<i class="fas fa-times-circle me-1"></i> Hide Guide';
            } else {
                toggleBtn.innerHTML = '<i class="fas fa-info-circle me-1"></i> Show Guide';
            }
        }

        // Listen for Bootstrap collapse events
        guideContent.addEventListener('shown.bs.collapse', updateButtonText);
        guideContent.addEventListener('hidden.bs.collapse', updateButtonText);

        // Add click handling for the power cards to select the corresponding option
        document.querySelectorAll('.power-card').forEach(card => {
            card.addEventListener('click', function() {
                // Get the power level from the card's class
                const powerLevel = this.classList.contains('standard-card') ? 'standard' :
                                  this.classList.contains('enhanced-card') ? 'enhanced' : 'premium';

                // Set the value in both model type dropdowns
                document.getElementById('modelType').value = powerLevel;
                document.getElementById('driveModelType').value = powerLevel;

                // Highlight the selected card
                document.querySelectorAll('.power-card').forEach(c => c.classList.remove('selected-power'));
                this.classList.add('selected-power');

                // Show a visual confirmation
                const confirmMessage = document.createElement('div');
                confirmMessage.className = 'power-select-confirmation';
                confirmMessage.textContent = powerLevel.charAt(0).toUpperCase() + powerLevel.slice(1) + ' processing power selected';

                // Remove any existing confirmation messages
                document.querySelectorAll('.power-select-confirmation').forEach(el => el.remove());

                // Add the new confirmation message
                this.appendChild(confirmMessage);

                // Remove the confirmation message after 2 seconds
                setTimeout(() => {
                    confirmMessage.remove();
                }, 2000);
            });
        });
    }
});