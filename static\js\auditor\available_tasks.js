document.addEventListener('DOMContentLoaded', function() {
    const selectionForm = document.getElementById('selectionForm');
    const toggleFormBtn = document.getElementById('toggleFormBtn');
    const tasksContainer = document.getElementById('tasks-container');
    const verificationModeSelect = document.getElementById('verification-mode');
    const datasetSelect = document.getElementById('dataset');

    // Store tasks in a global variable for access from other scripts
    window.currentTasks = [];

    console.log('Page loaded with verification mode:', verificationModeSelect.value);
    console.log('Dataset options count:', datasetSelect.options.length);
    console.log('Dataset disabled:', datasetSelect.disabled);

    // Enable dataset dropdown if verification mode is already selected
    if (verificationModeSelect.value) {
        datasetSelect.disabled = false;
        console.log('Enabling dataset dropdown because verification mode is:', verificationModeSelect.value);
    }

    // Verification mode change handler
    verificationModeSelect.addEventListener('change', function() {
        const verificationMode = this.value;

        if (verificationMode) {
            // Show loading state for dataset dropdown
            datasetSelect.disabled = true;
            datasetSelect.innerHTML = '<option value="">Loading datasets...</option>';

            // Instead of redirecting, fetch the data via AJAX and update the datasets dropdown
            $.get('/auditor/available-tasks?mode=' + verificationMode, function(html) {
                // Parse the HTML to get the dataset options
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newDatasetSelect = doc.getElementById('dataset');

                if (newDatasetSelect) {
                    // Update the dataset options
                    datasetSelect.innerHTML = newDatasetSelect.innerHTML;
                    datasetSelect.disabled = false;

                    // Update URL without reloading the page
                    const newUrl = window.location.pathname + '?mode=' + verificationMode;
                    history.pushState({}, '', newUrl);

                    console.log('Datasets loaded for mode:', verificationMode);
                    console.log('Dataset options count after update:', datasetSelect.options.length);
                } else {
                    console.error('Could not find dataset select element in response');
                    datasetSelect.innerHTML = '<option value="">Error loading datasets</option>';
                }
            }).fail(function() {
                console.error('Failed to fetch datasets');
                datasetSelect.innerHTML = '<option value="">Error loading datasets</option>';
                datasetSelect.disabled = true;
            });
        } else {
            // Reset and disable all dropdowns
            datasetSelect.disabled = true;
            datasetSelect.innerHTML = '<option value="">Select a dataset...</option>';
            document.getElementById('image-folder').disabled = true;
            document.getElementById('image-folder').innerHTML = '<option value="">Select image folder</option>';
            document.getElementById('verifier').disabled = true;
            document.getElementById('verifier').innerHTML = '<option value="">Select a verifier</option>';
            document.getElementById('json-file-select').disabled = true;
            document.getElementById('json-file-select').innerHTML = '<option value="">Select a verification file...</option>';
            document.getElementById('load-tasks').disabled = true;
        }
    });

    // Dataset change handler
    datasetSelect.addEventListener('change', function() {
        const datasetId = this.value;
        const imageFolder = document.getElementById('image-folder');
        const verifier = document.getElementById('verifier');
        const jsonFileSelect = document.getElementById('json-file-select');
        const loadTasksBtn = document.getElementById('load-tasks');

        if (datasetId) {
            // Load image folders
            imageFolder.disabled = true;
            imageFolder.innerHTML = '<option value="">Loading folders...</option>';

            $.get('/auditor/api/image-folders', { mode: verificationModeSelect.value }, function(response) {
                if (response.success) {
                    const options = ['<option value="">Select image folder</option>'];
                    response.folders.forEach(folder => {
                        options.push(`<option value="${folder.id}">${folder.name}</option>`);
                    });
                    imageFolder.innerHTML = options.join('');
                    imageFolder.disabled = false;
                } else {
                    showAlert("Error loading image folders", "danger");
                }
            });

            // Load verifiers
            verifier.disabled = true;
            verifier.innerHTML = '<option value="">Loading verifiers...</option>';

            $.post('/api/get_verifiers', { dataset_id: datasetId, mode: verificationModeSelect.value }, function(response) {
                if (response.success) {
                    const options = ['<option value="">Select a verifier</option>'];
                    response.verifiers.forEach(verifier => {
                        options.push(`<option value="${verifier.id}">${verifier.name}</option>`);
                    });
                    verifier.innerHTML = options.join('');
                    verifier.disabled = false;
                } else {
                    showAlert("Error loading verifiers", "danger");
                }
            });
        } else {
            // Reset dependent dropdowns
            imageFolder.disabled = true;
            imageFolder.innerHTML = '<option value="">Select image folder</option>';
            verifier.disabled = true;
            verifier.innerHTML = '<option value="">Select a verifier</option>';
            jsonFileSelect.disabled = true;
            jsonFileSelect.innerHTML = '<option value="">Select a verification file...</option>';
            loadTasksBtn.disabled = true;
        }
    });

    // Image folder change handler
    document.getElementById('image-folder').addEventListener('change', function() {
        checkEnableLoadButton();
    });

    // Verifier change handler
    document.getElementById('verifier').addEventListener('change', function() {
        const verifierId = this.value;
        const jsonFileSelect = document.getElementById('json-file-select');

        if (verifierId && datasetSelect.value) {
            // Load verification files
            jsonFileSelect.disabled = true;
            jsonFileSelect.innerHTML = '<option value="">Loading files...</option>';

            $.get(`/auditor/api/verification-files/${datasetSelect.value}/${verifierId}`,
                {
                    mode: verificationModeSelect.value,
                    _t: new Date().getTime() // Add cache-busting timestamp
                },
                function(response) {
                    if (response.success) {
                        const options = ['<option value="">Select a verification file...</option>'];
                        response.files.forEach(file => {
                            // Add status indicator with color coding
                            const statusClass = file.status === 'Processed' ? 'text-success' : 'text-danger';
                            const statusIcon = file.status === 'Processed' ? '✓' : '✗';
                            const statusInfo = ` (${file.status})`;

                            // Store file status as data attribute for later use
                            options.push(`<option value="${file.path}"
                                data-status="${file.status}"
                                class="${statusClass}">
                                ${statusIcon} ${file.name}${statusInfo}
                            </option>`);
                        });
                        jsonFileSelect.innerHTML = options.join('');
                        jsonFileSelect.disabled = false;

                        // Add custom styling to the select options
                        const style = document.createElement('style');
                        style.textContent = `
                            .text-success { color: green; }
                            .text-danger { color: red; }
                            select option.text-success { background-color: #d4edda; }
                            select option.text-danger { background-color: #f8d7da; }
                        `;
                        document.head.appendChild(style);
                    } else {
                        showAlert("Error loading verification files", "danger");
                    }
                }
            );
        } else {
            jsonFileSelect.disabled = true;
            jsonFileSelect.innerHTML = '<option value="">Select a verification file...</option>';
        }
        checkEnableLoadButton();
    });

    // JSON file change handler
    document.getElementById('json-file-select').addEventListener('change', function() {
        checkEnableLoadButton();
    });

    // Function to check if load button should be enabled
    function checkEnableLoadButton() {
        const loadTasksBtn = document.getElementById('load-tasks');
        const verifier = document.getElementById('verifier');
        const jsonFileSelect = document.getElementById('json-file-select');

        loadTasksBtn.disabled = !(datasetSelect.value && verifier.value && jsonFileSelect.value);
    }

    // Use the showAlert function from utils.js
    // If it's not available, define a fallback
    if (typeof window.showAlert !== 'function') {
        console.warn('showAlert function not found in utils.js, using fallback');
        window.showAlert = function(message, type, duration = 5000) {
            const alertHTML = `
                <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;

            const alertContainer = document.createElement('div');
            alertContainer.innerHTML = alertHTML;
            document.querySelector('.container-fluid').prepend(alertContainer.firstChild);

            // Auto-dismiss after duration
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, duration);
        };
    }

    // Handle form submission
    document.getElementById('selection-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Validation
        if (!verificationModeSelect.value || !datasetSelect.value || !document.getElementById('verifier').value || !document.getElementById('json-file-select').value) {
            alert('Please complete all required fields');
            return;
        }

        // Get form data
        const dataset_id = datasetSelect.value;
        const verifier_id = document.getElementById('verifier').value;
        const image_folder = document.getElementById('image-folder').value;
        const jsonFileSelect = document.getElementById('json-file-select');
        const json_file = jsonFileSelect.value;
        const verification_mode = verificationModeSelect.value;

        // Check if the selected file has already been processed
        const selectedOption = jsonFileSelect.options[jsonFileSelect.selectedIndex];
        const fileStatus = selectedOption.getAttribute('data-status');

        if (fileStatus === 'Processed') {
            // Show warning modal with simplified information
            const confirmProcess = confirm(
                `Warning: This file has already been processed. \n\n` +
                `Do you still want to proceed? \n\n` +
                `Click OK to continue or Cancel to select a different file.`
            );

            if (!confirmProcess) {
                return; // User canceled, don't proceed
            }
        }

        // Show loading state
        document.getElementById('tasks-loading').style.display = 'block';
        document.getElementById('no-tasks-message').style.display = 'none';
        document.getElementById('tasks-list').style.display = 'none';
        tasksContainer.style.display = 'block';

        console.log('Requesting tasks with parameters:', {
            dataset_id,
            verifier_id,
            image_folder,
            json_file,
            verification_mode
        });

        // Instead of using the tasks endpoint, let's use the json-content endpoint to explicitly load from the JSON file
        $.ajax({
            url: '/auditor/api/json-content',
            method: 'POST',
            data: {
                json_path: json_file,
                image_folder: image_folder,
                verification_mode: verification_mode
            },
            success: function(response) {
                console.log('JSON content response:', response);
                document.getElementById('tasks-loading').style.display = 'none';

                if (response.success && response.tasks && response.tasks.length > 0) {
                    // Clear the global variable before calling renderTasks
                    window.currentTasks = [];

                    // Get file status information
                    const selectedOption = jsonFileSelect.options[jsonFileSelect.selectedIndex];
                    const fileStatus = selectedOption.getAttribute('data-status');

                    // Add file status information to the tasks container
                    const statusBadge = document.createElement('div');
                    statusBadge.className = `alert ${fileStatus === 'Processed' ? 'alert-success' : 'alert-danger'} mt-2 mb-3`;
                    statusBadge.innerHTML = fileStatus === 'Processed' ?
                        `<i class="bi bi-check-circle-fill me-2"></i><strong>Status:</strong> Processed` :
                        `<i class="bi bi-x-circle-fill me-2"></i><strong>Status:</strong> Not Processed`;

                    // Insert the status badge at the top of the tasks list
                    const tasksList = document.getElementById('tasks-list');
                    if (tasksList.firstChild) {
                        tasksList.insertBefore(statusBadge, tasksList.firstChild);
                    } else {
                        tasksList.appendChild(statusBadge);
                    }

                    // Render the tasks (this will also update the global variable)
                    renderTasks(response.tasks);

                    // Update task count
                    document.getElementById('task-count').textContent = response.tasks.length + ' tasks';

                    // Show the tasks list
                    document.getElementById('tasks-list').style.display = 'block';

                    // Verify the Save All button has a click handler using jQuery
                    $("#save-all-btn").off('click').on('click', function() {
                        console.log('Save All button clicked from post-load handler');
                        window.saveAllLabels(window.currentTasks);
                    });
                } else {
                    // Show no tasks message
                    document.getElementById('no-tasks-message').style.display = 'block';
                    document.getElementById('task-count').textContent = '0 tasks';
                    if (response.message) {
                        showAlert(response.message, 'warning');
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error loading tasks:', error);
                document.getElementById('tasks-loading').style.display = 'none';
                document.getElementById('no-tasks-message').style.display = 'block';
                document.getElementById('task-count').textContent = '0 tasks';
                showAlert('Error loading tasks: ' + error, 'danger');
            }
        });

        // Collapse the form
        toggleSelectionForm();
    });

    // Function to render tasks in the table
    function renderTasks(tasks) {
        // Store tasks in global variable for access by other scripts
        window.currentTasks = tasks || [];
        console.log('Tasks stored globally:', window.currentTasks.length);

        const tableBody = document.querySelector('#image-review-table tbody');
        tableBody.innerHTML = '';

        tasks.forEach(function(task, index) {
            const row = document.createElement('tr');
            row.setAttribute('data-task-id', task.task_id);

            // Image cell
            const imgCell = document.createElement('td');
            const imgContainer = document.createElement('div');
            imgContainer.className = 'image-container';

            const img = document.createElement('img');
            img.className = 'task-thumbnail';

            // Use the correct endpoint to fetch images from NAS
            if (task.image_path) {
                const nasPath = encodeURIComponent(task.image_path);
                console.log('Setting image source with path:', nasPath);
                img.src = `/api/get-nas-image?nas_path=${nasPath}`;
                img.setAttribute('data-image-path', task.image_path);

                // Add error handling for image loading
                img.onerror = function() {
                    console.error('Failed to load image:', task.image_path);
                    this.src = '/static/img/placeholder.png';
                    this.alt = 'Image Load Error';
                    showAlert(`Failed to load image: ${task.image_path}`, 'warning');
                };
            } else {
                img.src = '/static/img/placeholder.png';
            }

            img.alt = 'Task Image';
            img.setAttribute('data-bs-toggle', 'modal');
            img.setAttribute('data-bs-target', '#imagePreviewModal');

            imgContainer.appendChild(img);
            imgCell.appendChild(imgContainer);

            // Labels cell
            const labelsCell = document.createElement('td');
            const labelsDiv = document.createElement('div');
            labelsDiv.className = 'editable-label';
            labelsDiv.contentEditable = true;

            // Handle different formats of labels
            let labelText = '';
            if (typeof task.labels === 'string') {
                labelText = task.labels;
            } else if (typeof task.labels === 'object' && task.labels !== null) {
                try {
                    labelText = JSON.stringify(task.labels, null, 2);
                } catch (e) {
                    console.error('Error stringifying labels:', e);
                    labelText = 'Error parsing labels';
                }
            } else {
                labelText = 'No labels available';
            }

            labelsDiv.textContent = labelText;
            labelsDiv.setAttribute('data-original-content', labelText);

            labelsCell.appendChild(labelsDiv);

            // Add cells to row
            row.appendChild(imgCell);
            row.appendChild(labelsCell);

            // Add row to table
            tableBody.appendChild(row);
        });

        // Event listener for save button is now centralized in a single location
        // to prevent duplicate event handlers
    }

    // Function to save all changes - making it globally accessible
    window.saveAllChanges = function(tasks) {
        console.log('saveAllChanges called with tasks:', tasks ? tasks.length : 'none');

        // If no tasks provided, use the stored tasks
        if (!tasks || tasks.length === 0) {
            tasks = window.currentTasks || [];
            console.log('Using stored tasks:', window.currentTasks ? window.currentTasks.length : 0);
        }

        if (!tasks || tasks.length === 0) {
            console.error('No tasks available to save');
            showAlert('No tasks to save', 'warning');
            return;
        }

        // Get comments if present
        const comments = document.getElementById('batchComments')?.value || '';

        // Show saving indicator
        showAlert('Saving labels...', 'info');

        // Show loading state on button
        const saveBtn = document.getElementById('save-all-btn');
        if (saveBtn) {
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
        }

        // Update the status badge to show 'Processing'
        const existingStatusBadge = document.querySelector('#tasks-list .alert');
        if (existingStatusBadge) {
            existingStatusBadge.className = 'alert alert-warning mt-2 mb-3';
            existingStatusBadge.innerHTML = '<i class="bi bi-hourglass-split me-2"></i><strong>Status:</strong> Processing...';
        }

        // Update task labels from editable content
        const rows = document.querySelectorAll('#image-review-table tbody tr');
        rows.forEach(function(row) {
            const taskId = row.getAttribute('data-task-id');
            const labelsDiv = row.querySelector('.editable-label');

            if (labelsDiv) {
                const currentContent = labelsDiv.textContent;
                const taskIndex = tasks.findIndex(t => t.task_id === taskId);
                if (taskIndex !== -1) {
                    tasks[taskIndex].labels = currentContent;
                }
            }
        });

        // Prepare the data to send
        const data = {
            tasks: tasks,
            folder_name: document.getElementById('image-folder').value,
            json_path: document.getElementById('json-file-select').value,
            verifier: document.getElementById('verifier').value,
            comments: comments
        };

        console.log('Sending data to server:', data);

        // Send to server
        $.ajax({
            url: '/auditor/api/save-labels',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(response) {
                console.log('Server response:', response);
                if (response.success) {
                    // Remove any existing notifications first
                    document.querySelectorAll('.alert').forEach(el => {
                        if (bootstrap.Alert) {
                            const bsAlert = new bootstrap.Alert(el);
                            bsAlert.close();
                        } else {
                            el.remove();
                        }
                    });

                    // Show a single success notification
                    showAlert(`Labels saved successfully via ${response.connector_type} to ${response.saved_path}`, 'success', 8000);

                    // Update the status badge to show 'Processed'
                    const existingStatusBadge = document.querySelector('#tasks-list .alert');
                    if (existingStatusBadge) {
                        existingStatusBadge.className = 'alert alert-success mt-2 mb-3';
                        existingStatusBadge.innerHTML = `<i class="bi bi-check-circle-fill me-2"></i><strong>Status:</strong> Processed`;
                    }

                    // Add "Proceed to Next Task" button below the Save All Labels button
                    // First, remove any existing next task button
                    const existingNextBtn = document.getElementById('next-task-btn');
                    if (existingNextBtn) {
                        existingNextBtn.remove();
                    }

                    // Create and add the next task button
                    const nextTaskBtn = document.createElement('button');
                    nextTaskBtn.id = 'next-task-btn';
                    nextTaskBtn.className = 'btn btn-primary mt-2';
                    nextTaskBtn.innerHTML = '<i class="bi bi-arrow-right me-1"></i> Proceed to Next Task';

                    // Add the button after the save-all-btn in the action-row
                    const actionRow = document.querySelector('.action-row');
                    if (actionRow) {
                        // Create container for the next task button with proper styling
                        const btnContainer = document.createElement('div');
                        btnContainer.className = 'd-flex justify-content-end mt-3 mb-2';
                        btnContainer.appendChild(nextTaskBtn);

                        // Insert after the action row
                        actionRow.parentNode.insertBefore(btnContainer, actionRow.nextSibling);

                        // Add event listener to the next task button
                        nextTaskBtn.addEventListener('click', function() {
                            // Hide the current task container
                            document.getElementById('tasks-container').style.display = 'none';

                            // Show the selection form again
                            document.getElementById('selectionForm').classList.remove('collapsed');
                            const toggleBtn = document.getElementById('toggleFormBtn');
                            if (toggleBtn) {
                                toggleBtn.innerHTML = '<i class="bi bi-chevron-up"></i> Hide Selection Form';
                                toggleBtn.classList.remove('visible');
                            }

                            // Clear the current task data
                            window.currentTasks = [];

                            // Store the current selections
                            const currentDatasetId = datasetSelect.value;
                            const currentVerifierId = document.getElementById('verifier').value;

                            // Force refresh of the verification files to get updated status
                            if (currentVerifierId && currentDatasetId) {
                                const jsonFileSelect = document.getElementById('json-file-select');
                                jsonFileSelect.disabled = true;
                                jsonFileSelect.innerHTML = '<option value="">Refreshing files...</option>';

                                // Add a timestamp parameter to prevent caching
                                $.get(`/auditor/api/verification-files/${currentDatasetId}/${currentVerifierId}`,
                                    {
                                        mode: verificationModeSelect.value,
                                        _t: new Date().getTime() // Cache-busting timestamp
                                    },
                                    function(response) {
                                        if (response.success) {
                                            const options = ['<option value="">Select a verification file...</option>'];
                                            response.files.forEach(file => {
                                                // Add status indicator with color coding
                                                const statusClass = file.status === 'Processed' ? 'text-success' : 'text-danger';
                                                const statusIcon = file.status === 'Processed' ? '✓' : '✗';
                                                const statusInfo = ` (${file.status})`;

                                                options.push(`<option value="${file.path}"
                                                    data-status="${file.status}"
                                                    class="${statusClass}">
                                                    ${statusIcon} ${file.name}${statusInfo}
                                                </option>`);
                                            });
                                            jsonFileSelect.innerHTML = options.join('');
                                            jsonFileSelect.disabled = false;
                                            console.log('Refreshed file list with updated status information');
                                        } else {
                                            showAlert("Error refreshing verification files", "danger");
                                        }
                                    }
                                );
                            }

                            // Focus on the load button to guide the user
                            document.getElementById('load-tasks').focus();

                            // Remove this button
                            btnContainer.remove();
                        });
                    }

                    // Reset comments field
                    if (document.getElementById('batchComments')) {
                        document.getElementById('batchComments').value = '';
                    }

                    // Update original content to reflect saved state
                    rows.forEach(function(row) {
                        const labelsDiv = row.querySelector('.editable-label');
                        if (labelsDiv) {
                            const currentContent = labelsDiv.textContent;
                            labelsDiv.setAttribute('data-original-content', currentContent);

                            // Add visual indication that the row is saved
                            row.classList.add('bg-light');

                            // Add a saved badge if it doesn't exist
                            if (!row.querySelector('.status-badge')) {
                                const statusBadge = document.createElement('div');
                                statusBadge.className = 'status-badge position-absolute top-0 end-0 m-2';
                                statusBadge.innerHTML = '<span class="badge bg-success">Saved</span>';
                                const imgContainer = row.querySelector('.image-container');
                                if (imgContainer) {
                                    imgContainer.style.position = 'relative';
                                    imgContainer.appendChild(statusBadge);
                                }
                            }
                        }
                    });

                    // Add a small delay and reload the audit history panel if it exists
                    setTimeout(() => {
                        if (window.refreshAuditHistory && typeof window.refreshAuditHistory === 'function') {
                            window.refreshAuditHistory();
                        }
                    }, 1000);
                } else {
                    showAlert('Error: ' + (response.message || 'Unknown error'), 'danger');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error details:', xhr.responseText);
                showAlert('Error saving labels: ' + error, 'danger');
            },
            complete: function() {
                // Reset button state
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = '<i class="bi bi-save me-1"></i>Save All Labels';
                }
            }
        });
    };

    // For backward compatibility with main.js - alias saveAllLabels to saveAllChanges
    window.saveAllLabels = window.saveAllChanges;

    // Toggle form button click handler
    toggleFormBtn.addEventListener('click', toggleSelectionForm);

    // Function to toggle form visibility
    function toggleSelectionForm() {
        selectionForm.classList.toggle('collapsed');
        const isCollapsed = selectionForm.classList.contains('collapsed');
        toggleFormBtn.innerHTML = `
            <i class="bi bi-chevron-${isCollapsed ? 'down' : 'up'}"></i>
            ${isCollapsed ? 'Show' : 'Hide'} Selection Form
        `;
        toggleFormBtn.classList.add('visible');
    }
});


//image preview

document.addEventListener('DOMContentLoaded', function () {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Centralized event handler for the Save All button
    $("#save-all-btn").off('click').on('click', function() {
        console.log('Save All Labels button clicked');
        if (window.saveAllLabels) {
            window.saveAllLabels(window.currentTasks);
        }
    });

    // Connect Save Comments button to save process
    const saveCommentsBtn = document.getElementById('saveCommentsBtn');
    if (saveCommentsBtn) {
        saveCommentsBtn.addEventListener('click', function() {
            // Get comments text
            const comments = document.getElementById('batchComments').value;
            // Show confirmation message
            showAlert('Comments saved. They will be included with your next Save All Labels.', 'info');
            // Close the modal
            const commentsModal = bootstrap.Modal.getInstance(document.getElementById('commentsModal'));
            if (commentsModal) {
                commentsModal.hide();
            }
        });
    }

    // Handle image preview modal
    const imagePreviewModal = document.getElementById('imagePreviewModal');
    if (imagePreviewModal) {
        imagePreviewModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imagePath = button.getAttribute('data-image-path');
            const previewImage = document.getElementById('previewImage');
            const imageLoadError = document.getElementById('imageLoadError');

            // Hide error message initially
            imageLoadError.style.display = 'none';

            // Set the preview image source using the correct endpoint
            if (imagePath) {
                const nasPath = encodeURIComponent(imagePath);
                console.log('Loading preview image with path:', nasPath);
                previewImage.src = `/api/get-nas-image?nas_path=${nasPath}`;

                // Show error message if image fails to load
                previewImage.onerror = function() {
                    console.error('Failed to load preview image:', imagePath);
                    this.src = '/static/img/placeholder.png';
                    imageLoadError.style.display = 'block';
                    imageLoadError.textContent = `Failed to load image: ${imagePath}`;
                };

                // Hide error if image loads successfully
                previewImage.onload = function() {
                    imageLoadError.style.display = 'none';
                };
            } else {
                previewImage.src = button.src; // Use the thumbnail source as fallback
            }

            // Reset zoom
            previewImage.style.transform = 'scale(1)';
        });

        // Zoom controls
        let currentZoom = 1;
        let isDragging = false;
        let startX, startY, translateX = 0, translateY = 0;
        const previewImg = document.getElementById('previewImage');

        function updatePreviewTransform() {
            if (previewImg) {
                previewImg.style.transform = `scale(${currentZoom}) translate(${translateX/currentZoom}px, ${translateY/currentZoom}px)`;
            }
        }

        document.getElementById('zoomIn')?.addEventListener('click', function() {
            currentZoom += 0.1;
            updatePreviewTransform();
            if (currentZoom > 1 && previewImg) {
                previewImg.style.cursor = 'grab';
            }
        });

        document.getElementById('zoomOut')?.addEventListener('click', function() {
            currentZoom = Math.max(0.1, currentZoom - 0.1);
            if (currentZoom <= 1) {
                translateX = 0;
                translateY = 0;
                if (previewImg) {
                    previewImg.style.cursor = 'default';
                }
            }
            updatePreviewTransform();
        });

        document.getElementById('resetZoom')?.addEventListener('click', function() {
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            updatePreviewTransform();
            if (previewImg) {
                previewImg.style.cursor = 'default';
            }
        });

        // Add mouse wheel zoom
        previewImg?.addEventListener('wheel', function(e) {
            e.preventDefault();
            if (e.deltaY < 0) {
                // Zoom in
                currentZoom += 0.1;
                this.style.cursor = 'grab';
            } else {
                // Zoom out
                currentZoom = Math.max(0.1, currentZoom - 0.1);
                if (currentZoom <= 1) {
                    translateX = 0;
                    translateY = 0;
                    this.style.cursor = 'default';
                }
            }
            updatePreviewTransform();
        });

        // Add drag functionality to preview image
        previewImg?.addEventListener('mousedown', function(e) {
            if (currentZoom <= 1) return;

            isDragging = true;
            startX = e.clientX - translateX;
            startY = e.clientY - translateY;
            this.style.cursor = 'grabbing';
            e.preventDefault();
        });

        previewImg?.addEventListener('mousemove', function(e) {
            if (!isDragging) return;

            translateX = e.clientX - startX;
            translateY = e.clientY - startY;
            updatePreviewTransform();
        });

        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                if (previewImg) {
                    previewImg.style.cursor = currentZoom > 1 ? 'grab' : 'default';
                }
            }
        });

        imagePreviewModal.addEventListener('hidden.bs.modal', function() {
            // Reset variables when modal is closed
            currentZoom = 1;
            translateX = 0;
            translateY = 0;
            isDragging = false;
        });
    }

    // Add inline image zoom with mouse wheel for task thumbnails
    const setupImageZoom = () => {
        const thumbnails = document.querySelectorAll('.task-thumbnail');
        thumbnails.forEach(img => {
            let imgZoom = 1;
            let isDragging = false;
            let startX, startY, translateX = 0, translateY = 0;

            img.addEventListener('wheel', function(e) {
                e.preventDefault();

                if (e.deltaY < 0) {
                    // Zoom in
                    imgZoom += 0.1;
                } else {
                    // Zoom out
                    imgZoom = Math.max(0.5, imgZoom - 0.1);

                    // If zooming out below certain threshold, reset position
                    if (imgZoom <= 1) {
                        translateX = 0;
                        translateY = 0;
                    }
                }

                updateImageTransform(this);
                this.style.cursor = imgZoom > 1 ? 'grab' : 'default';
            });

            // Mouse down - start dragging
            img.addEventListener('mousedown', function(e) {
                if (imgZoom <= 1) return; // Only allow dragging when zoomed in

                isDragging = true;
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
                this.style.cursor = 'grabbing';
                e.preventDefault(); // Prevent image drag default behavior
            });

            // Mouse move - perform dragging
            img.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                translateX = e.clientX - startX;
                translateY = e.clientY - startY;
                updateImageTransform(this);
            });

            // Mouse up - stop dragging
            img.addEventListener('mouseup', function() {
                isDragging = false;
                this.style.cursor = 'grab';
            });

            // Mouse leave - stop dragging
            img.addEventListener('mouseleave', function() {
                isDragging = false;
                this.style.cursor = imgZoom > 1 ? 'grab' : 'default';
            });

            // Function to apply transform with both zoom and translation
            function updateImageTransform(element) {
                element.style.transform = `scale(${imgZoom}) translate(${translateX/imgZoom}px, ${translateY/imgZoom}px)`;
                element.style.transformOrigin = 'center center';
            }
        });
    };

    // Call it on page load
    setupImageZoom();

    // Re-apply zoom functionality when new images are loaded
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                setupImageZoom();
            }
        });
    });

    const tableBody = document.querySelector('#image-review-table tbody');
    if (tableBody) {
        observer.observe(tableBody, { childList: true, subtree: true });

        // Also observe when the Save All button appears
        observer.observe(document.querySelector('.action-row'), { childList: true, subtree: true });
    }
});

