document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('auditTable');
    
    if (searchInput && table) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
            
            for (let row of rows) {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            }
        });
    }
    
    // Initialize refresh function
    window.refreshAuditHistory = function() {
        console.log('Refreshing audit history...');
        
        // If we're on the history page, reload it
        if (window.location.pathname.includes('/auditor/history')) {
            // Show loading indicator if needed
            const historyTable = document.getElementById('auditTable');
            if (historyTable) {
                const tbody = historyTable.querySelector('tbody');
                if (tbody) {
                    const loadingRow = document.createElement('tr');
                    loadingRow.id = 'loading-row';
                    loadingRow.innerHTML = '<td colspan="8" class="text-center py-3"><div class="spinner-border spinner-border-sm text-secondary me-2"></div> Refreshing history...</td>';
                    tbody.prepend(loadingRow);
                }
            }
            
            // Fetch updated history data
            fetch(window.location.href)
                .then(response => response.text())
                .then(html => {
                    // Parse the HTML to get the new table content
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newTable = doc.getElementById('auditTable');
                    
                    // Replace the current table with the new one
                    if (newTable && historyTable) {
                        historyTable.innerHTML = newTable.innerHTML;
                        console.log('History refreshed successfully');
                    } else {
                        // Just refresh the page if we can't update the table
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Error refreshing history:', error);
                    // Remove loading indicator
                    document.getElementById('loading-row')?.remove();
                });
        }
    };
});