/**
 * Sidebar toggle functionality for auditor interface
 * Controls the collapsible sidebar behavior and saves state to localStorage
 */
document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');

    // Check for saved state
    const sidebarState = localStorage.getItem('sidebarCollapsed');
    if (sidebarState === 'true') {
        body.classList.add('sidebar-collapsed');
    }

    // Add click event listener to toggle button
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            body.classList.toggle('sidebar-collapsed');
            // Save state
            localStorage.setItem('sidebarCollapsed', body.classList.contains('sidebar-collapsed'));
        });
    }
});
