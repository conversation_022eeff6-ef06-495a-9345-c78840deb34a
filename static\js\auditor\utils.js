
/**
 * Shows an alert message that automatically dismisses after a timeout
 * 
 * @param {string} message - The message to display
 * @param {string} type - The alert type (success, danger, warning, info)
 * @param {number} timeout - Time in milliseconds before auto-dismissing (default: 5000)
 */
function showAlert(message, type, timeout = 5000) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const alertContainer = document.createElement('div');
    alertContainer.innerHTML = alertHTML;
    document.querySelector('.container-fluid').prepend(alertContainer.firstChild);
    
    // Auto-dismiss after timeout
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (bootstrap.Alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            } else {
                alert.remove();
            }
        });
    }, timeout);
}
