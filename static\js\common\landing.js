document.addEventListener('DOMContentLoaded', function() {
    // Navbar scroll effect - keeping this for usability
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.landing-navbar');
        if (window.scrollY > 10) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add subtle fade-in animation to feature cards
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * (index + 1));
    });

    // Mobile menu toggle functionality
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-nav');

    if (mobileMenuToggle && mainNav) {
        mobileMenuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            mainNav.classList.toggle('show');
        });
    }

    // Initialize the tagline animation
    initializeTaglineAnimation();
});

// Tagline animation for alternating text
function initializeTaglineAnimation() {
    const taglineLeftElement = document.getElementById('tagline-left');
    const taglineRightElement = document.getElementById('tagline-right');

    if (!taglineLeftElement || !taglineRightElement) {
        console.warn('Tagline animation elements not found.');
        return;
    }

    const leftMessages = [
        'Your Data',
        'Your Database',
        'Your Privacy'
    ];
    const rightMessages = [
        'Our AI Processing',
        'Our Human Validation',
        'Our Accountability'
    ];

    let currentLeftIndex = 0;
    let currentRightIndex = 0;
    const fadeDuration = 500; // Must match CSS transition duration (0.5s * 1000)
    const visibleDuration = 3000; // How long text stays visible

    function cycleLeftTagline() {
        // Fade out
        taglineLeftElement.style.opacity = 0;

        setTimeout(() => {
            // Change text after fade out
            currentLeftIndex = (currentLeftIndex + 1) % leftMessages.length;
            taglineLeftElement.textContent = leftMessages[currentLeftIndex];

            // Fade in
            taglineLeftElement.style.opacity = 1;

            // Schedule next cycle
            setTimeout(cycleLeftTagline, visibleDuration + fadeDuration);
        }, fadeDuration);
    }

    function cycleRightTagline() {
        // Fade out
        taglineRightElement.style.opacity = 0;

        setTimeout(() => {
            // Change text after fade out
            currentRightIndex = (currentRightIndex + 1) % rightMessages.length;
            taglineRightElement.textContent = rightMessages[currentRightIndex];

            // Fade in
            taglineRightElement.style.opacity = 1;

            // Schedule next cycle
            setTimeout(cycleRightTagline, visibleDuration + fadeDuration);
        }, fadeDuration);
    }

    // Start the animations after a short delay
    setTimeout(cycleLeftTagline, visibleDuration);
    // Optionally add a slight delay to the right side for staggering
    setTimeout(cycleRightTagline, visibleDuration + 100);
}

// Legacy tagline typing animation (kept for reference but not used)
function initializeTaglineTyping() {
    const line1Element = document.querySelector('.tagline-line-1');
    const line2Element = document.querySelector('.tagline-line-2');
    const line1Text = "YOUR DATA";
    const line2Text = "YOUR DATABASE";
    const typeSpeed = 100; // ms per character
    const deleteSpeed = 60; // ms per character (often faster than typing)
    const linePause = 1500; // ms pause after typing a line
    const deletePause = 700; // ms pause after deleting a line
    const cyclePause = 1000; // ms pause before restarting cycle

    if (!line1Element || !line2Element) {
        console.warn('Tagline animation elements not found.');
        return;
    }

    function typeLine(element, text, callback) {
        let charIndex = 0;
        element.textContent = ''; // Clear previous text

        function typeChar() {
            if (charIndex < text.length) {
                element.textContent += text.charAt(charIndex);
                charIndex++;
                setTimeout(typeChar, typeSpeed);
            } else {
                // Typing finished for this line
                if (callback) setTimeout(callback, linePause);
            }
        }
        typeChar();
    }

    function deleteLine(element, callback) {
        let text = element.textContent;
        let charIndex = text.length;

        function deleteChar() {
            if (charIndex > 0) {
                element.textContent = text.substring(0, charIndex - 1);
                charIndex--;
                setTimeout(deleteChar, deleteSpeed);
            } else {
                // Deleting finished for this line
                if (callback) setTimeout(callback, deletePause);
            }
        }
        deleteChar();
    }

    function startCycle() {
        // Reset text content
        line1Element.textContent = '';
        line2Element.textContent = '';

        // Updated Sequence
        typeLine(line1Element, line1Text, () => {
            typeLine(line2Element, line2Text, () => {
                deleteLine(line2Element, () => { // Delete line 2 first
                    deleteLine(line1Element, () => { // Then delete line 1
                        setTimeout(startCycle, cyclePause);
                    });
                });
            });
        });
    }

    // Start the first cycle
    startCycle();
}
