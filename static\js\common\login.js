document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const themeToggle = document.getElementById('themeToggle');
    const spinner = document.querySelector('.spinner-border');
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggle.checked = true;
    }
    
    // Toggle password visibility
    passwordToggle.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = passwordToggle.querySelector('i');
        if (type === 'text') {
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }
    });
    
    // Toggle theme
    themeToggle.addEventListener('change', function() {
        const newTheme = this.checked ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
    });
    
    // Form validation
    loginForm.addEventListener('submit', function(event) {
        let isValid = true;
        
        if (!usernameInput.value.trim()) {
            usernameInput.classList.add('is-invalid');
            isValid = false;
        } else {
            usernameInput.classList.remove('is-invalid');
        }
        
        if (!passwordInput.value.trim()) {
            passwordInput.classList.add('is-invalid');
            isValid = false;
        } else {
            passwordInput.classList.remove('is-invalid');
        }
        
        if (isValid) {
            // Show loading spinner
            spinner.style.display = 'inline-block';
            
            // Get the username to check if it might be an admin
            const username = usernameInput.value.trim();
            
            // Make an AJAX request to check if the user is an admin
            fetch('/check-user-role', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username: username }),
            })
            .then(response => response.json())
            .then(data => {
                // Only show flash message for non-admin users
                if (data.role !== 'admin') {
                    // Show flash message
                    const flashMessage = document.getElementById('loginFlashMessage');
                    flashMessage.style.display = 'block';
                    
                    // Array of loading messages
                    const loadingMessages = [
                        "Please hold on while we assign the task to you.",
                        "Preparing your workspace...",
                        "Authenticating your credentials...",
                        "Loading your profile...",
                        "Almost there..."
                    ];
                    
                    // Function to cycle through messages
                    let messageIndex = 0;
                    const messageInterval = setInterval(() => {
                        document.getElementById('flashMessageText').textContent = loadingMessages[messageIndex];
                        messageIndex = (messageIndex + 1) % loadingMessages.length;
                    }, 2000);
                    
                    // Store the interval ID in a global variable so it can be cleared if needed
                    window.loginMessageInterval = messageInterval;
                }
            })
            .catch(error => {
                // In case of error, default to showing the message (fail safe)
                console.error('Error checking user role:', error);
                
                // Show flash message as fallback
                const flashMessage = document.getElementById('loginFlashMessage');
                flashMessage.style.display = 'block';
                
                // Single static message in case of error
                document.getElementById('flashMessageText').textContent = "Please wait while we process your login...";
            });
            
            // Disable the button to prevent multiple submissions
            const submitButton = loginForm.querySelector('button[type="submit"]');
            submitButton.disabled = true;
        } else {
            event.preventDefault();
        }
    });
    
    // Real-time validation
    usernameInput.addEventListener('input', function() {
        if (usernameInput.value.trim()) {
            usernameInput.classList.remove('is-invalid');
        }
    });
    
    passwordInput.addEventListener('input', function() {
        if (passwordInput.value.trim()) {
            passwordInput.classList.remove('is-invalid');
        }
    });
    
    // Add subtle fade-in animation to login card
    const loginCard = document.querySelector('.login-card');
    loginCard.style.opacity = '0';
    loginCard.style.transform = 'translateY(20px)';
    loginCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    
    setTimeout(() => {
        loginCard.style.opacity = '1';
        loginCard.style.transform = 'translateY(0)';
    }, 100);
    
    // Add hover effect to create account button
    const createAccountBtn = document.querySelector('.create-account-btn');
    if (createAccountBtn) {
        createAccountBtn.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(-2px)';
        });
        
        createAccountBtn.addEventListener('mouseleave', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(0)';
        });
    }
    
    // Clean up interval when page unloads
    window.addEventListener('beforeunload', function() {
        if (window.loginMessageInterval) {
            clearInterval(window.loginMessageInterval);
        }
    });
});