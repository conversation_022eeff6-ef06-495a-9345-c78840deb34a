$(document).ready(function() {
    // Form validation
    $("#adminRegisterForm").submit(function(event) {
        let isValid = true;

        // Username validation
        const username = $("#username").val();
        const usernamePattern = /^[a-zA-Z0-9_]+$/;
        if (!usernamePattern.test(username)) {
            $("#username").addClass("is-invalid");
            isValid = false;
        } else {
            $("#username").removeClass("is-invalid");
        }

        // Password validation
        const password = $("#password").val();
        if (password.length < 6) {
            $("#password").addClass("is-invalid");
            isValid = false;
        } else {
            $("#password").removeClass("is-invalid");
        }

        // Confirm password validation
        const confirmPassword = $("#confirm_password").val();
        if (password !== confirmPassword) {
            $("#confirm_password").addClass("is-invalid");
            isValid = false;
        } else {
            $("#confirm_password").removeClass("is-invalid");
        }

        // Email validation
        const email = $("#email").val();
        if (email && !isValidEmail(email)) {
            $("#email").addClass("is-invalid");
            isValid = false;
        } else {
            $("#email").removeClass("is-invalid");
        }

        if (!isValid) {
            event.preventDefault();
        }
    });

    // Email validation function
    function isValidEmail(email) {
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailPattern.test(email);
    }

    // Show/hide annotation options based on role
    $("#role").change(function() {
        const role = $(this).val();
        if (role === "annotator") {
            $(".annotator-options").show();
        } else if (role === "auditor" || role === "admin") {
            $(".annotator-options").hide();
        }
    });

    // Initial check for role
    $("#role").trigger("change");

    // Bootstrap form validation
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms)
            .forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    const password = form.querySelector('#password');
                    const confirmPassword = form.querySelector('#confirm_password');
                    if (password && confirmPassword && password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity("Passwords do not match.");
                    } else if (confirmPassword) {
                        confirmPassword.setCustomValidity("");
                    }

                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }

                    form.classList.add('was-validated')
                }, false)
            })
    })();

    // Real-time password validation
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    if(passwordInput && confirmPasswordInput) {
        const validateConfirm = () => {
            if (passwordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.setCustomValidity("Passwords do not match.");
            } else {
                confirmPasswordInput.setCustomValidity("");
            }
        };
        passwordInput.addEventListener('change', validateConfirm);
        confirmPasswordInput.addEventListener('keyup', validateConfirm);
    }
});