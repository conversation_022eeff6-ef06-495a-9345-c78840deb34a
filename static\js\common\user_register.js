document.addEventListener('DOMContentLoaded', function() {
    // Hide navbar and footer completely
    const navbars = document.querySelectorAll('.navbar');
    const footers = document.querySelectorAll('footer');
    
    navbars.forEach(navbar => {
        navbar.style.display = 'none';
    });
    
    footers.forEach(footer => {
        footer.style.display = 'none';
    });
    
    const form = document.getElementById('registerForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const username = document.getElementById('username');
    const email = document.getElementById('email');
    
    // Add animation to the card
    const registrationCard = document.querySelector('.auth-card');
    const bottomIndicator = document.querySelector('.card-indicator.bottom');
    
    registrationCard.style.opacity = '0';
    registrationCard.style.transform = 'translateY(20px)';
    registrationCard.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    
    // Initially hide bottom indicator
    if (bottomIndicator) {
        bottomIndicator.style.opacity = '0';
    }
    
    setTimeout(() => {
        registrationCard.style.opacity = '1';
        registrationCard.style.transform = 'translateY(0)';
        
        // Show bottom indicator with delay
        setTimeout(() => {
            if (bottomIndicator) {
                bottomIndicator.style.opacity = '1';
                bottomIndicator.style.transition = 'opacity 0.5s ease';
            }
        }, 500);
    }, 100);
    
    // Add focus effects
    const inputs = form.querySelectorAll('.form-control, .form-select');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            input.parentElement.style.transform = 'translateY(-2px)';
        });
        
        input.addEventListener('blur', () => {
            input.parentElement.style.transform = 'none';
        });
    });
    
    // Validate form on submit
    form.addEventListener('submit', function(event) {
        let isValid = true;
        
        // Validate username
        if (!username.value.match(/^[a-zA-Z0-9_]+$/)) {
            username.classList.add('is-invalid');
            isValid = false;
        } else {
            username.classList.remove('is-invalid');
        }
        
        // Validate password length
        if (password.value.length < 6) {
            password.classList.add('is-invalid');
            isValid = false;
        } else {
            password.classList.remove('is-invalid');
        }
        
        // Validate password match
        if (password.value !== confirmPassword.value) {
            confirmPassword.classList.add('is-invalid');
            isValid = false;
        } else {
            confirmPassword.classList.remove('is-invalid');
        }
        
        // Validate email if provided
        if (email.value && !email.checkValidity()) {
            email.classList.add('is-invalid');
            isValid = false;
        } else {
            email.classList.remove('is-invalid');
        }
        
        if (!isValid) {
            event.preventDefault();
        }
    });
    
    // Real-time password match validation
    confirmPassword.addEventListener('input', function() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.classList.add('is-invalid');
        } else {
            confirmPassword.classList.remove('is-invalid');
        }
    });
});

$(document).ready(function() {
    // Show/hide annotation options based on role
    $("#role").change(function() {
        const role = $(this).val();
        if (role === "annotator") {
            $(".annotator-options").show();
        } else if (role === "auditor" ) {
            $(".annotator-options").hide();
        }
    });
    // Initial check for role
    $("#role").trigger("change");
});