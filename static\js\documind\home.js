// Mobile menu functionality
function initializeMobileMenu() {
  document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
      this.classList.toggle('active');
      document.querySelector('.main-nav').classList.toggle('show');
      document.querySelector('.nav-links').classList.toggle('show');
  });
}

// Smooth scrolling functionality
function initializeSmoothScroll() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
          e.preventDefault();
          
          const targetId = this.getAttribute('href');
          if (targetId === '#') return;
          
          const targetElement = document.querySelector(targetId);
          if (targetElement) {
              window.scrollTo({
                  top: targetElement.offsetTop - 100,
                  behavior: 'smooth'
              });
              
              // Close mobile menu if open
              document.querySelector('.nav-links').classList.remove('show');
              document.querySelector('.main-nav').classList.remove('show');
              document.querySelector('.mobile-menu-toggle').classList.remove('active');
          }
      });
  });
}

// Header scroll effect
function initializeHeaderScroll() {
  window.addEventListener('scroll', function() {
      const header = document.querySelector('.main-header');
      if (window.scrollY > 50) {
          header.classList.add('scrolled');
      } else {
          header.classList.remove('scrolled');
      }
  });
}

// Background parallax effect
function initializeParallax() {
  window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      const shapes = document.querySelectorAll('.shape');
      
      shapes.forEach((shape, index) => {
          const speed = 0.05 + (index * 0.01);
          const yPos = scrollPosition * speed;
          shape.style.transform = `translateY(${yPos}px)`;
      });
  });
}

// Tagline typing animation
function initializeTaglineTyping() {
  const taglineTyping = document.querySelector('.tagline-typing');
  const taglineOptions = [
      'AI P<span class="smaller">ROCESSING</span>', 
      'H<span class="smaller">UMAN</span> S<span class="smaller">UPERVISION</span>'
  ];
  let taglineIndex = 0;
  let charIndex = 0;
  let isDeleting = false;
  let plainText = '';
  
  function typeTagline() {
      const currentHTML = taglineOptions[taglineIndex];
      
      // Extract plain text from HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = currentHTML;
      const fullText = tempDiv.textContent || tempDiv.innerText || '';
      
      if (!isDeleting) {
          // Typing
          if (charIndex == 0) {
              plainText = '';
          }
          
          if (charIndex < fullText.length) {
              plainText += fullText.charAt(charIndex);
              charIndex++;
              
              updateTaglineHTML(plainText, taglineIndex);
              setTimeout(typeTagline, 100);
          } else {
              isDeleting = true;
              setTimeout(typeTagline, 2000);
          }
      } else {
          // Deleting
          if (charIndex > 0) {
              charIndex--;
              plainText = fullText.substring(0, charIndex);
              
              updateTaglineHTML(plainText, taglineIndex);
              setTimeout(typeTagline, 50);
          } else {
              isDeleting = false;
              taglineIndex = (taglineIndex + 1) % taglineOptions.length;
              setTimeout(typeTagline, 500);
          }
      }
  }
  
  function updateTaglineHTML(plainText, index) {
      if (index === 0) {
          // "AI PROCESSING"
          if (plainText.length <= 2) {
              taglineTyping.innerHTML = plainText;
          } else {
              taglineTyping.innerHTML = 'AI <span class="smaller">' + 
                  plainText.substring(3) + '</span>';
          }
      } else {
          // "HUMAN SUPERVISION"
          if (plainText.length <= 1) {
              taglineTyping.innerHTML = plainText;
          } else if (plainText.length <= 5) {
              taglineTyping.innerHTML = 'H<span class="smaller">' + 
                  plainText.substring(1) + '</span>';
          } else if (plainText.length <= 6) {
              taglineTyping.innerHTML = 'H<span class="smaller">UMAN</span> ';
          } else {
              taglineTyping.innerHTML = 'H<span class="smaller">UMAN</span> S<span class="smaller">' + 
                  plainText.substring(7) + '</span>';
          }
      }
  }
  
  // Start the typing animation
  setTimeout(typeTagline, 500);
}

// Rotating messages animation
function initializeRotatingMessages() {
  const typingMessageElement = document.querySelector('.typing-message');
  const messages = [
      'Seamlessly fetch data from anywhere, store everywhere.',
      'Gather every source\'s data, store in any database.',
      'Acquire data from any source, serve any destination.',
      'Connect any source and power every database.'
  ];
  let currentMessageIndex = 0;
  
  function fadeOutAndIn() {
      typingMessageElement.style.opacity = 0;
      
      setTimeout(() => {
          currentMessageIndex = (currentMessageIndex + 1) % messages.length;
          typingMessageElement.textContent = messages[currentMessageIndex];
          typingMessageElement.style.opacity = 1;
          setTimeout(fadeOutAndIn, 5000);
      }, 100);
  }

  // Start the animation after initial display
  setTimeout(fadeOutAndIn, 4000);
}

// Initialize all functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeMobileMenu();
  initializeSmoothScroll();
  initializeHeaderScroll();
  initializeParallax();
  initializeTaglineTyping();
  initializeRotatingMessages();
}); 