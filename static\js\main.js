/**
 * Main application JavaScript
 */

$(document).ready(function() {
    // Initialize tooltips and popovers
    $('[data-bs-toggle="tooltip"]').tooltip();
    $('[data-bs-toggle="popover"]').popover();
    
    // Add a simple alert helper
    window.showAlert = function(message, type = 'info', container = '#alertContainer') {
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        $(container).html(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $(`#${alertId}`).fadeOut('slow', function() {
                $(this).remove();
            });
        }, 5000);
    };
    
    // Handle dropdown toggling for user menu
    $('.dropdown-toggle').dropdown();
    
    // Generic ajax form submission
    $('.ajax-form').submit(function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const url = $form.attr('action');
        const method = $form.attr('method') || 'POST';
        const data = $form.serialize();
        
        $.ajax({
            url: url,
            type: method,
            data: data,
            success: function(response) {
                if (response.success) {
                    showAlert(response.message || 'Operation completed successfully', 'success');
                    
                    // Redirect if provided
                    if (response.redirect) {
                        setTimeout(() => {
                            window.location.href = response.redirect;
                        }, 1500);
                    }
                } else {
                    showAlert(response.message || 'Operation failed', 'danger');
                }
            },
            error: function() {
                showAlert('Server error occurred. Please try again.', 'danger');
            }
        });
    });
    
    // Dark mode toggle
    $('#darkModeToggle').change(function() {
        if ($(this).is(':checked')) {
            $('body').attr('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
        } else {
            $('body').attr('data-theme', 'light');
            localStorage.setItem('theme', 'light');
        }
    });
    
    // Check for saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        $('body').attr('data-theme', 'dark');
        $('#darkModeToggle').prop('checked', true);
    }
}); 