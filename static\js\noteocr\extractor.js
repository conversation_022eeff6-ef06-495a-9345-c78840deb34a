document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const dropZone = document.getElementById('dropZone');
    const uploadForm = document.getElementById('uploadForm');
    const imageUpload = document.getElementById('imageUpload');
    const previewImage = document.getElementById('previewImage');
    const uploadPreview = document.querySelector('.upload-preview');
    const browseBtn = document.getElementById('browseBtn');
    const fallbackBrowseBtn = document.getElementById('fallbackBrowseBtn');
    const previewSection = document.querySelector('.preview-section');
    const extractBtn = document.getElementById('extractBtn');
    const processingBtn = document.getElementById('processingBtn');
    const processingIndicator = document.getElementById('processingIndicator');
    const emptyState = document.getElementById('emptyState');
    const ocrResult = document.getElementById('ocrResult');
    const resultText = document.getElementById('resultText');
    const copyBtn = document.getElementById('copyBtn');
    const resultHeaderText = document.getElementById('resultHeaderText');
    
    // Mode selection elements
    const customPromptBtn = document.getElementById('customPromptBtn');
    const chatWithImageBtn = document.getElementById('chatWithImageBtn');
    const customPromptInput = document.getElementById('customPromptInput');
    const promptText = document.getElementById('promptText');
    const extractWithPromptBtn = document.getElementById('extractWithPromptBtn');
    
    // Editing controls
    const toggleEditBtn = document.getElementById('toggleEditBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const editControls = document.querySelector('.edit-controls');
    const cancelEditBtn = document.getElementById('cancelEditBtn');
    const saveEditBtn = document.getElementById('saveEditBtn');
    
    // Chat elements
    const chatSection = document.getElementById('chatSection');
    const toggleChatBtn = document.getElementById('toggleChatBtn');
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendChatBtn = document.getElementById('sendChatBtn');
    const chatProcessing = document.getElementById('chatProcessing');
    
    // Modal elements
    const modalImage = document.getElementById('modalImage');
    const modalText = document.getElementById('modalText');
    const modalCopyBtn = document.getElementById('modalCopyBtn');
    const modalDownloadBtn = document.getElementById('modalDownloadBtn');
    
    // Current state
    let currentImage = null;
    let uploadedImageFile = null;
    let originalText = '';
    let chatHistory = [];
    let currentMode = null; // 'extract' or 'chat'
    
    // Log to verify elements are found
    console.log('Browse button found:', browseBtn !== null);
    console.log('Image upload input found:', imageUpload !== null);
    console.log('Custom prompt button found:', customPromptBtn !== null);
    console.log('Chat with image button found:', chatWithImageBtn !== null);
    
    // Fallback browse button click event
    if (fallbackBrowseBtn) {
        console.log('Fallback browse button found');
        fallbackBrowseBtn.addEventListener('click', function(e) {
            console.log('Fallback browse button clicked');
            e.preventDefault();
            if (imageUpload) {
                // Trigger file input click
                imageUpload.click();
            } else {
                console.error('Image upload input not found for fallback button');
            }
        });
    } else {
        console.error('Fallback browse button not found');
    }
    
    // Mode Selection Button Event Handlers
    customPromptBtn.addEventListener('click', function() {
        // Set active state
        setActiveMode('extract');
        customPromptBtn.classList.add('active');
        chatWithImageBtn.classList.remove('active');
        
        // Show custom prompt input
        customPromptInput.classList.remove('d-none');
        extractBtn.classList.add('d-none');
        
        // Hide chat section if visible
        chatSection.classList.add('d-none');
        
        // Update result header
        resultHeaderText.textContent = 'OCR Results';
        
        // Focus on prompt text
        promptText.focus();
    });
    
    chatWithImageBtn.addEventListener('click', function() {
        // Set active state
        setActiveMode('chat');
        chatWithImageBtn.classList.add('active');
        customPromptBtn.classList.remove('active');
        
        // Hide custom prompt input
        customPromptInput.classList.add('d-none');
        extractBtn.classList.add('d-none');
        
        // Show chat section
        chatSection.classList.remove('d-none');
        resetChat();
        showChatSection();
        
        // Update result header
        resultHeaderText.textContent = 'Chat Results';
        
        // Hide OCR result if visible
        ocrResult.classList.add('d-none');
        emptyState.classList.add('d-none');
    });
    
    // Setup default prompt text
    promptText.value = "Extract all text from this image carefully. Include all visible text, numbers, labels, and maintain the structure and layout if possible. Format the result in a clean, readable way.";
    
    // Extract with custom prompt button
    extractWithPromptBtn.addEventListener('click', function() {
        if (currentImage) {
            const customPrompt = promptText.value.trim();
            if (customPrompt) {
                // Show processing indicators
                customPromptInput.classList.add('d-none');
                processingBtn.classList.remove('d-none');
                processingIndicator.classList.remove('d-none');
                emptyState.classList.add('d-none');
                ocrResult.classList.add('d-none');
                
                // Extract text with custom prompt
                extractTextWithCustomPrompt(currentImage, customPrompt);
            } else {
                showToast('Please enter a prompt for extraction', 'warning');
            }
        } else {
            showToast('Please upload an image first', 'warning');
        }
    });
    
    // Extract text with custom prompt
    function extractTextWithCustomPrompt(imageData, prompt) {
        fetch('/api/extractor-mode/extract-ocr-custom', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image_id: imageData.image_id,
                prompt: prompt
            })
        })
        .then(response => response.json())
        .then(data => {
            // Hide processing indicator
            processingIndicator.classList.add('d-none');
            processingBtn.classList.add('d-none');
            customPromptInput.classList.remove('d-none');
            
            if (data.success) {
                // Display the OCR result
                resultText.textContent = data.text;
                originalText = data.text; // Store original for edit cancellation
                emptyState.classList.add('d-none');
                ocrResult.classList.remove('d-none');
                
                // Enable edit and download buttons
                toggleEditBtn.disabled = false;
                downloadBtn.disabled = false;
                
                // Show success message
                showToast('Text extracted successfully with custom prompt!', 'success');
            } else {
                // Show error and reset
                showToast('Extraction failed: ' + data.error, 'error');
                resetResultDisplay();
            }
        })
        .catch(error => {
            // Hide processing indicator
            processingIndicator.classList.add('d-none');
            processingBtn.classList.add('d-none');
            customPromptInput.classList.remove('d-none');
            
            // Show error and reset
            showToast('Extraction failed: ' + error.message, 'error');
            resetResultDisplay();
        });
    }
    
    // Set active mode helper
    function setActiveMode(mode) {
        currentMode = mode;
        // Reset previous state
        ocrResult.classList.add('d-none');
        emptyState.classList.remove('d-none');
    }
    
    // Send chat message
    sendChatBtn.addEventListener('click', function() {
        sendChatMessage();
    });
    
    // Send chat message on Enter key
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            sendChatMessage();
        }
    });
    
    // Drag and drop functionality
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('drag-over');
    });
    
    dropZone.addEventListener('dragleave', function() {
        dropZone.classList.remove('drag-over');
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('drag-over');
        
        if (e.dataTransfer.files.length) {
            imageUpload.files = e.dataTransfer.files;
            handleImageSelection(e.dataTransfer.files[0]);
        }
    });
    
    // Click on dropzone or browse button to trigger file input
    dropZone.addEventListener('click', function(e) {
        // Checking if the click is on the browse button itself
        if (e.target === browseBtn || browseBtn.contains(e.target)) {
            return; // Let the browse button handler handle it
        }
        
        // Otherwise, trigger file input
        imageUpload.click();
    });
    
    // Browse button click event - single event handler
    browseBtn.addEventListener('click', function(e) {
        e.preventDefault(); // Prevent default button behavior
        e.stopPropagation(); // Prevent event from bubbling to dropZone
        console.log('Browse button clicked');
        
        if (imageUpload) {
            imageUpload.click();
        } else {
            console.error('Image upload input not found');
        }
    });
    
    // File input change event
    imageUpload.addEventListener('change', function() {
        console.log('File input changed, files:', this.files);
        if (this.files.length) {
            handleImageSelection(this.files[0]);
        }
    });
    
    // Upload form submit event
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        if (uploadedImageFile) {
            uploadImage(uploadedImageFile);
        }
    });
    
    // Extract button click event (original)
    extractBtn.addEventListener('click', function() {
        if (currentImage) {
            // Show processing button and hide extract button
            extractBtn.classList.add('d-none');
            processingBtn.classList.remove('d-none');
            
            extractText(currentImage);
        }
    });
    
    // Toggle edit button click event
    toggleEditBtn.addEventListener('click', function() {
        if (resultText.getAttribute('contenteditable') === 'true') {
            // Currently in edit mode, exit it
            exitEditMode();
        } else {
            // Enter edit mode
            enterEditMode();
        }
    });
    
    // Cancel edit button click event
    cancelEditBtn.addEventListener('click', function() {
        cancelEdit();
    });
    
    // Save edit button click event
    saveEditBtn.addEventListener('click', function() {
        saveEdit();
    });
    
    // Download button click event
    downloadBtn.addEventListener('click', function() {
        if (resultText.textContent) {
            downloadText(resultText.textContent, generateFilename());
        }
    });
    
    // Copy button click event
    copyBtn.addEventListener('click', function() {
        copyTextToClipboard(resultText.textContent);
    });
    
    // Modal copy button click event
    // modalCopyBtn.addEventListener('click', function() {
    //     copyTextToClipboard(modalText.textContent);
    // });
    
    // // Modal download button click event
    // modalDownloadBtn.addEventListener('click', function() {
    //     downloadText(modalText.textContent, generateFilename());
    // });
    
    // Chat functionality
    // Show chat section when OCR extraction is successful
    function showChatSection() {
        chatSection.classList.remove('d-none');
    }
    
    // Toggle chat section visibility
    toggleChatBtn.addEventListener('click', function() {
        const chatContainer = chatSection.querySelector('.chat-container');
        if (chatContainer.style.display === 'none') {
            chatContainer.style.display = 'block';
            toggleChatBtn.innerHTML = '<i class="bi bi-chevron-up"></i>';
        } else {
            chatContainer.style.display = 'none';
            toggleChatBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        }
    });
    
    // Send chat message
    function sendChatMessage() {
        const message = chatInput.value.trim();
        if (message && currentImage) {
            // Add user message to chat
            addChatMessage(message, 'user');
            
            // Clear input
            chatInput.value = '';
            
            // Show processing indicator
            chatProcessing.classList.remove('d-none');
            
            // Send message to server
            processChatMessage(message);
        }
    }
    
    // Add message to chat
    function addChatMessage(message, sender) {
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${sender}-message`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // Handle multi-line messages and code blocks
        if (sender === 'bot' && message.includes('```')) {
            // Handle code blocks in the response
            const parts = message.split(/```(?:\w*\n)?/);
            for (let i = 0; i < parts.length; i++) {
                if (i % 2 === 0) {
                    // Regular text
                    if (parts[i].trim()) {
                        const textPara = document.createElement('p');
                        textPara.innerHTML = parts[i].replace(/\n/g, '<br>');
                        messageContent.appendChild(textPara);
                    }
                } else {
                    // Code block
                    const codeBlock = document.createElement('pre');
                    const code = document.createElement('code');
                    code.textContent = parts[i].trim();
                    codeBlock.appendChild(code);
                    messageContent.appendChild(codeBlock);
                }
            }
        } else {
            // Regular text message
            const messageText = document.createElement('p');
            messageText.innerHTML = message.replace(/\n/g, '<br>');
            messageContent.appendChild(messageText);
        }
        
        messageElement.appendChild(messageContent);
        chatMessages.appendChild(messageElement);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // Process chat message with server
    function processChatMessage(message) {
        // Show a more detailed processing message
        addSystemMessage("Analyzing the image to answer your question...");
        
        // Create a simpler chat history structure that only includes the most recent messages
        // This helps avoid potential issues with complex history objects
        const simplifiedHistory = chatHistory.length > 2 ? 
            chatHistory.slice(chatHistory.length - 2) : 
            [...chatHistory];
            
        fetch('/api/extractor-mode/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image_id: currentImage.image_id,
                query: message,
                chat_history: simplifiedHistory
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Hide processing indicator
            chatProcessing.classList.add('d-none');
            
            if (data.success) {
                // Get the response
                const response = data.response;
                
                // Add response to chat
                addChatMessage(response, 'bot');
                
                // Update chat history
                chatHistory.push({
                    role: 'user',
                    content: message
                });
                chatHistory.push({
                    role: 'assistant',
                    content: response
                });
            } else {
                // Show error and retry without history
                console.error("Chat error:", data.error);
                if (chatHistory.length > 0 && !data.error.includes("tried without history")) {
                    // Try again without history (only if we haven't already tried)
                    addSystemMessage("Let me try analyzing the image differently...");
                    retryWithoutHistory(message);
                } else {
                    // Show error
                    addChatMessage(`I'm sorry, I'm having trouble analyzing this image. Please try a more specific question about the visible content.`, 'bot');
                }
            }
        })
        .catch(error => {
            // Hide processing indicator
            chatProcessing.classList.add('d-none');
            console.error("Chat error:", error);
            
            // Show error
            addChatMessage(`Sorry, I encountered a technical issue. Please try again in a moment.`, 'bot');
            
            // Try without history as fallback
            if (chatHistory.length > 0) {
                setTimeout(() => {
                    retryWithoutHistory(message);
                }, 1000);
            }
        });
    }
    
    // Add a system message to the chat (gray info message)
    function addSystemMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message system-message';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const messageText = document.createElement('p');
        messageText.textContent = message;
        
        messageContent.appendChild(messageText);
        messageElement.appendChild(messageContent);
        
        chatMessages.appendChild(messageElement);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    // Retry without chat history
    function retryWithoutHistory(message) {
        fetch('/api/extractor-mode/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image_id: currentImage.image_id,
                query: message,
                chat_history: null
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add response to chat
                addChatMessage(data.response, 'bot');
                
                // Reset chat history to just this interaction
                chatHistory = [
                    { role: 'user', content: message },
                    { role: 'assistant', content: data.response }
                ];
            } else {
                // Show error
                addChatMessage(`I'm having trouble analyzing this image. Let's try a different question or upload a clearer image.`, 'bot');
            }
        })
        .catch(error => {
            console.error("Retry failed:", error);
            // Final error message
            addChatMessage(`I'm sorry, but I'm having trouble processing your request right now. Please try again later.`, 'bot');
        });
    }
    
    // Handle image selection
    function handleImageSelection(file) {
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file.');
            return;
        }
        
        uploadedImageFile = file;
        const reader = new FileReader();
        
        reader.onload = function(e) {
            // Update preview section with the image
            previewSection.innerHTML = '';
            const previewImg = document.createElement('img');
            previewImg.src = e.target.result;
            previewImg.style.maxWidth = '100%';
            previewImg.style.maxHeight = '200px';
            previewSection.appendChild(previewImg);
            
            // Reset OCR state
            toggleEditBtn.disabled = true;
            downloadBtn.disabled = true;
            resetResultDisplay();
            
            // Reset chat
            resetChat();
            
            // Hide all result sections
            ocrResult.classList.add('d-none');
            chatSection.classList.add('d-none');
            
            // Enable mode buttons
            customPromptBtn.disabled = false;
            chatWithImageBtn.disabled = false;
            
            // Clear active states
            customPromptBtn.classList.remove('active');
            chatWithImageBtn.classList.remove('active');
            
            // Upload the image but don't process automatically
            uploadImage(file);
        };
        
        reader.readAsDataURL(file);
    }
    
    // Upload image to server
    function uploadImage(file) {
        const formData = new FormData();
        formData.append('image', file);
        
        // Show loading state briefly
        customPromptBtn.disabled = true;
        chatWithImageBtn.disabled = true;
        
        fetch('/api/extractor-mode/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Store the image data returned from server
                currentImage = data;
                
                // Enable the mode buttons
                customPromptBtn.disabled = false;
                chatWithImageBtn.disabled = false;
                
                // Show success message
                showToast('Image uploaded successfully! Choose a mode to continue.', 'success');
            } else {
                showToast('Upload failed: ' + data.error, 'error');
            }
        })
        .catch(error => {
            // Enable buttons
            customPromptBtn.disabled = false;
            chatWithImageBtn.disabled = false;
            
            showToast('Upload failed: ' + error.message, 'error');
        });
    }
    
    // Extract text from image
    function extractText(imageData) {
        // Show processing state
        processingIndicator.classList.remove('d-none');
        emptyState.classList.add('d-none');
        ocrResult.classList.add('d-none');
        
        fetch('/api/extractor-mode/extract-ocr', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                image_id: imageData.image_id
            })
        })
        .then(response => response.json())
        .then(data => {
            // Hide processing indicator
            processingIndicator.classList.add('d-none');
            
            // Restore extract button and hide processing button
            extractBtn.classList.remove('d-none');
            processingBtn.classList.add('d-none');
            
            if (data.success) {
                // Display the OCR result
                resultText.textContent = data.text;
                originalText = data.text; // Store original for edit cancellation
                emptyState.classList.add('d-none');
                ocrResult.classList.remove('d-none');
                
                // Enable edit and download buttons
                toggleEditBtn.disabled = false;
                downloadBtn.disabled = false;
                
                // Show chat section
                showChatSection();
                
                // Show success message
                showToast('Text extracted successfully!', 'success');
            } else {
                // Show error and reset
                showToast('Extraction failed: ' + data.error, 'error');
                resetResultDisplay();
            }
        })
        .catch(error => {
            // Hide processing indicator
            processingIndicator.classList.add('d-none');
            
            // Restore extract button and hide processing button
            extractBtn.classList.remove('d-none');
            processingBtn.classList.add('d-none');
            
            // Show error and reset
            showToast('Extraction failed: ' + error.message, 'error');
            resetResultDisplay();
        });
    }
    
    // Enter edit mode
    function enterEditMode() {
        // Save original text for cancellation
        originalText = resultText.textContent;
        
        // Make result text editable
        resultText.setAttribute('contenteditable', 'true');
        resultText.focus();
        
        // Update button text
        toggleEditBtn.innerHTML = '<i class="bi bi-x-circle"></i> Exit Edit';
        
        // Show edit controls
        editControls.classList.remove('d-none');
        
        // Set cursor at the end of the text
        placeCaretAtEnd(resultText);
    }
    
    // Exit edit mode
    function exitEditMode() {
        // Make result text non-editable
        resultText.setAttribute('contenteditable', 'false');
        
        // Update button text
        toggleEditBtn.innerHTML = '<i class="bi bi-pencil"></i> Edit';
        
        // Hide edit controls
        editControls.classList.add('d-none');
    }
    
    // Cancel edit
    function cancelEdit() {
        // Restore original text
        resultText.textContent = originalText;
        
        // Exit edit mode
        exitEditMode();
    }
    
    // Save edit
    function saveEdit() {
        // Exit edit mode
        exitEditMode();
        
        // Show success message
        showToast('Edits saved successfully!', 'success');
    }
    
    // Helper: Place caret at the end of a contenteditable element
    function placeCaretAtEnd(el) {
        el.focus();
        if (typeof window.getSelection !== "undefined" && typeof document.createRange !== "undefined") {
            const range = document.createRange();
            range.selectNodeContents(el);
            range.collapse(false);
            const sel = window.getSelection();
            sel.removeAllRanges();
            sel.addRange(range);
        }
    }
    
    // Generate filename for downloads
    function generateFilename() {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const baseFilename = uploadedImageFile ? uploadedImageFile.name.split('.')[0] : 'ocr-text';
        return `${baseFilename}-${timestamp}.txt`;
    }
    
    // Reset result display
    function resetResultDisplay() {
        emptyState.classList.remove('d-none');
        ocrResult.classList.add('d-none');
        processingIndicator.classList.add('d-none');
        exitEditMode();
    }
    
    // Helper: Copy text to clipboard
    function copyTextToClipboard(text) {
        navigator.clipboard.writeText(text)
            .then(() => showToast('Text copied to clipboard!', 'success'))
            .catch(err => showToast('Failed to copy text: ' + err, 'error'));
    }
    
    // Helper: Download text as file
    function downloadText(text, filename) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
        
        showToast('Text file downloaded!', 'success');
    }
    
    // Helper: Show toast notification
    function showToast(message, type = 'info') {
        // Check if toast container exists
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${type === 'error' ? 'bg-danger text-white' : type === 'success' ? 'bg-success text-white' : ''}">
                    <strong class="me-auto">${type === 'error' ? 'Error' : type === 'success' ? 'Success' : 'Info'}</strong>
                    <button type="button" class="btn-close ${type === 'error' || type === 'success' ? 'btn-close-white' : ''}" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        
        toast.show();
        
        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }
    
    // Reset chat
    function resetChat() {
        chatHistory = [];
        chatMessages.innerHTML = '';
        
        // Add system message
        const systemMessage = document.createElement('div');
        systemMessage.className = 'chat-message system-message';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const messageText = document.createElement('p');
        messageText.innerHTML = 'You can ask questions about what you see in this image.<br>For example: "What text appears in this image?" or "Describe what you see in this image."';
        
        messageContent.appendChild(messageText);
        systemMessage.appendChild(messageContent);
        
        chatMessages.appendChild(systemMessage);
    }
}); 