// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    document.querySelector('.mobile-menu-toggle').addEventListener('click', function() {
        this.classList.toggle('active');
        document.querySelector('.main-nav').classList.toggle('show');
        document.querySelector('.nav-links').classList.toggle('show');
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                document.querySelector('.nav-links').classList.remove('show');
                document.querySelector('.main-nav').classList.remove('show');
                document.querySelector('.mobile-menu-toggle').classList.remove('active');
            }
        });
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.main-header');
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });

    // Background parallax effect on scroll
    window.addEventListener('scroll', function() {
        const scrollPosition = window.scrollY;
        const shapes = document.querySelectorAll('.shape');
        
        shapes.forEach((shape, index) => {
            const speed = 0.05 + (index * 0.01);
            const yPos = scrollPosition * speed;
            shape.style.transform = `translateY(${yPos}px)`;
        });
    });

    // Tagline typing animation
    const taglineTyping = document.querySelector('.tagline-typing');
    const taglineOptions = [
        'A<span class="smaller">DVANCED</span> OCR', 
        'D<span class="smaller">OCUMENT</span> A<span class="smaller">NALYSIS</span>'
    ];
    let taglineIndex = 0;
    let charIndex = 0;
    let isDeleting = false;
    let plainText = '';
    
    function typeTagline() {
        const currentHTML = taglineOptions[taglineIndex];
        
        // Extract plain text from HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = currentHTML;
        const fullText = tempDiv.textContent || tempDiv.innerText || '';
        
        if (!isDeleting) {
            // Typing
            if (charIndex == 0) {
                plainText = '';
            }
            
            if (charIndex < fullText.length) {
                // Add one character
                plainText += fullText.charAt(charIndex);
                charIndex++;
                
                // Build HTML with the proper format
                if (taglineIndex === 0) {
                    // "ADVANCED OCR"
                    if (plainText.length <= 1) {
                        // Just "A"
                        taglineTyping.innerHTML = plainText;
                    } else {
                        // "A" + "DVANCED" with styling
                        taglineTyping.innerHTML = 'A<span class="smaller">' + 
                            plainText.substring(1) + '</span>';
                    }
                } else {
                    // "DOCUMENT ANALYSIS"
                    if (plainText.length <= 1) {
                        // Just "D"
                        taglineTyping.innerHTML = plainText;
                    } else if (plainText.length <= 8) {
                        // "DOCUMENT" with styling
                        taglineTyping.innerHTML = 'D<span class="smaller">' + 
                            plainText.substring(1) + '</span>';
                    } else if (plainText.length <= 9) {
                        // "DOCUMENT " (with space)
                        taglineTyping.innerHTML = 'D<span class="smaller">OCUMENT</span> ';
                    } else {
                        // "DOCUMENT ANALYSIS" with styling
                        taglineTyping.innerHTML = 'D<span class="smaller">OCUMENT</span> A<span class="smaller">' + 
                            plainText.substring(10) + '</span>';
                    }
                }
                
                setTimeout(typeTagline, 100);
            } else {
                // Done typing, pause before deleting
                isDeleting = true;
                setTimeout(typeTagline, 2000);
            }
        } else {
            // Deleting
            if (charIndex > 0) {
                // Remove one character
                charIndex--;
                plainText = fullText.substring(0, charIndex);
                
                // Build HTML with proper format (same logic as typing)
                if (taglineIndex === 0) {
                    // "ADVANCED OCR"
                    if (plainText.length <= 1) {
                        // Just "A"
                        taglineTyping.innerHTML = plainText;
                    } else {
                        // "A" + "DVANCED" with styling
                        taglineTyping.innerHTML = 'A<span class="smaller">' + 
                            plainText.substring(1) + '</span>';
                    }
                } else {
                    // "DOCUMENT ANALYSIS"
                    if (plainText.length <= 1) {
                        // Just "D"
                        taglineTyping.innerHTML = plainText;
                    } else if (plainText.length <= 8) {
                        // "DOCUMENT" with styling
                        taglineTyping.innerHTML = 'D<span class="smaller">' + 
                            plainText.substring(1) + '</span>';
                    } else if (plainText.length <= 9) {
                        // "DOCUMENT " (with space)
                        taglineTyping.innerHTML = 'D<span class="smaller">OCUMENT</span> ';
                    } else {
                        // "DOCUMENT ANALYSIS" with styling
                        taglineTyping.innerHTML = 'D<span class="smaller">OCUMENT</span> A<span class="smaller">' + 
                            plainText.substring(10) + '</span>';
                    }
                }
                
                setTimeout(typeTagline, 50);
            } else {
                // Done deleting, move to next word
                isDeleting = false;
                taglineIndex = (taglineIndex + 1) % taglineOptions.length;
                setTimeout(typeTagline, 500);
            }
        }
    }
    
    // Start the typing animation
    setTimeout(typeTagline, 500);

    // Rotating messages in typing container
    const typingMessageElement = document.querySelector('.typing-message');
    const messages = [
        'Extract structured content from complex PDF documents.',
        'Process tables, images, text, and formulas from PDFs.',
        'Transform unstructured PDFs into organized data files.',
        'Intelligent document analysis with precision extraction.'
    ];
    let currentMessageIndex = 0;
    
    function fadeOutAndIn() {
        // Fade out - will take 1 second due to CSS transition
        typingMessageElement.style.opacity = 0;
        
        // Wait for fade out to complete (1s), then change text
        setTimeout(() => {
            // Update to next message
            currentMessageIndex = (currentMessageIndex + 1) % messages.length;
            typingMessageElement.textContent = messages[currentMessageIndex];
            
            // Fade in - will take 1 second due to CSS transition
            typingMessageElement.style.opacity = 1;
            
            // Wait 4 seconds before starting the next transition
            setTimeout(fadeOutAndIn, 5000);
        }, 100);
    }

    // Start the animation after initial display
    setTimeout(fadeOutAndIn, 4000);
});
