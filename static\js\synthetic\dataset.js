document.addEventListener('DOMContentLoaded', function() {
    // DOM elements
    const selectedSourcesList = document.getElementById('selected-sources');
    const sourceList = document.getElementById('source-list');
    const sourcesPanel = document.getElementById('sources-panel');
    const noSourcesMessage = document.getElementById('no-sources-message');
    const sourceCountEl = document.getElementById('source-count');
    const selectedSourceCountEl = document.getElementById('selected-source-count');
    const selectAllSourcesBtn = document.getElementById('select-all-sources');
    const deselectAllSourcesBtn = document.getElementById('deselect-all-sources');
    const generateBtn = document.getElementById('generate-dataset');
    const previewPanel = document.getElementById('preview-panel');
    const qaPreview = document.getElementById('qa-preview');
    const qaCountDisplay = document.getElementById('qa-count-display');
    const modelOptions = document.querySelectorAll('.model-option');
    const apiKeyContainer = document.getElementById('api-key-container');
    const apiKeyInput = document.getElementById('api-key');
    const submitApiKeyButton = document.getElementById('submit-api-key');
    const queryInput = document.getElementById('query-input');
    const addQueryBtn = document.getElementById('add-query');
    const customQueriesList = document.getElementById('custom-queries');
    const includeAutoQueries = document.getElementById('include-auto-queries');
    const useDefaultKeyRadio = document.getElementById('use-default-key');
    const useCustomKeyRadio = document.getElementById('use-custom-key');
    const customKeyInput = document.getElementById('custom-key-input');
    const apiKeyNotification = document.getElementById('api-key-notification');

    // Dataset type selection elements
    const datasetTypeOptions = document.getElementById('dataset-type-options');
    const changeDatasetLink = document.getElementById('change-dataset-link');
    const selectedDatasetName = document.getElementById('selected-dataset-name');
    const selectedDatasetIcon = document.getElementById('selected-dataset-icon');
    const datasetTypeRadios = document.querySelectorAll('input[name="dataset-type"]');

    // Initialize dataset type selection functionality
    initializeDatasetTypeSelection();

    // Function to initialize dataset type selection behavior
    function initializeDatasetTypeSelection() {
        // Keep the options grid visible and selected display hidden
        if (datasetTypeOptions) {
            datasetTypeOptions.style.display = 'grid';
        }

        if (document.getElementById('selected-dataset-display')) {
            document.getElementById('selected-dataset-display').style.display = 'none';
        }

        // Add click event to the "Change" link - keeping this in case it's needed elsewhere
        if (changeDatasetLink) {
            changeDatasetLink.addEventListener('click', function(e) {
                e.preventDefault();
                datasetTypeOptions.style.display = 'grid';
                this.parentElement.parentElement.style.display = 'none';
            });
        }

        // Add click events to the radio buttons - modified to keep options visible
        datasetTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    updateSelectedDatasetDisplay();
                    // Keep dataset type options visible
                    datasetTypeOptions.style.display = 'grid';
                    // Keep selected dataset display hidden
                    document.getElementById('selected-dataset-display').style.display = 'none';
                }
            });
        });
    }

    // Function to update the selected dataset display
    function updateSelectedDatasetDisplay() {
        let selectedRadio = document.querySelector('input[name="dataset-type"]:checked');
        if (!selectedRadio) return;

        // Get the dataset type label
        const label = document.querySelector(`label[for="${selectedRadio.id}"]`);
        if (label) {
            selectedDatasetName.textContent = label.textContent.trim();
        }

        // Update icon based on selected dataset type
        updateSelectedDatasetIcon(selectedRadio.id);

        // Update configuration panel content based on dataset type
        updateConfigurationPanel(selectedRadio.value);
    }

    // Function to update the selected dataset icon
    function updateSelectedDatasetIcon(datasetTypeId) {
        // Set the icon based on the dataset type ID
        const iconMasks = {
            'dataset-type-qa': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M7.7,7.7L7,8.42V13.4L10.23,16.64L11,15.93L8.5,13.43V8.63L7.7,7.7M16.25,15.92L15.55,16.62L11.46,12.53L12.17,11.83L16.25,15.92M8.43,17.2L7.7,16.47L13.47,10.7L14.2,11.42L8.43,17.2M10.7,5.83L11.42,6.56L7.63,10.35L6.9,9.62L10.7,5.83M15.42,11.77L14.7,11.04L16.09,9.65L16.81,10.38L15.42,11.77Z\'/%3E%3C/svg%3E")',
            'dataset-type-entity-extraction': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M18.5,4L19.66,8.35L18.7,8.61C18.25,7.74 17.79,6.87 17.28,6.07C15.73,3.82 14.17,2.5 12,2.5C9.83,2.5 8.26,3.82 6.72,6.07C5.18,8.32 4.5,10.53 4.5,12C4.5,13.47 5.18,15.68 6.72,17.93C8.26,20.18 9.83,21.5 12,21.5C14.17,21.5 15.73,20.18 17.28,17.93C17.79,17.13 18.25,16.26 18.7,15.39L19.66,15.65L18.5,20H19.5L21.65,11.5H19.5L18.7,15.39C18.25,16.26 17.79,17.13 17.28,17.93C15.73,20.18 14.17,21.5 12,21.5C9.83,21.5 8.26,20.18 6.72,17.93C5.18,15.68 4.5,13.47 4.5,12C4.5,10.53 5.18,8.32 6.72,6.07C8.26,3.82 9.83,2.5 12,2.5C14.17,2.5 15.73,3.82 17.28,6.07C17.79,6.87 18.25,7.74 18.7,8.61L19.66,8.35L18.5,4\'/%3E%3C/svg%3E")',
            'dataset-type-concept-definitions': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z\'/%3E%3C/svg%3E")',
            'dataset-type-summarization': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M14,17H7V15H14M17,13H7V11H17M17,9H7V7H17M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z\'/%3E%3C/svg%3E")',
            'dataset-type-procedures': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,13V15H7V13H5M3,13H9V19H3V13Z\'/%3E%3C/svg%3E")',
            'dataset-type-comparisons': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M9,17H7V10H9V17M13,17H11V7H13V17M17,17H15V13H17V17M19,19H5V5H19V19.1M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3Z\'/%3E%3C/svg%3E")',
            'dataset-type-role-relationships': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M16,13C15.71,13 15.38,13 15.03,13.05C16.19,13.89 17,15 17,16.5V19H23V16.5C23,14.17 18.33,13 16,13M8,13C5.67,13 1,14.17 1,16.5V19H15V16.5C15,14.17 10.33,13 8,13M8,11A3,3 0 0,0 11,8A3,3 0 0,0 8,5A3,3 0 0,0 5,8A3,3 0 0,0 8,11M16,11A3,3 0 0,0 19,8A3,3 0 0,0 16,5A3,3 0 0,0 13,8A3,3 0 0,0 16,11Z\'/%3E%3C/svg%3E")',
            'dataset-type-code-explanations': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M8,3A2,2 0 0,0 6,5V9A2,2 0 0,1 4,11H3V13H4A2,2 0 0,1 6,15V19A2,2 0 0,0 8,21H10V19H8V14A2,2 0 0,0 6,12A2,2 0 0,0 8,10V5H10V3M16,3A2,2 0 0,1 18,5V9A2,2 0 0,0 20,11H21V13H20A2,2 0 0,0 18,15V19A2,2 0 0,1 16,21H14V19H16V14A2,2 0 0,1 18,12A2,2 0 0,1 16,10V5H14V3H16Z\'/%3E%3C/svg%3E")',
            'dataset-type-fact-opinion': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M12,2L6.5,11H17.5L12,2M12,5.84L13.93,9H10.06L12,5.84M17.5,13C15.6,13 14,14.6 14,16.5C14,18.4 15.6,20 17.5,20C19.4,20 21,18.4 21,16.5C21,14.6 19.4,13 17.5,13M17.5,18.5C16.5,18.5 15.5,17.8 15.5,16.5H19.5C19.5,17.8 18.5,18.5 17.5,18.5M3,13.5H8V15H3V13.5Z\'/%3E%3C/svg%3E")',
            'dataset-type-cause-effect': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z\'/%3E%3C/svg%3E")',
            'dataset-type-paraphrases': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M21.17 11.28C21.17 15.3 17.93 18.55 13.91 18.55C10,18.55 7.06 15.43 6.72 11.58L7.28 12.14L8.69 10.73L5.85 7.89L3,10.73L4.41 12.14L4.97 11.58C5.5 16.15 9.25 19.85 13.91 19.85C18.66 19.85 22.55 15.96 22.55 11.28H21.17M16.12 6.11C16.58 5.64 16.58 4.87 16.12 4.41C15.66 3.95 14.89 3.95 14.43 4.41L13.97 4.87L14.5 5.35L15,5.84L15.56 6.41L16.12 6.11M5.41 14.89L5.8 15.42L6.56 14.67L7.11 14.11L7.67 13.56L7.13 13L6.7 12.58L6.28 12.17C6.28 12.17 5.5 13 5.41 15.43V14.89M14.91 12.53L11.47 9.09L6.41 14.15L9.85 17.59L14.91 12.53Z\'/%3E%3C/svg%3E")',
            'dataset-type-intent-detection': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.1 14.8,9.5V11C15.4,11 16,11.6 16,12.3V15.8C16,16.4 15.4,17 14.7,17H9.2C8.6,17 8,16.4 8,15.7V12.2C8,11.6 8.6,11 9.2,11V9.5C9.2,8.1 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,9.5V11H13.5V9.5C13.5,8.7 12.8,8.2 12,8.2Z\'/%3E%3C/svg%3E")',
            'dataset-type-topic-classification': 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\'%3E%3Cpath d=\'M20,17A2,2 0 0,0 22,15V4A2,2 0 0,0 20,2H9.46C9.81,2.61 10,3.3 10,4H20V15H11V17M15,7V9H9V22H7V16H5V22H3V14H1.5V9A2,2 0 0,1 3.5,7H15M8,4A2,2 0 0,1 6,6A2,2 0 0,1 4,4A2,2 0 0,1 6,2A2,2 0 0,1 8,4Z\'/%3E%3C/svg%3E")'
        };

        if (selectedDatasetIcon && iconMasks[datasetTypeId]) {
            selectedDatasetIcon.style.maskImage = iconMasks[datasetTypeId];
            selectedDatasetIcon.style.webkitMaskImage = iconMasks[datasetTypeId];
        }
    }

    // Function to update the configuration panel based on selected dataset type
    function updateConfigurationPanel(datasetType) {
        const configHeading = document.querySelector('.dataset-panel h2 i.fas.fa-cog').parentNode.nextElementSibling;
        const countLabel = document.querySelector('.option-group:first-child .form-label');
        const countDescription = document.querySelector('.option-group:first-child .small-text');
        const difficultyDescription = document.querySelector('.option-group:nth-child(2) .small-text');

        // Update configuration panel heading text
        if (configHeading) {
            const datasetTypeText = document.querySelector(`label[for="dataset-type-${datasetType}"]`).textContent.trim();
            configHeading.textContent = `Customize how your ${datasetTypeText} dataset will be generated`;
        }

        // Update count label and description based on dataset type
        if (countLabel && countDescription) {
            switch (datasetType) {
                case 'qa':
                    countLabel.textContent = 'Number of QA Pairs';
                    countDescription.textContent = 'How many question-answer pairs to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of generated questions';
                    }
                    break;
                case 'entity-extraction':
                    countLabel.textContent = 'Number of Entities';
                    countDescription.textContent = 'How many entities to extract per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of entity extraction';
                    }
                    break;
                case 'concept-definitions':
                    countLabel.textContent = 'Number of Concepts';
                    countDescription.textContent = 'How many concept definitions to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines detail level of concept definitions';
                    }
                    break;
                case 'summarization':
                    countLabel.textContent = 'Number of Summaries';
                    countDescription.textContent = 'How many summaries to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines depth of generated summaries';
                    }
                    break;
                case 'procedures':
                    countLabel.textContent = 'Number of Procedures';
                    countDescription.textContent = 'How many procedures to extract per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of extracted procedures';
                    }
                    break;
                case 'comparisons':
                    countLabel.textContent = 'Number of Comparisons';
                    countDescription.textContent = 'How many comparison pairs to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines depth of comparative analysis';
                    }
                    break;
                case 'role-relationships':
                    countLabel.textContent = 'Number of Relationships';
                    countDescription.textContent = 'How many role relationships to identify per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of relationship analysis';
                    }
                    break;
                case 'code-explanations':
                    countLabel.textContent = 'Number of Code Examples';
                    countDescription.textContent = 'How many code examples to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines detail level of code examples';
                    }
                    break;
                case 'fact-opinion':
                    countLabel.textContent = 'Number of Statements';
                    countDescription.textContent = 'How many fact vs opinion statements to analyze per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of fact-opinion classification';
                    }
                    break;
                case 'cause-effect':
                    countLabel.textContent = 'Number of Relationships';
                    countDescription.textContent = 'How many cause-effect relationships to identify per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of causal analysis';
                    }
                    break;
                case 'paraphrases':
                    countLabel.textContent = 'Number of Paraphrases';
                    countDescription.textContent = 'How many paraphrases to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines variation level in generated paraphrases';
                    }
                    break;
                case 'intent-detection':
                    countLabel.textContent = 'Number of Intents';
                    countDescription.textContent = 'How many user intents to detect per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of intent detection';
                    }
                    break;
                case 'topic-classification':
                    countLabel.textContent = 'Number of Topics';
                    countDescription.textContent = 'How many topics to classify per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines granularity of topic classification';
                    }
                    break;
                default:
                    countLabel.textContent = 'Number of Items';
                    countDescription.textContent = 'How many items to generate per source';
                    if (difficultyDescription) {
                        difficultyDescription.textContent = 'Determines complexity of generated items';
                    }
            }
        }
    }

    // Update configuration panel on page load
    const initialDatasetType = document.querySelector('input[name="dataset-type"]:checked');
    if (initialDatasetType) {
        updateConfigurationPanel(initialDatasetType.value);
    }

    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const dependencyType = urlParams.get('dependency');

    // If coming from landing page with dependency parameter
    if (dependencyType) {
        // Store dependency type in sessionStorage for later use
        sessionStorage.setItem('dependencyType', dependencyType);

        // Add a dependency indicator in the UI
        const datasetHeader = document.querySelector('.dataset-header');
        if (datasetHeader) {
            const dependencyIndicator = document.createElement('div');
            dependencyIndicator.className = 'dependency-type-indicator';
            dependencyIndicator.innerHTML = `
                <span class="dependency-badge ${dependencyType === 'full' ? 'full-dependency' : 'reference-dependency'}">
                    <i class="fas ${dependencyType === 'full' ? 'fa-sitemap' : 'fa-project-diagram'}"></i>
                    ${dependencyType === 'full' ? 'Full Dependency' : 'Referential Dependency'}
                </span>
            `;
            datasetHeader.appendChild(dependencyIndicator);
        }

        console.log(`Dataset will be generated with ${dependencyType} dependency type`);
    }

    // Variables
    let sources = [];
    let selectedModel = null;
    let customQueries = [];
    let modelApiKeys = {}; // Store API keys for different models
    let currentApiKeySubmitted = false; // Track if current API key has been submitted
    let useDefaultApiKey = true; // Default to using default API key

    // Helper function to show errors that auto-dismiss
    function showError(message, element) {
        // Clear any existing error messages first
        if (element) {
            const existingErrors = element.querySelectorAll('.error-message');
            existingErrors.forEach(err => err.remove());
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <p>${message}</p>
        `;

        // If an element is provided, append to that element
        if (element) {
            element.innerHTML = '';
            element.appendChild(errorDiv);
        }

        // Auto-hide after 7 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 7000);

        return errorDiv;
    }

    // Custom alert function with auto-dismiss
    function showAlert(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'custom-alert';
        alertDiv.innerHTML = `
            <div class="alert-content">
                <i class="fas fa-exclamation-circle"></i>
                <p>${message}</p>
            </div>
        `;

        document.body.appendChild(alertDiv);

        // Auto-hide after 7 seconds
        setTimeout(() => {
            alertDiv.classList.add('fade-out');
            setTimeout(() => {
                alertDiv.remove();
            }, 300); // Fade out transition
        }, 7000);
    }

    // Load existing sources when page loads
    loadSources();

    // Function to load and display keywords from selected sources
    function loadKeywords() {
        const extractedKeywordsEl = document.getElementById('extracted-keywords');
        const noKeywordsMessage = document.getElementById('no-keywords-message');

        // Get only selected source IDs
        const selectedSourceIds = getSelectedSourceIds();

        if (selectedSourceIds.length === 0) {
            // No sources selected, show message
            extractedKeywordsEl.innerHTML = '';
            noKeywordsMessage.style.display = 'block';
            return;
        }

        // Show loading indicator
        extractedKeywordsEl.innerHTML = `
            <div class="loading-keywords">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Extracting keywords from ${selectedSourceIds.length} selected sources...</span>
            </div>
        `;
        noKeywordsMessage.style.display = 'none';

        // Call the API to extract keywords
        fetch('/api/extract_keywords', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ source_ids: selectedSourceIds })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to extract keywords');
            }
            return response.json();
        })
        .then(data => {
            if (data.keywords && data.keywords.length > 0) {
                displayKeywords(data.keywords);
            } else {
                // No keywords found
                extractedKeywordsEl.innerHTML = '';
                noKeywordsMessage.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error extracting keywords:', error);
            extractedKeywordsEl.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Error extracting keywords. Please try again.</span>
                </div>
            `;
        });
    }

    // Function to display extracted keywords and make them clickable
    function displayKeywords(keywords) {
        const extractedKeywordsEl = document.getElementById('extracted-keywords');
        const noKeywordsMessage = document.getElementById('no-keywords-message');

        // Clear previous content
        extractedKeywordsEl.innerHTML = '';

        // Create keyword tags
        keywords.forEach(keyword => {
            if (keyword && keyword.trim()) {
                const keywordEl = document.createElement('div');
                keywordEl.className = 'keyword-tag';
                keywordEl.textContent = keyword;

                // Make keyword clickable to add as a custom query
                keywordEl.addEventListener('click', function() {
                    addCustomQuery(keyword);

                    // Visual feedback that keyword was selected
                    this.classList.add('keyword-selected');
                    setTimeout(() => {
                        this.classList.remove('keyword-selected');
                    }, 1000);
                });

                extractedKeywordsEl.appendChild(keywordEl);
            }
        });

        // Show no keywords message if none were displayed
        if (extractedKeywordsEl.children.length === 0) {
            noKeywordsMessage.style.display = 'block';
        } else {
            noKeywordsMessage.style.display = 'none';
        }
    }

    // Model selection
    modelOptions.forEach(option => {
        option.addEventListener('click', function() {
            const newModelId = this.dataset.model;
            const needsKey = this.dataset.needsKey === 'true';

            // If switching to a different model
            if (selectedModel && selectedModel.id !== newModelId) {
                // Reset submission state for new model
                currentApiKeySubmitted = false;
            }

            // Remove selected class from all options
            modelOptions.forEach(opt => opt.classList.remove('selected'));

            // Add selected class to clicked option
            this.classList.add('selected');

            // Store selected model
            selectedModel = {
                id: newModelId,
                name: this.querySelector('.model-name').textContent,
                needsKey: needsKey
            };

            // Show/hide API key input
            if (selectedModel.needsKey) {
                // Check if default API key is available
                checkDefaultApiKey(selectedModel.id);

                apiKeyContainer.classList.remove('hidden');

                // Reset to default option
                useDefaultKeyRadio.checked = true;
                useCustomKeyRadio.checked = false;
                customKeyInput.classList.add('hidden');
                useDefaultApiKey = true;

                // If we have a saved key for this model and it's for custom entry
                if (modelApiKeys[selectedModel.id] && !currentApiKeySubmitted) {
                    apiKeyInput.value = modelApiKeys[selectedModel.id];
                } else if (!currentApiKeySubmitted) {
                    // Clear the input when switching to a new model that needs a key
                    apiKeyInput.value = '';
                }
            } else {
                apiKeyContainer.classList.add('hidden');
                // Hide any notifications
                hideApiKeyNotification();
            }

            console.log('Selected model:', selectedModel);

            // Update generate button state
            updateGenerateButtonState();
        });
    });

    // API key input change
    apiKeyInput.addEventListener('input', updateGenerateButtonState);

    // Submit API Key button
    submitApiKeyButton.addEventListener('click', function() {
        if (useDefaultApiKey) {
            // Just mark as submitted when using default key
            currentApiKeySubmitted = true;
            apiKeyContainer.classList.add('hidden');
            updateGenerateButtonState();
        } else {
            const apiKey = apiKeyInput.value.trim();
            if (apiKey) {
                // Store the API key for this model
                modelApiKeys[selectedModel.id] = apiKey;
                currentApiKeySubmitted = true;

                // Hide the API key container
                apiKeyContainer.classList.add('hidden');
                // Update generate button state
                updateGenerateButtonState();
            } else {
                showAlert('Please enter an API key');
            }
        }
    });

    // Custom query handling
    addQueryBtn.addEventListener('click', () => {
        const query = queryInput.value.trim();
        if (query) {
            addCustomQuery(query);
            queryInput.value = '';
        }
    });

    // Add custom query function
    function addCustomQuery(query) {
        const queryId = Date.now().toString();
        customQueries.push({ id: queryId, text: query });

        const queryItem = document.createElement('div');
        queryItem.className = 'custom-query-item';
        queryItem.dataset.id = queryId;
        queryItem.innerHTML = `
            <div class="query-text">${query}</div>
            <button class="query-remove" data-id="${queryId}">
                <i class="fas fa-times"></i>
            </button>
        `;

        customQueriesList.appendChild(queryItem);

        // Add event listener for remove button
        queryItem.querySelector('.query-remove').addEventListener('click', (e) => {
            const id = e.currentTarget.dataset.id;
            removeCustomQuery(id);
        });
    }

    // Remove custom query
    function removeCustomQuery(id) {
        customQueries = customQueries.filter(q => q.id !== id);

        const queryItem = document.querySelector(`.custom-query-item[data-id="${id}"]`);
        if (queryItem) {
            queryItem.remove();
        }
    }

    // Function to load sources from the server with better error handling
    function loadSources() {
        fetch('/get-sources')
            .then(response => response.json())
            .then(data => {
                console.log('Loaded sources:', data.sources);
                if (data.sources && data.sources.length > 0) {
                    // We have sources from the previous upload
                    sources = data.sources;
                    displaySources();
                    updateGenerateButtonState();

                    // Load keywords from the selected sources
                    loadKeywords();
                } else {
                    // No sources found
                    selectedSourcesList.innerHTML = '';
                    sourceList.style.display = 'none';
                    noSourcesMessage.style.display = 'block';
                    generateBtn.disabled = true;

                    // Clear keywords when no sources
                    const extractedKeywordsEl = document.getElementById('extracted-keywords');
                    const noKeywordsMessage = document.getElementById('no-keywords-message');
                    extractedKeywordsEl.innerHTML = '';
                    noKeywordsMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error loading sources:', error);
                selectedSourcesList.innerHTML = '';
                const errorElement = showError('Failed to load sources. Please try again.', selectedSourcesList);
                errorElement.classList.add('text-center', 'p-3');
                generateBtn.disabled = true;
            });
    }

    // Display loaded sources
    function displaySources() {
        // Update count
        sourceCountEl.textContent = sources.length;

        // Update list
        selectedSourcesList.innerHTML = '';

        // Create items for each source
        sources.forEach(source => {
            const sourceItem = document.createElement('div');
            sourceItem.className = 'source-item';
            sourceItem.innerHTML = `
                <div class="source-checkbox-container">
                    <input type="checkbox" class="source-checkbox" id="source-${source.id}" data-source-id="${source.id}" checked>
                    <label class="checkbox-label" for="source-${source.id}"></label>
                </div>
                <div class="source-icon">
                    <i class="${getSourceIcon(source.type)}"></i>
                </div>
                <div class="source-details">
                    <div class="source-name">${source.name}</div>
                    <div class="source-meta">${source.wordCount || '0'} words</div>
                </div>
            `;

            selectedSourcesList.appendChild(sourceItem);

            // Add event listener to the checkbox
            const checkbox = sourceItem.querySelector('.source-checkbox');
            if (checkbox) {
                checkbox.addEventListener('change', function() {
                    if (this.checked) {
                        sourceItem.classList.add('selected');
                    } else {
                        sourceItem.classList.remove('selected');
                    }
                    updateSelectedSourceCount();

                    // Refresh keywords when source selection changes
                    loadKeywords();
                    updateGenerateButtonState();
                });

                // Set initial selected class
                if (checkbox.checked) {
                    sourceItem.classList.add('selected');
                }
            }
        });

        // Update selected count initially
        updateSelectedSourceCount();
    }

    // Get icon based on source type
    function getSourceIcon(type) {
        switch(type) {
            case 'file':
                return 'fas fa-file-alt';
            case 'link':
                return 'fas fa-link';
            case 'text':
                return 'fas fa-align-left';
            default:
                return 'fas fa-file';
        }
    }

    // Function to update generate button state
    function updateGenerateButtonState() {
        const hasSelectedSources = getSelectedSourceIds().length > 0;
        const hasModel = selectedModel !== null;
        const hasApiKey = !selectedModel?.needsKey ||
                         useDefaultApiKey ||
                         (apiKeyInput.value.trim().length > 0 && !useDefaultApiKey);

        generateBtn.disabled = !(hasSelectedSources && hasModel && hasApiKey);
    }

    // Helper function to get the correct endpoint based on dataset type
    function getEndpointForDatasetType(datasetType) {
        // Map dataset types to their corresponding endpoint names
        const endpointMap = {
            'qa': 'qa-dataset',
            'entity-extraction': 'entity-extraction',
            'concept-definitions': 'concept-definitions',
            'summarization': 'summarization',
            'procedures': 'procedures',
            'comparisons': 'comparisons',
            'role-relationships': 'role-relationships',
            'code-explanations': 'code-explanations',
            'fact-opinion': 'fact-opinion',
            'cause-effect': 'cause-effect',
            'paraphrases': 'paraphrases',
            'intent-detection': 'intent-detection',
            'topic-classification': 'topic-classification'
        };

        return endpointMap[datasetType] || datasetType;
    }

    // Generate dataset event handler
    generateBtn.addEventListener('click', async function() {
        try {
            // Show loading state
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

            // Get dataset type from selected radio button
            const datasetTypeEls = document.querySelectorAll('input[name="dataset-type"]');
            let datasetType = 'qa';
            datasetTypeEls.forEach(el => {
                if (el.checked) {
                    datasetType = el.value;
                }
            });

            // Get configuration values
            const qaCount = document.getElementById('qa-count').value;
            const difficulty = document.getElementById('difficulty').value;
            const format = getOutputFormat(); // Use the new function
            const customQueries = Array.from(customQueriesList.children).map(item => item.dataset.id);
            const includeAutoQueries = document.getElementById('include-auto-queries').checked;
            const dependencyType = sessionStorage.getItem('dependencyType') || 'full';

            // Validate inputs
            if (!selectedModel) {
                showAlert('Please select a model');
                return;
            }

            // Get selected sources rather than all sources
            const selectedSourceIds = getSelectedSourceIds();

            if (!selectedSourceIds || selectedSourceIds.length === 0) {
                showAlert('Please select at least one source');
                return;
            }

            // Get selected sources objects by their IDs
            const selectedSources = sources.filter(source => selectedSourceIds.includes(source.id));

            // Get API key if needed
            let apiKey = null;
            let useDefaultKey = false;

            if (selectedModel.needsKey) {
                if (useDefaultApiKey) {
                    // Use default API key
                    useDefaultKey = true;
                } else if (currentApiKeySubmitted && modelApiKeys[selectedModel.id]) {
                    // Use submitted key
                    apiKey = modelApiKeys[selectedModel.id];
                } else {
                    // Get key from input
                    apiKey = apiKeyInput.value.trim();

                    if (!apiKey) {
                        showAlert('Please enter an API key');
                        return;
                    }
                }
            }

            // Show loading indicator
            qaPreview.innerHTML = '';

            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                <span>Generating dataset from ${selectedSources.length} selected sources...</span>
            `;

            qaPreview.appendChild(loadingIndicator);
            previewPanel.style.display = 'block';

            // Scroll to preview
            previewPanel.scrollIntoView({ behavior: 'smooth' });

            // Prepare data - use sampleCount for non-QA datasets
            const requestData = {
                sources: selectedSources,
                model: selectedModel.id,
                api_key: apiKey,
                use_default_key: useDefaultKey,
                customQueries: customQueries,
                includeAutoQueries: includeAutoQueries,
                dependency_type: dependencyType
            };

            // Add specific parameters based on dataset type
            if (datasetType === 'qa') {
                requestData.qaCount = qaCount;
                requestData.difficulty = difficulty;
            } else {
                requestData.sampleCount = qaCount;
            }

            // Call the backend API with the correct endpoint
            const endpoint = getEndpointForDatasetType(datasetType);
            const response = await fetch(`/generate-${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            // Check if response is OK
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            // Get raw text first for debugging
            const rawText = await response.text();

            // Try to parse JSON, with error handling
            let data;
            try {
                data = JSON.parse(rawText);
            } catch (jsonError) {
                console.error('JSON Parse Error:', jsonError);
                console.error('Raw response:', rawText);
                // Only show the start of the response if it's very long
                const preview = rawText.length > 100 ?
                    `${rawText.substring(0, 100)}... (${rawText.length} chars total)` :
                    rawText;
                throw new Error(`Invalid JSON response: ${jsonError.message}. Response starts with: ${preview}`);
            }

            if (data.success) {
                // Display the generated dataset based on type
                switch (datasetType) {
                    case 'qa':
                        displayQAPairs(data.qa_pairs);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'entity-extraction':
                        displayEntityExtractionSamples(data.entity_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'concept-definitions':
                        displayConceptDefinitions(data.concept_definitions);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'summarization':
                        displaySummarizationSamples(data.summarization_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'comparisons':
                        displayComparisonSamples(data.comparison_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'fact-opinion':
                        displayFactOpinionSamples(data.fact_opinion_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'paraphrases':
                        displayParaphrasesSamples(data.paraphrases_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'intent-detection':
                        displayIntentDetectionSamples(data.intent_detection_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'topic-classification':
                        displayTopicClassificationSamples(data.topic_classification_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    default:
                        // Generic display for other types
                        displayGenericDataset(data, datasetType);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                }
            } else {
                // Show error with more details
                console.error('API Error:', data.error);
                qaPreview.innerHTML = '';
                const errorMsg = data.error || 'An error occurred while generating the dataset.';
                const errorElement = showError(errorMsg, qaPreview);
                // Add additional hint if needed
                const hint = document.createElement('p');
                hint.className = 'small-text';
                hint.textContent = 'If sources can\'t be found, try going back to the upload page and re-processing your sources.';
                errorElement.appendChild(hint);
            }
            // Remove the loading indicator instead of hiding it
            const loadingElement = document.querySelector('.loading-indicator');
            if (loadingElement) {
                loadingElement.remove();
            }

            // Scroll to preview
            previewPanel.scrollIntoView({ behavior: 'smooth' });
        } catch (error) {
            console.error('Error:', error);
            qaPreview.innerHTML = '';
            showError('Failed to connect to the server. Please try again later.', qaPreview);
            // Remove the loading indicator instead of hiding it
            const loadingElement = document.querySelector('.loading-indicator');
            if (loadingElement) {
                loadingElement.remove();
            }
        } finally {
            // Reset generate button
            generateBtn.disabled = false;
            generateBtn.innerHTML = 'Generate Dataset';
        }
    });

    // Display QA pairs in the preview
    function displayQAPairs(pairs) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = pairs.length;

        // Check if we have any fallback pairs
        const hasFallbackPairs = pairs.some(pair => pair.is_fallback);

        // If we have fallback pairs, show a warning
        if (hasFallbackPairs) {
            const warningDiv = document.createElement('div');
            warningDiv.className = 'fallback-warning';
            warningDiv.innerHTML = `
                <div class="warning-icon"><i class="fas fa-exclamation-triangle"></i></div>
                <div class="warning-message">
                    <h4>Some QA pairs were generated using fallback methods</h4>
                    <p>The model had difficulty generating high-quality QA pairs for some of the content.
                    Fallback pairs are marked with a yellow border and may be of lower quality.</p>
                </div>
            `;
            qaPreview.appendChild(warningDiv);
        }

        // Add each QA pair to the preview
        pairs.forEach((pair, index) => {
            const qaItem = document.createElement('div');
            qaItem.className = 'qa-pair';

            // Add fallback class if this is a fallback pair
            if (pair.is_fallback) {
                qaItem.classList.add('fallback-pair');
            }

            qaItem.innerHTML = `
                <div class="qa-question">Q${index+1}: ${pair.question}</div>
                <div class="qa-answer">A: ${pair.answer}</div>
                <div class="qa-source">Source: ${pair.source}</div>
            `;

            // Add fallback indicator if this is a fallback pair
            if (pair.is_fallback) {
                const fallbackDiv = document.createElement('div');
                fallbackDiv.className = 'fallback-indicator';
                fallbackDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> Fallback';
                qaItem.appendChild(fallbackDiv);
            }

            // Add thinking process button if available
            if (pair.thinking_process) {
                addThinkingProcessButton(qaItem, pair.thinking_process);
            }

            qaPreview.appendChild(qaItem);
        });
    }

    // Add this after the display functions
    function addThinkingProcessButton(parentElement, thinkingProcess) {
        if (!thinkingProcess) return;

        const button = document.createElement('button');
        button.className = 'thinking-process-btn';
        button.innerHTML = '<i class="fas fa-brain"></i> View Thinking Process';
        button.style.fontWeight = 'bold';
        button.style.backgroundColor = 'rgba(74, 107, 255, 0.2)';
        button.style.color = '#4a6bff';
        button.style.border = '1px solid rgba(74, 107, 255, 0.3)';
        button.style.padding = '10px 20px';
        button.style.borderRadius = '4px';
        button.style.cursor = 'pointer';
        button.style.display = 'flex';
        button.style.alignItems = 'center';
        button.style.justifyContent = 'center';
        button.style.gap = '8px';
        button.style.margin = '0 auto';
        button.style.transition = 'all 0.2s ease';

        const processDiv = document.createElement('div');
        processDiv.className = 'thinking-process hidden';
        processDiv.style.marginTop = '15px';
        processDiv.style.padding = '15px';
        processDiv.style.backgroundColor = 'rgba(74, 107, 255, 0.05)';
        processDiv.style.border = '1px solid rgba(74, 107, 255, 0.2)';
        processDiv.style.borderRadius = '4px';
        processDiv.innerHTML = `
            <div class="thinking-process-content">
                <h4 style="margin-top: 0; color: #4a6bff;">Model's Thinking Process</h4>
                <pre style="white-space: pre-wrap; font-family: monospace; background-color: rgba(255, 255, 255, 0.7); padding: 10px; border-radius: 4px; overflow: auto; max-height: 400px;">${thinkingProcess}</pre>
            </div>
        `;

        button.addEventListener('click', () => {
            processDiv.classList.toggle('hidden');
            button.innerHTML = processDiv.classList.contains('hidden') ?
                '<i class="fas fa-brain"></i> View Thinking Process' :
                '<i class="fas fa-brain"></i> Hide Thinking Process';
        });

        parentElement.appendChild(button);
        parentElement.appendChild(processDiv);
    }

    // Display Entity Extraction samples
    function displayEntityExtractionSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Create the preview content
        samples.forEach(function(sample, index) {
            // Log sample structure to help debug
            console.log("Sample structure:", JSON.stringify(sample, null, 2));

            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Create the text section
            const textElem = document.createElement('div');
            textElem.className = 'qa-question';

            // Check if sample has text or entity field for the main content
            const sampleText = sample.text || (sample.context || "No text available");
            textElem.innerHTML = `<h4>Text ${index + 1}</h4><p>${sampleText}</p>`;

            // Create the entities section
            const entitiesElem = document.createElement('div');
            entitiesElem.className = 'qa-answer';
            entitiesElem.innerHTML = '<h4>Entities</h4>';

            // Create a table for entities
            const entitiesTable = document.createElement('table');
            entitiesTable.className = 'entities-table';
            entitiesTable.innerHTML = '<thead><tr><th>Entity</th><th>Type</th></tr></thead>';
            const tableBody = document.createElement('tbody');

            // Check if we have entities array or if the sample itself is an entity
            if (sample.entities && Array.isArray(sample.entities)) {
                // Add each entity as a row
                sample.entities.forEach(function(entity) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${entity.entity}</td>
                        <td>${entity.type}</td>
                    `;
                    tableBody.appendChild(row);
                });
            } else if (sample.entity && sample.type) {
                // The sample itself is an entity
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${sample.entity}</td>
                    <td>${sample.type}</td>
                `;
                tableBody.appendChild(row);
            } else {
                // No entities found
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="2">No entities found</td>';
                tableBody.appendChild(row);
            }

            entitiesTable.appendChild(tableBody);
            entitiesElem.appendChild(entitiesTable);

            // Add source if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add everything to the pair element
            sampleElem.appendChild(textElem);
            sampleElem.appendChild(entitiesElem);

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            // Add to the preview container
            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Concept Definitions
    function displayConceptDefinitions(concepts) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = concepts.length;

        // Create the preview content
        concepts.forEach(function(concept, index) {
            const conceptElem = document.createElement('div');
            conceptElem.className = 'qa-pair';

            // 1. Concept Name - handle both 'term' and 'concept' field names
            const conceptNameElem = document.createElement('div');
            conceptNameElem.className = 'qa-question';
            const conceptName = concept.concept || concept.term; // Use either 'concept' or 'term'
            conceptNameElem.innerHTML = `<h4>Concept ${index + 1}</h4><p>${conceptName}</p>`;

            // 2. Definition
            const definitionElem = document.createElement('div');
            definitionElem.className = 'qa-answer';
            definitionElem.innerHTML = `<h4>Definition</h4><p>${concept.definition}</p>`;

            // 3. Related Concepts
            let relatedElem;
            if (concept.related_concepts && concept.related_concepts.length > 0) {
                relatedElem = document.createElement('div');
                relatedElem.className = 'qa-answer';
                relatedElem.innerHTML = '<h4>Related Concepts</h4>';

                const relatedList = document.createElement('ul');
                concept.related_concepts.forEach(related => {
                    const listItem = document.createElement('li');
                    listItem.textContent = related;
                    relatedList.appendChild(listItem);
                });
                relatedElem.appendChild(relatedList);
            }

            // 4. Examples
            let examplesElem;
            if (concept.examples && concept.examples.length > 0) {
                examplesElem = document.createElement('div');
                examplesElem.className = 'qa-answer';
                examplesElem.innerHTML = '<h4>Examples</h4>';

                const examplesList = document.createElement('ul');
                concept.examples.forEach(example => {
                    const listItem = document.createElement('li');
                    listItem.textContent = example;
                    examplesList.appendChild(listItem);
                });
                examplesElem.appendChild(examplesList);
            }

            // Source information (at the end)
            let sourceElem;
            if (concept.source) {
                sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${concept.source}`;
            }

            // Add all elements in the correct sequence
            conceptElem.appendChild(conceptNameElem);
            conceptElem.appendChild(definitionElem);
            if (relatedElem) conceptElem.appendChild(relatedElem);
            if (examplesElem) conceptElem.appendChild(examplesElem);
            if (sourceElem) conceptElem.appendChild(sourceElem);

            // Add thinking process button if available
            if (concept.thinking_process) {
                addThinkingProcessButton(conceptElem, concept.thinking_process);
            }

            // Add to the preview container
            qaPreview.appendChild(conceptElem);
        });
    }

    // Display Summarization samples
    function displaySummarizationSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Create the preview content
        samples.forEach(function(sample, index) {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Create the original text section
            const textElem = document.createElement('div');
            textElem.className = 'qa-question';
            textElem.innerHTML = `<h4>Original Text ${index + 1}</h4><p>${sample.original_text}</p>`;

            // Create the summary section
            const summaryElem = document.createElement('div');
            summaryElem.className = 'qa-answer';
            summaryElem.innerHTML = `<h4>Summary</h4><p>${sample.summary}</p>`;

            // Create key points section if available
            if (sample.key_points && sample.key_points.length > 0) {
                const keyPointsElem = document.createElement('div');
                keyPointsElem.className = 'qa-answer';
                keyPointsElem.innerHTML = '<h4>Key Points</h4>';

                const pointsList = document.createElement('ul');
                sample.key_points.forEach(point => {
                    const listItem = document.createElement('li');
                    listItem.textContent = point;
                    pointsList.appendChild(listItem);
                });

                keyPointsElem.appendChild(pointsList);
                sampleElem.appendChild(keyPointsElem);
            }

            // Add source if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add main components
            sampleElem.appendChild(textElem);
            sampleElem.appendChild(summaryElem);

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            // Add to the preview container
            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Paraphrases samples
    function displayParaphrasesSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each paraphrase sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Original text section (make visually distinct)
            const originalTextElem = document.createElement('div');
            originalTextElem.className = 'qa-question';
            originalTextElem.innerHTML = `<h4>Original Text ${index+1}</h4><p><strong>${sample.original_text}</strong></p>`;
            sampleElem.appendChild(originalTextElem);

            // Paraphrases section
            const paraphrasesElem = document.createElement('div');
            paraphrasesElem.className = 'qa-answer';
            paraphrasesElem.innerHTML = '<h4>Paraphrases</h4>';

            // Create a clean numbered list for paraphrases
            const paraphrasesList = document.createElement('ol');
            paraphrasesList.className = 'paraphrases-list';

            // Handle both string and list inputs
            const paraphrases = Array.isArray(sample.paraphrases) ? sample.paraphrases : [sample.paraphrases];

            paraphrases.forEach((paraphrase) => {
                const listItem = document.createElement('li');
                listItem.className = 'paraphrase-item';
                listItem.textContent = paraphrase;
                paraphrasesList.appendChild(listItem);
            });

            paraphrasesElem.appendChild(paraphrasesList);
            sampleElem.appendChild(paraphrasesElem);

            // Context if available
            if (sample.context && sample.context.trim()) {
                const contextElem = document.createElement('div');
                contextElem.className = 'qa-context';
                contextElem.innerHTML = `<h4>Context</h4><p>${sample.context}</p>`;
                sampleElem.appendChild(contextElem);
            }

            // Source information
            const sourceElem = document.createElement('div');
            sourceElem.className = 'qa-source';
            sourceElem.textContent = `Source: ${sample.source}`;
            sampleElem.appendChild(sourceElem);

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Intent Detection samples
    function displayIntentDetectionSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each intent detection sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Utterance section
            const utteranceElem = document.createElement('div');
            utteranceElem.className = 'qa-question';
            utteranceElem.innerHTML = `<h4>Utterance ${index+1}</h4><p>${sample.utterance || sample.text}</p>`;
            sampleElem.appendChild(utteranceElem);

            // Intent section
            const intentElem = document.createElement('div');
            intentElem.className = 'qa-answer';
            intentElem.innerHTML = `<h4>Intent</h4><p>${sample.intent}</p>`;
            sampleElem.appendChild(intentElem);

            // Confidence score if available
            if (sample.confidence !== undefined) {
                const confidenceElem = document.createElement('div');
                confidenceElem.className = 'qa-confidence';
                confidenceElem.innerHTML = `<h4>Confidence</h4><p>${sample.confidence}</p>`;
                sampleElem.appendChild(confidenceElem);
            }

            // Entities or slots if available
            if (sample.entities && Array.isArray(sample.entities) && sample.entities.length > 0) {
                const entitiesElem = document.createElement('div');
                entitiesElem.className = 'qa-entities';
                entitiesElem.innerHTML = '<h4>Entities/Slots</h4>';

                const entitiesList = document.createElement('ul');
                sample.entities.forEach(entity => {
                    const entityItem = document.createElement('li');
                    if (typeof entity === 'string') {
                        entityItem.textContent = entity;
                    } else {
                        entityItem.textContent = `${entity.name || entity.type}: ${entity.value}`;
                    }
                    entitiesList.appendChild(entityItem);
                });
                entitiesElem.appendChild(entitiesList);
                sampleElem.appendChild(entitiesElem);
            }

            // Source information
            const sourceElem = document.createElement('div');
            sourceElem.className = 'qa-source';
            sourceElem.textContent = `Source: ${sample.source}`;
            sampleElem.appendChild(sourceElem);

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Topic Classification samples
    function displayTopicClassificationSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each topic classification sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Text section
            const textElem = document.createElement('div');
            textElem.className = 'qa-question';
            textElem.innerHTML = `<h4>Text ${index+1}</h4><p>${sample.text}</p>`;
            sampleElem.appendChild(textElem);

            // Topic/Category section
            const topicElem = document.createElement('div');
            topicElem.className = 'qa-answer';
            topicElem.innerHTML = `<h4>Topic/Category</h4><p>${sample.topic}</p>`;
            sampleElem.appendChild(topicElem);

            // Source information if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Comparison samples
    function displayComparisonSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each comparison sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Determine the topic to display
            let topicText = '';
            if (sample.topic && sample.topic.trim()) {
                // If topic is directly provided (Gemini model)
                topicText = sample.topic;
            } else if (sample.item1 && sample.item2) {
                // If item1 and item2 are provided (Ollama model)
                topicText = `${sample.item1} vs ${sample.item2}`;
            } else {
                // Fallback
                topicText = `Comparison ${index+1}`;
            }

            // Topic section - use the actual topic as the main content
            const topicElem = document.createElement('div');
            topicElem.className = 'qa-question';
            topicElem.innerHTML = `<p>${topicText}</p>`;
            sampleElem.appendChild(topicElem);

            // Create the answer section that contains both similarities and differences
            const answerElem = document.createElement('div');
            answerElem.className = 'qa-answer';

            // Format the answer with clear sections for similarities and differences
            let answerContent = '<p>';

            // Add similarities section
            answerContent += '<strong>Similarities:</strong><br>';
            if (Array.isArray(sample.similarities) && sample.similarities.length > 0) {
                sample.similarities.forEach(similarity => {
                    answerContent += `- ${similarity}<br>`;
                });
            } else {
                answerContent += 'No similarities found<br>';
            }

            // Add differences section
            answerContent += '<br><strong>Differences:</strong><br>';
            if (Array.isArray(sample.differences) && sample.differences.length > 0) {
                sample.differences.forEach(difference => {
                    answerContent += `- ${difference}<br>`;
                });
            } else {
                answerContent += 'No differences found<br>';
            }

            answerContent += '</p>';
            answerElem.innerHTML = answerContent;
            sampleElem.appendChild(answerElem);

            // Source information if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Fact-Opinion samples
    function displayFactOpinionSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each fact-opinion sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Statement section
            const statementElem = document.createElement('div');
            statementElem.className = 'qa-question';
            statementElem.innerHTML = `<h4>Statement ${index+1}</h4><p>${sample.statement || ''}</p>`;
            sampleElem.appendChild(statementElem);

            // Classification and reasoning section
            const answerElem = document.createElement('div');
            answerElem.className = 'qa-answer';

            // Format the answer with clear sections for classification and reasoning
            let answerContent = '<p>';

            // Add classification section
            answerContent += '<strong>Classification:</strong> ';
            answerContent += sample.classification || 'Not specified';

            // Add reasoning/explanation section (use either one that's available)
            answerContent += '<br><br><strong>Reasoning:</strong><br>';
            answerContent += sample.reasoning || sample.explanation || 'No reasoning provided';

            answerContent += '</p>';
            answerElem.innerHTML = answerContent;
            sampleElem.appendChild(answerElem);

            // Source information if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display Role Relationship samples
    function displayRoleRelationshipSamples(samples) {
        qaPreview.innerHTML = '';

        // Update count display
        qaCountDisplay.textContent = samples.length;

        // Add each role relationship sample to the preview
        samples.forEach((sample, index) => {
            const sampleElem = document.createElement('div');
            sampleElem.className = 'qa-pair';

            // Context section
            const contextElem = document.createElement('div');
            contextElem.className = 'qa-question';
            contextElem.innerHTML = `<h4>Context ${index+1}</h4><p>${sample.context || ''}</p>`;
            sampleElem.appendChild(contextElem);

            // Roles and relationship section
            const relationshipElem = document.createElement('div');
            relationshipElem.className = 'qa-answer';
            relationshipElem.innerHTML = '<h4>Roles & Relationship</h4>';

            // Create structured content with data attributes for export
            const role1Elem = document.createElement('div');
            role1Elem.setAttribute('data-role', 'role1');
            role1Elem.innerHTML = `<strong>Role 1:</strong> ${sample.role1 || ''}`;
            relationshipElem.appendChild(role1Elem);

            const role2Elem = document.createElement('div');
            role2Elem.setAttribute('data-role', 'role2');
            role2Elem.innerHTML = `<strong>Role 2:</strong> ${sample.role2 || ''}`;
            relationshipElem.appendChild(role2Elem);

            const relationElem = document.createElement('div');
            relationElem.setAttribute('data-role', 'relationship');
            relationElem.innerHTML = `<strong>Relationship:</strong> ${sample.relationship || ''}`;
            relationshipElem.appendChild(relationElem);

            sampleElem.appendChild(relationshipElem);

            // Source information if available
            if (sample.source) {
                const sourceElem = document.createElement('div');
                sourceElem.className = 'qa-source';
                // Removed sourceId storage as requested
                sourceElem.textContent = `Source: ${sample.source}`;
                sampleElem.appendChild(sourceElem);
            }

            // Add thinking process button if available
            if (sample.thinking_process) {
                addThinkingProcessButton(sampleElem, sample.thinking_process);
            }

            qaPreview.appendChild(sampleElem);
        });
    }

    // Display generic dataset for any other types
    function displayGenericDataset(response, datasetType) {
        qaPreview.innerHTML = '';

        // Try to determine the response data key based on dataset type
        let dataKey = null;
        let displayTitle = '';

        switch(datasetType) {
            case 'procedures':
                dataKey = 'procedure_samples';
                displayTitle = 'Procedures';
                break;
            case 'comparisons':
                dataKey = 'comparison_samples';
                displayTitle = 'Comparisons';
                break;
            case 'role-relationships':
                dataKey = 'relationship_samples';
                displayTitle = 'Role Relationships';
                break;
            case 'code-explanations':
                // Check for both possible keys
                if (response.code_explanations) {
                    dataKey = 'code_explanations';
                } else if (response.code_explanation_samples) {
                    dataKey = 'code_explanation_samples';
                }
                displayTitle = 'Code Explanations';
                break;
            case 'fact-opinion':
                dataKey = 'fact_opinion_samples';
                displayTitle = 'Fact vs Opinion';
                break;
            case 'cause-effect':
                // Check for both possible keys
                if (response.cause_effects) {
                    dataKey = 'cause_effects';
                } else if (response.cause_effect_samples) {
                    dataKey = 'cause_effect_samples';
                } else {
                    // If neither key exists, try to find any array in the response
                    for (const key in response) {
                        if (Array.isArray(response[key]) && key !== 'success') {
                            dataKey = key;
                            break;
                        }
                    }
                }
                displayTitle = 'Cause-Effect Relationships';
                break;
            case 'paraphrases':
                dataKey = 'paraphrases_samples';
                displayTitle = 'Paraphrases';
                break;
            case 'intent-detection':
                dataKey = 'intent_detection_samples';
                displayTitle = 'Intent Detection';
                break;
            case 'topic-classification':
                dataKey = 'topic_classification_samples';
                displayTitle = 'Topic Classification';
                break;
            default:
                // Attempt to find a key in the response that looks like a dataset
                for (const key in response) {
                    if (Array.isArray(response[key]) && key !== 'success') {
                        dataKey = key;
                        displayTitle = key.replace(/_/g, ' ').replace(/samples/g, 'examples');
                        break;
                    }
                }
        }

        if (dataKey && response[dataKey]) {
            const samples = response[dataKey];
            qaCountDisplay.textContent = samples.length;

            // Check if we have a dedicated display function for this dataset type
            if (datasetType === 'paraphrases' && typeof displayParaphrasesSamples === 'function') {
                return displayParaphrasesSamples(samples);
            } else if (datasetType === 'intent-detection' && typeof displayIntentDetectionSamples === 'function') {
                return displayIntentDetectionSamples(samples);
            } else if (datasetType === 'topic-classification' && typeof displayTopicClassificationSamples === 'function') {
                return displayTopicClassificationSamples(samples);
            } else if (datasetType === 'role-relationships' && typeof displayRoleRelationshipSamples === 'function') {
                return displayRoleRelationshipSamples(samples);
            } else if (datasetType === 'comparisons' && typeof displayComparisonSamples === 'function') {
                return displayComparisonSamples(samples);
            } else if (datasetType === 'fact-opinion' && typeof displayFactOpinionSamples === 'function') {
                return displayFactOpinionSamples(samples);
            }

            // Display a message if the dataset is empty
            if (samples.length === 0) {
                const infoMessage = document.createElement('div');
                infoMessage.className = 'info-message';
                infoMessage.textContent = 'No examples generated.';
                qaPreview.appendChild(infoMessage);
                return;
            }

            // Display each sample
            samples.forEach(function(sample, index) {
                const sampleElem = document.createElement('div');
                sampleElem.className = 'qa-pair';

                // Add a title for this sample
                const titleElem = document.createElement('h4');
                titleElem.textContent = `${displayTitle} Sample ${index + 1}`;
                titleElem.className = 'sample-title';
                sampleElem.appendChild(titleElem);

                // Add a generic visualization based on the sample structure
                if (datasetType === 'cause-effect') {
                    // Create cause section
                    const causeElem = document.createElement('div');
                    causeElem.className = 'qa-question';
                    const causeTitle = document.createElement('h4');
                    causeTitle.textContent = 'Cause';
                    causeElem.appendChild(causeTitle);
                    const causeContent = document.createElement('p');
                    causeContent.textContent = sample.cause || '';
                    causeElem.appendChild(causeContent);
                    sampleElem.appendChild(causeElem);

                    // Create effect section
                    const effectElem = document.createElement('div');
                    effectElem.className = 'qa-answer';
                    const effectTitle = document.createElement('h4');
                    effectTitle.textContent = 'Effect';
                    effectElem.appendChild(effectTitle);
                    const effectContent = document.createElement('p');
                    effectContent.textContent = sample.effect || '';
                    effectElem.appendChild(effectContent);
                    sampleElem.appendChild(effectElem);

                    // Create explanation section if it exists
                    if (sample.explanation) {
                        const explanationElem = document.createElement('div');
                        explanationElem.className = 'explanation';
                        const explanationTitle = document.createElement('h4');
                        explanationTitle.textContent = 'Explanation';
                        explanationElem.appendChild(explanationTitle);
                        const explanationContent = document.createElement('p');
                        explanationContent.textContent = sample.explanation;
                        explanationElem.appendChild(explanationContent);
                        sampleElem.appendChild(explanationElem);
                    }

                    // Add language if available
                    if (sample.language) {
                        const languageElem = document.createElement('div');
                        languageElem.className = 'qa-language';
                        languageElem.textContent = `Language: ${sample.language}`;
                        sampleElem.appendChild(languageElem);
                    }

                    // Add source if available
                    if (sample.source) {
                        const sourceElem = document.createElement('div');
                        sourceElem.className = 'qa-source';
                        sourceElem.textContent = `Source: ${sample.source}`;
                        sampleElem.appendChild(sourceElem);
                    }
                } else if (datasetType === 'code-explanations') {
                    // Create topic section
                    const topicElem = document.createElement('div');
                    topicElem.className = 'qa-question';
                    const topicTitle = document.createElement('h4');
                    topicTitle.textContent = 'Topic';
                    topicElem.appendChild(topicTitle);
                    const topicContent = document.createElement('p');
                    topicContent.textContent = sample.topic || '';
                    topicElem.appendChild(topicContent);
                    sampleElem.appendChild(topicElem);

                    // Create code section with syntax highlighting and proper formatting
                    const codeElem = document.createElement('div');
                    codeElem.className = 'qa-answer code-block-container';
                    const codeTitle = document.createElement('h4');
                    codeTitle.textContent = 'Code';
                    codeElem.appendChild(codeTitle);

                    // Use <pre><code> for formatting and highlight.js for syntax highlighting
                    const codeContent = document.createElement('pre');
                    const codeBlock = document.createElement('code');
                    codeBlock.className = 'language-python'; // or detect language if possible

                    // Decode escaped characters (like \n, \t)
                    let codeString = sample.code || '';
                    try {
                        codeString = JSON.parse('"' + codeString.replace(/"/g, '\\"') + '"');
                    } catch (e) {
                        // fallback: replace \n with real newlines
                        codeString = codeString.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
                    }
                    codeBlock.textContent = codeString;

                    codeContent.appendChild(codeBlock);
                    codeElem.appendChild(codeContent);
                    sampleElem.appendChild(codeElem);

                    // Optionally, trigger syntax highlighting if using highlight.js
                    if (window.hljs) {
                        window.hljs.highlightElement(codeBlock);
                    }

                    // Add language if available
                    if (sample.language) {
                        const languageElem = document.createElement('div');
                        languageElem.className = 'qa-language';
                        languageElem.textContent = `Language: ${sample.language}`;
                        sampleElem.appendChild(languageElem);
                    }

                    // Add source if available
                    if (sample.source) {
                        const sourceElem = document.createElement('div');
                        sourceElem.className = 'qa-source';
                        sourceElem.textContent = `Source: ${sample.source}`;
                        sampleElem.appendChild(sourceElem);
                    }
                } else {
                    // Special handling for comparisons
                    if (datasetType === 'comparisons') {
                        // Determine the topic to display
                        let topicText = '';
                        if (sample.topic && sample.topic.trim()) {
                            // If topic is directly provided (Gemini model)
                            topicText = sample.topic;
                        } else if (sample.item1 && sample.item2) {
                            // If item1 and item2 are provided (Ollama model)
                            topicText = `${sample.item1} vs ${sample.item2}`;
                        } else {
                            // Fallback
                            topicText = 'Comparison';
                        }

                        // Topic section - use the actual topic as the main content
                        const topicElem = document.createElement('div');
                        topicElem.className = 'qa-question';
                        topicElem.innerHTML = `<p>${topicText}</p>`;
                        sampleElem.appendChild(topicElem);

                        // Create the answer section that contains both similarities and differences
                        const answerElem = document.createElement('div');
                        answerElem.className = 'qa-answer';

                        // Format the answer with clear sections for similarities and differences
                        let answerContent = '<p>';

                        // Add similarities section
                        answerContent += '<strong>Similarities:</strong><br>';
                        if (Array.isArray(sample.similarities) && sample.similarities.length > 0) {
                            sample.similarities.forEach(similarity => {
                                answerContent += `- ${similarity}<br>`;
                            });
                        } else {
                            answerContent += 'No similarities found<br>';
                        }

                        // Add differences section
                        answerContent += '<br><strong>Differences:</strong><br>';
                        if (Array.isArray(sample.differences) && sample.differences.length > 0) {
                            sample.differences.forEach(difference => {
                                answerContent += `- ${difference}<br>`;
                            });
                        } else {
                            answerContent += 'No differences found<br>';
                        }

                        answerContent += '</p>';
                        answerElem.innerHTML = answerContent;
                        sampleElem.appendChild(answerElem);
                    }
                    // Special handling for procedures
                    else if (datasetType === 'procedures') {
                        // Task section
                        const taskElem = document.createElement('div');
                        taskElem.className = 'qa-question';
                        const taskTitle = document.createElement('h4');
                        taskTitle.textContent = 'Task';
                        taskElem.appendChild(taskTitle);
                        const taskContent = document.createElement('p');
                        taskContent.textContent = sample.task || '';
                        taskElem.appendChild(taskContent);
                        sampleElem.appendChild(taskElem);

                        // Steps section
                        const stepsElem = document.createElement('div');
                        stepsElem.className = 'qa-answer';
                        const stepsTitle = document.createElement('h4');
                        stepsTitle.textContent = 'Steps';
                        stepsElem.appendChild(stepsTitle);

                        // Create list for steps
                        const stepsList = document.createElement('ul');
                        stepsList.className = 'procedure-steps';

                        if (Array.isArray(sample.steps) && sample.steps.length > 0) {
                            sample.steps.forEach((step, stepIndex) => {
                                const stepItem = document.createElement('li');
                                stepItem.textContent = step;
                                stepsList.appendChild(stepItem);
                            });
                        } else {
                            // Add default steps if none exist
                            const defaultSteps = [
                                "Step 1: Prepare necessary resources",
                                "Step 2: Follow standard procedures",
                                "Step 3: Complete the task"
                            ];
                            defaultSteps.forEach(step => {
                                const stepItem = document.createElement('li');
                                stepItem.textContent = step;
                                stepsList.appendChild(stepItem);
                            });
                        }

                        stepsElem.appendChild(stepsList);
                        sampleElem.appendChild(stepsElem);

                        // Requirements section
                        const reqElem = document.createElement('div');
                        reqElem.className = 'qa-answer';
                        const reqTitle = document.createElement('h4');
                        reqTitle.textContent = 'Requirements';
                        reqElem.appendChild(reqTitle);

                        // Create list for requirements
                        const reqList = document.createElement('ul');
                        reqList.className = 'procedure-requirements';

                        if (Array.isArray(sample.requirements) && sample.requirements.length > 0) {
                            sample.requirements.forEach((req, reqIndex) => {
                                const reqItem = document.createElement('li');
                                reqItem.textContent = req;
                                reqList.appendChild(reqItem);
                            });
                        } else {
                            // Add default requirements if none exist
                            const defaultReqs = [
                                "Required tools and software",
                                "Basic knowledge of the task",
                                "Access permissions"
                            ];
                            defaultReqs.forEach(req => {
                                const reqItem = document.createElement('li');
                                reqItem.textContent = req;
                                reqList.appendChild(reqItem);
                            });
                        }

                        reqElem.appendChild(reqList);
                        sampleElem.appendChild(reqElem);
                    } else {
                        // Generic display for other types
                        for (const key in sample) {
                            if (key !== 'source' && key !== 'sourceId' && key !== 'thinking_process') {
                                const contentElem = document.createElement('div');
                                contentElem.className = key === 'question' ? 'qa-question' : 'qa-answer';

                                const headerElem = document.createElement('h4');
                                headerElem.textContent = key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ');
                                contentElem.appendChild(headerElem);

                                // Handle different value types
                                if (typeof sample[key] === 'string') {
                                    const textElem = document.createElement('p');
                                    textElem.textContent = sample[key];
                                    contentElem.appendChild(textElem);
                                } else if (Array.isArray(sample[key])) {
                                    const list = document.createElement('ul');
                                    sample[key].forEach(item => {
                                        const listItem = document.createElement('li');
                                        if (typeof item === 'string') {
                                            listItem.textContent = item;
                                        } else {
                                            listItem.textContent = JSON.stringify(item);
                                        }
                                        list.appendChild(listItem);
                                    });
                                    contentElem.appendChild(list);
                                } else if (typeof sample[key] === 'object' && sample[key] !== null) {
                                    // For objects, create a simple key-value display
                                    const details = document.createElement('dl');
                                    for (const subKey in sample[key]) {
                                        const dt = document.createElement('dt');
                                        dt.textContent = subKey;
                                        details.appendChild(dt);

                                        const dd = document.createElement('dd');
                                        dd.textContent = sample[key][subKey];
                                        details.appendChild(dd);
                                    }
                                    contentElem.appendChild(details);
                                }

                                sampleElem.appendChild(contentElem);
                            }
                        }
                    }

                    // Add source if available
                    if (sample.source) {
                        const sourceElem = document.createElement('div');
                        sourceElem.className = 'qa-source';
                        sourceElem.textContent = `Source: ${sample.source}`;
                        sampleElem.appendChild(sourceElem);
                    }

                    // Add thinking process button if available
                    if (sample.thinking_process) {
                        addThinkingProcessButton(sampleElem, sample.thinking_process);
                    }
                }

                // Add to the preview container
                qaPreview.appendChild(sampleElem);
            });
        } else {
            // If we couldn't determine the data structure, show a raw JSON view
            const rawJsonElem = document.createElement('div');
            rawJsonElem.className = 'raw-json';

            const preElem = document.createElement('pre');
            preElem.textContent = JSON.stringify(response, null, 2);

            rawJsonElem.appendChild(preElem);
            qaPreview.appendChild(rawJsonElem);
        }
    }

    // Refresh preview button
    document.getElementById('refresh-preview').addEventListener('click', () => {
        // Get the currently selected dataset type
        const datasetTypeEls = document.querySelectorAll('input[name="dataset-type"]');
        let datasetType = 'qa';
        datasetTypeEls.forEach(el => {
            if (el.checked) {
                datasetType = el.value;
            }
        });

        // Get relevant configuration based on dataset type
        const count = parseInt(document.getElementById('qa-count').value, 10);
        const difficulty = document.getElementById('difficulty').value;

        // Clear previous content
        qaPreview.innerHTML = '';

        // Get selected sources rather than all sources
        const selectedSourceIds = getSelectedSourceIds();

        if (!selectedSourceIds || selectedSourceIds.length === 0) {
            showAlert('Please select at least one source');
            return;
        }

        // Get selected sources objects by their IDs
        const selectedSources = sources.filter(source => selectedSourceIds.includes(source.id));

        // Create a new loading indicator each time
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i>
            <span>Regenerating ${datasetType} dataset from ${selectedSources.length} selected sources...</span>
        `;
        loadingIndicator.style.display = 'flex';

        // Add it to the preview
        qaPreview.appendChild(loadingIndicator);

        // Prepare source data for API - only use selected sources
        const sourceData = selectedSources.map(source => {
            return {
                id: source.id,
                name: source.name,
                type: source.type,
                url: source.url
            };
        });

        // Prepare data based on selected dataset type
        const requestData = {
            sources: sourceData,
            model: selectedModel.id,
            api_key: selectedModel.needsKey ? apiKeyInput.value.trim() : null,
            use_default_key: useDefaultApiKey,
            customQueries: customQueries.map(q => q.text),
            includeAutoQueries: includeAutoQueries.checked
        };

        // Add specific parameters based on dataset type
        if (datasetType === 'qa') {
            requestData.qaCount = count;
            requestData.difficulty = difficulty;
        } else {
            requestData.sampleCount = count;
        }

        // Call the API for the selected dataset type with the correct endpoint
        const endpoint = getEndpointForDatasetType(datasetType);
        fetch(`/generate-${endpoint}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }
            return response.text();
        })
        .then(rawText => {
            // Try to parse JSON, with error handling
            let data;
            try {
                data = JSON.parse(rawText);
            } catch (jsonError) {
                console.error('JSON Parse Error:', jsonError);
                console.error('Raw response:', rawText);
                // Only show the start of the response if it's very long
                const preview = rawText.length > 100 ?
                    `${rawText.substring(0, 100)}... (${rawText.length} chars total)` :
                    rawText;
                throw new Error(`Invalid JSON response: ${jsonError.message}. Response starts with: ${preview}`);
            }
            return data;
        })
        .then(data => {
            if (data.success) {
                // Display based on dataset type
                switch (datasetType) {
                    case 'qa':
                        displayQAPairs(data.qa_pairs);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'entity-extraction':
                        displayEntityExtractionSamples(data.entity_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'concept-definitions':
                        displayConceptDefinitions(data.concept_definitions);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'summarization':
                        displaySummarizationSamples(data.summarization_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'paraphrases':
                        displayParaphrasesSamples(data.paraphrases_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'intent-detection':
                        displayIntentDetectionSamples(data.intent_detection_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'topic-classification':
                        displayTopicClassificationSamples(data.topic_classification_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'role-relationships':
                        displayRoleRelationshipSamples(data.relationship_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'comparisons':
                        displayComparisonSamples(data.comparison_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    case 'fact-opinion':
                        displayFactOpinionSamples(data.fact_opinion_samples);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                    default:
                        // Generic display for other types
                        displayGenericDataset(data, datasetType);
                        // Add global thinking process button if available
                        if (data.thinking_process) {
                            addGlobalThinkingProcess(data.thinking_process, datasetType);
                        }
                        break;
                }
            } else {
                // Show error with more details
                console.error('API Error:', data.error);
                qaPreview.innerHTML = '';
                const errorMsg = data.error || 'An error occurred while regenerating the dataset.';
                const errorElement = showError(errorMsg, qaPreview);
                // Add additional hint if needed
                const hint = document.createElement('p');
                hint.className = 'small-text';
                hint.textContent = 'If sources can\'t be found, try going back to the upload page and re-processing your sources.';
                errorElement.appendChild(hint);
            }
            // Remove the loading indicator
            const loadingElement = document.querySelector('.loading-indicator');
            if (loadingElement) {
                loadingElement.remove();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            qaPreview.innerHTML = '';
            showError('Failed to connect to the server. Please try again later.', qaPreview);
            // Remove the loading indicator
            const loadingElement = document.querySelector('.loading-indicator');
            if (loadingElement) {
                loadingElement.remove();
            }
        });
    });

    // Export buttons
    const exportJsonBtn = document.getElementById('export-json');
    const exportCsvBtn = document.getElementById('export-csv');
    const exportTxtBtn = document.getElementById('export-txt');

    if (exportJsonBtn) {
        exportJsonBtn.addEventListener('click', function() {
            exportDataset('json');
        });
    }

    if (exportCsvBtn) {
        exportCsvBtn.addEventListener('click', function() {
            exportDataset('csv');
        });
    }

    if (exportTxtBtn) {
        exportTxtBtn.addEventListener('click', function() {
            exportDataset('txt');
        });
    }

    // Export dataset function
    function exportDataset(format) {
        // Get dataset type from selected radio button
        const datasetTypeEls = document.querySelectorAll('input[name="dataset-type"]');
        let datasetType = 'qa';
        datasetTypeEls.forEach(el => {
            if (el.checked) {
                datasetType = el.value;
            }
        });

        const pairs = document.querySelectorAll('.qa-pair');
        const items = [];

        pairs.forEach(pair => {
            switch(datasetType) {
                case 'qa':
                    // Improved extraction with better text cleaning
                    let questionEl = pair.querySelector('.qa-question');
                    let answerEl = pair.querySelector('.qa-answer');
                    let sourceEl = pair.querySelector('.qa-source');

                    // Log elements for debugging
                    console.log('QA elements found:', {
                        questionEl: questionEl,
                        answerEl: answerEl,
                        sourceEl: sourceEl
                    });

                    // Extract text with proper cleaning
                    let question = '';
                    if (questionEl) {
                        // Remove the 'Q1: ' prefix if present
                        let text = questionEl.textContent.trim();
                        question = text.includes(':') ? text.substring(text.indexOf(':') + 1).trim() : text;
                    }

                    let answer = '';
                    if (answerEl) {
                        // Remove the 'A: ' prefix if present
                        let text = answerEl.textContent.trim();
                        answer = text.includes(':') ? text.substring(text.indexOf(':') + 1).trim() : text;
                    }

                    let source = '';
                    if (sourceEl) {
                        // Remove the 'Source: ' prefix if present
                        let text = sourceEl.textContent.trim();
                        source = text.includes(':') ? text.substring(text.indexOf(':') + 1).trim() : text;
                    }

                    items.push({ question, answer, source });
                    break;

                case 'concept-definitions':
                    const concept = pair.querySelector('.qa-question h4 + p')?.textContent || '';
                    const definition = pair.querySelector('.qa-answer h4 + p')?.textContent || '';
                    items.push({ concept, definition });
                    break;

                case 'entity-extraction':
                    const text = pair.querySelector('.qa-question p')?.textContent || '';
                    const entities = Array.from(pair.querySelectorAll('.entities-table tbody tr')).map(row => ({
                        entity: row.cells[0].textContent,
                        type: row.cells[1].textContent
                    }));
                    items.push({ text, entities: JSON.stringify(entities) });
                    break;

                case 'summarization':
                    const originalText = pair.querySelector('.qa-question p')?.textContent || '';
                    const summary = pair.querySelector('.qa-answer p')?.textContent || '';
                    items.push({ original_text: originalText, summary });
                    break;

                case 'procedures':
                    // Get the procedure task from the DOM
                    let procedureTask = pair.querySelector('.qa-question p')?.textContent || '';

                    // Extract steps directly from the DOM
                    let procedureSteps = [];
                    const stepsList = pair.querySelector('.procedure-steps');
                    if (stepsList) {
                        // Get steps from the DOM list items
                        procedureSteps = Array.from(stepsList.querySelectorAll('li')).map(li => li.textContent);
                    }

                    // Extract requirements directly from the DOM
                    let procedureRequirements = [];
                    const reqList = pair.querySelector('.procedure-requirements');
                    if (reqList) {
                        // Get requirements from the DOM list items
                        procedureRequirements = Array.from(reqList.querySelectorAll('li')).map(li => li.textContent);
                    }

                    // If steps or requirements are still empty, use default values
                    if (procedureSteps.length === 0) {
                        procedureSteps = [
                            "Step 1: Prepare necessary resources",
                            "Step 2: Follow standard procedures",
                            "Step 3: Complete the task"
                        ];
                    }

                    if (procedureRequirements.length === 0) {
                        procedureRequirements = [
                            "Required tools and software",
                            "Basic knowledge of the task",
                            "Access permissions"
                        ];
                    }

                    // Log the extracted data for debugging
                    console.log('Extracted procedure data:', {
                        task: procedureTask,
                        steps: procedureSteps,
                        requirements: procedureRequirements
                    });

                    // Add the procedure to the items array
                    items.push({
                        task: procedureTask,
                        steps: procedureSteps,
                        requirements: procedureRequirements
                    });
                    break;

                case 'comparisons':
                    console.log('Processing comparison item:', pair);

                    // Extract topic from the question section
                    const topic = pair.querySelector('.qa-question p')?.textContent || '';
                    console.log('Extracted topic:', topic);

                    // Extract comparison data from the answer section
                    const comparisonAnswer = pair.querySelector('.qa-answer p')?.textContent || '';
                    console.log('Extracted comparison answer:', comparisonAnswer);

                    // Parse the answer to extract similarities and differences
                    const similaritiesMatch = comparisonAnswer.match(/Similarities:\s*([\s\S]*?)(?=Differences:|$)/i);
                    const differencesMatch = comparisonAnswer.match(/Differences:\s*([\s\S]*?)$/i);

                    // Process similarities
                    let similarities = [];
                    if (similaritiesMatch && similaritiesMatch[1]) {
                        similarities = similaritiesMatch[1]
                            .split('\n')
                            .map(sim => sim.trim())
                            .filter(sim => sim && sim !== '-');

                        // If we have a single string with bullet points, split it further
                        if (similarities.length === 1 && (similarities[0].includes('•') || similarities[0].includes('-'))) {
                            similarities = similarities[0]
                                .split(/[•\-]/)
                                .map(s => s.trim())
                                .filter(s => s);
                        }
                    }
                    console.log('Extracted similarities:', similarities);

                    // Process differences
                    let differences = [];
                    if (differencesMatch && differencesMatch[1]) {
                        differences = differencesMatch[1]
                            .split('\n')
                            .map(diff => diff.trim())
                            .filter(diff => diff && diff !== '-');

                        // If we have a single string with bullet points, split it further
                        if (differences.length === 1 && (differences[0].includes('•') || differences[0].includes('-'))) {
                            differences = differences[0]
                                .split(/[•\-]/)
                                .map(d => d.trim())
                                .filter(d => d);
                        }
                    }
                    console.log('Extracted differences:', differences);

                    // If we still don't have any similarities or differences, try to extract from list items
                    if ((similarities.length === 0 || differences.length === 0) && pair.querySelectorAll('ul, ol').length > 0) {
                        const lists = pair.querySelectorAll('ul, ol');

                        // First list is usually similarities, second is differences
                        if (lists.length >= 1 && similarities.length === 0) {
                            similarities = Array.from(lists[0].querySelectorAll('li')).map(li => li.textContent.trim());
                            console.log('Extracted similarities from list:', similarities);
                        }

                        if (lists.length >= 2 && differences.length === 0) {
                            differences = Array.from(lists[1].querySelectorAll('li')).map(li => li.textContent.trim());
                            console.log('Extracted differences from list:', differences);
                        }
                    }

                    // Ensure we have at least empty arrays if nothing was found
                    similarities = similarities || [];
                    differences = differences || [];

                    // Check if the topic contains "vs" which might indicate it's from Ollama model
                    // and we need to split it into item1 and item2
                    let comparisonItem = {};
                    if (topic.includes(' vs ')) {
                        const [item1, item2] = topic.split(' vs ').map(item => item.trim());
                        comparisonItem = {
                            topic: topic,
                            item1: item1,
                            item2: item2,
                            similarities: similarities,
                            differences: differences
                        };
                    } else {
                        comparisonItem = {
                            topic: topic || "Comparison",
                            similarities: similarities,
                            differences: differences
                        };
                    }

                    console.log('Final comparison item:', comparisonItem);
                    items.push(comparisonItem);
                    break;

                case 'role-relationships':
                    const roleContext = pair.querySelector('.qa-question p')?.textContent || '';
                    const roleAnswer = pair.querySelector('.qa-answer p')?.textContent || '';
                    const roleSourceText = pair.querySelector('.qa-source')?.textContent || '';
                    // Removed sourceId variable as it's no longer needed

                    // Parse the answer to extract role1, role2, and relationship
                    const roleMatch = roleAnswer.match(/Role 1: (.*?)\s*Role 2: (.*?)\s*Relationship: (.*)/i);

                    // If we can extract the roles and relationship from the text
                    if (roleMatch) {
                        items.push({
                            role1: roleMatch[1].trim(),
                            role2: roleMatch[2].trim(),
                            relationship: roleMatch[3].trim(),
                            context: roleContext,
                            source: roleSourceText.replace('Source: ', '')
                            // sourceId removed as requested
                        });
                    } else {
                        // If we can't extract from text, try to get the data from the DOM structure
                        // Look for specific elements that might contain the role information
                        const role1Elem = pair.querySelector('[data-role="role1"]');
                        const role2Elem = pair.querySelector('[data-role="role2"]');
                        const relationshipElem = pair.querySelector('[data-role="relationship"]');

                        if (role1Elem && role2Elem && relationshipElem) {
                            // If we found the elements, use their content
                            items.push({
                                role1: role1Elem.textContent.trim(),
                                role2: role2Elem.textContent.trim(),
                                relationship: relationshipElem.textContent.trim(),
                                context: roleContext,
                                source: roleSourceText.replace('Source: ', '')
                                // sourceId removed as requested
                            });
                        } else {
                            // If all else fails, create a default entry with the available information
                            // This ensures we always have something in the export
                            items.push({
                                role1: "Role 1",
                                role2: "Role 2",
                                relationship: roleAnswer.trim(),
                                context: roleContext,
                                source: roleSourceText.replace('Source: ', '')
                                // sourceId removed as requested
                            });
                        }
                    }
                    break;

                case 'code-explanations':
                    const codeTopic = pair.querySelector('.qa-question p')?.textContent || '';
                    const code = pair.querySelector('.code-block-container')?.textContent || '';
                    items.push({ topic: codeTopic, code });
                    break;

                case 'fact-opinion':
                    console.log('Processing fact-opinion item:', pair);

                    // Extract statement from the question section
                    const statement = pair.querySelector('.qa-question p')?.textContent || '';
                    console.log('Extracted statement:', statement);

                    // Extract fact-opinion data from the answer section
                    const factOpinionAnswer = pair.querySelector('.qa-answer p')?.textContent || '';
                    console.log('Extracted fact-opinion answer:', factOpinionAnswer);

                    // Parse the answer to extract classification and reasoning/explanation
                    const classificationMatch = factOpinionAnswer.match(/Classification:\s*([\s\S]*?)(?=Reasoning:|Explanation:|$)/i);
                    const reasoningMatch = factOpinionAnswer.match(/Reasoning:\s*([\s\S]*?)$/i);
                    const explanationMatch = factOpinionAnswer.match(/Explanation:\s*([\s\S]*?)$/i);

                    // Extract classification
                    let classification = '';
                    if (classificationMatch && classificationMatch[1]) {
                        classification = classificationMatch[1].trim();
                    } else {
                        // Try to find classification in a different format
                        const classificationElement = pair.querySelector('.classification-value');
                        if (classificationElement) {
                            classification = classificationElement.textContent.trim();
                        } else if (factOpinionAnswer.includes('Fact') || factOpinionAnswer.includes('Opinion')) {
                            // Try to determine if it contains the words "Fact" or "Opinion"
                            classification = factOpinionAnswer.includes('Fact') ? 'Fact' : 'Opinion';
                        }
                    }
                    console.log('Extracted classification:', classification);

                    // Extract reasoning or explanation (accept either one)
                    let reasoning = '';

                    // First try to get reasoning
                    if (reasoningMatch && reasoningMatch[1]) {
                        reasoning = reasoningMatch[1].trim();
                        console.log('Extracted reasoning from Reasoning field:', reasoning);
                    }
                    // If no reasoning, try to get explanation
                    else if (explanationMatch && explanationMatch[1]) {
                        reasoning = explanationMatch[1].trim();
                        console.log('Extracted reasoning from Explanation field:', reasoning);
                    }
                    // Try other formats
                    else {
                        // Try to find reasoning/explanation in a different format
                        const reasoningElement = pair.querySelector('.reasoning-value') || pair.querySelector('.explanation-value');
                        if (reasoningElement) {
                            reasoning = reasoningElement.textContent.trim();
                            console.log('Extracted reasoning from DOM element:', reasoning);
                        } else {
                            // If we couldn't find a specific reasoning/explanation section, use the whole answer
                            // but remove the classification part if present
                            reasoning = factOpinionAnswer
                                .replace(/Classification:\s*(?:Fact|Opinion)/i, '')
                                .replace(/Reasoning:/i, '')
                                .replace(/Explanation:/i, '')
                                .trim();
                            console.log('Extracted reasoning from full answer text:', reasoning);
                        }
                    }
                    console.log('Final reasoning value:', reasoning);

                    // Create the fact-opinion item
                    const factOpinionItem = {
                        statement: statement,
                        classification: classification,
                        reasoning: reasoning
                    };

                    console.log('Final fact-opinion item:', factOpinionItem);
                    items.push(factOpinionItem);
                    break;

                case 'cause-effect':
                    const cause = pair.querySelector('.qa-question p')?.textContent || '';
                    const effect = pair.querySelector('.qa-answer p')?.textContent || '';
                    const explanationText = pair.querySelector('.explanation p')?.textContent || '';
                    items.push({ cause, effect, explanation: explanationText });
                    break;

                case 'paraphrases':
                    console.log('Processing paraphrase item:', pair);

                    // Extract original sentence from the question section
                    const originalSentence = pair.querySelector('.qa-question p')?.textContent || '';
                    console.log('Extracted original sentence:', originalSentence);

                    // Try to extract paraphrases from list items first (most reliable)
                    let paraphrases = [];
                    const paraphrasesList = pair.querySelector('.paraphrases-list');

                    if (paraphrasesList) {
                        // Extract from list items
                        paraphrases = Array.from(paraphrasesList.querySelectorAll('li')).map(li => {
                            // Extract just the text part, ignoring the number
                            const paraphraseText = li.querySelector('.paraphrase-text')?.textContent || li.textContent;
                            return paraphraseText.trim();
                        }).filter(text => text);

                        console.log('Extracted paraphrases from list:', paraphrases);
                    } else {
                        // Fallback: try to extract from the answer text
                        const paraphraseAnswer = pair.querySelector('.qa-answer p')?.textContent || '';
                        console.log('Extracted paraphrase answer text:', paraphraseAnswer);

                        // Split by newlines or bullet points
                        paraphrases = paraphraseAnswer
                            .split(/[\n\r]|(?:^\s*[-•])/m)
                            .map(line => line.trim())
                            .filter(line => line && !line.startsWith('Paraphrases:'));

                        console.log('Extracted paraphrases from text:', paraphrases);
                    }

                    // If we still don't have paraphrases, try one more approach
                    if (paraphrases.length === 0) {
                        // Look for any text content in the answer section
                        const answerContent = pair.querySelector('.qa-answer')?.textContent || '';

                        // Remove the heading and split by numbers or bullet points
                        const cleanedContent = answerContent.replace(/Paraphrases:/i, '').trim();
                        paraphrases = cleanedContent
                            .split(/(?:\d+\.|\s*[-•])/m)
                            .map(line => line.trim())
                            .filter(line => line);

                        console.log('Extracted paraphrases from answer content:', paraphrases);
                    }

                    // Create the paraphrase item
                    const paraphraseItem = {
                        original_sentence: originalSentence,
                        paraphrases: paraphrases || []
                    };

                    console.log('Final paraphrase item:', paraphraseItem);
                    items.push(paraphraseItem);
                    break;

                case 'intent-detection':
                    const utterance = pair.querySelector('.qa-question p')?.textContent || '';
                    const intent = pair.querySelector('.qa-answer p')?.textContent || '';
                    items.push({ utterance, intent });
                    break;

                case 'topic-classification':
                    const topicText = pair.querySelector('.qa-question p')?.textContent || '';
                    const topicCategory = pair.querySelector('.qa-answer p')?.textContent || '';
                    items.push({ text: topicText, topic: topicCategory });
                    break;
            }
        });

        if (format === 'json') {
            const blob = new Blob([JSON.stringify(items, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${datasetType}_dataset.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        } else if (format === 'csv') {
            if (items.length === 0) {
                showAlert('No data to export');
                return;
            }

            // Get headers from the first item
            const headers = Object.keys(items[0]);

            // Convert items to CSV rows
            const csvRows = [
                headers.join(','), // Header row
                ...items.map(item =>
                    headers.map(header => {
                        const value = item[header];
                        // Handle entity objects specially
                        if (header === 'entities' && typeof value === 'string') {
                            const entities = JSON.parse(value);
                            return `"${entities.map(e => `${e.entity} (${e.type})`).join('; ')}"`;
                        }
                        // Escape quotes and wrap in quotes if contains comma or quotes
                        const escapedValue = String(value).replace(/"/g, '""');
                        return /[,"\n]/.test(escapedValue) ? `"${escapedValue}"` : escapedValue;
                    }).join(',')
                )
            ];

            const csvContent = csvRows.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${datasetType}_dataset.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        } else if (format === 'txt') {
            if (items.length === 0) {
                showAlert('No data to export');
                return;
            }

            let content = `${datasetType.toUpperCase().replace(/-/g, ' ')} DATASET\n`;
            content += `Generated: ${new Date().toLocaleString()}\n`;
            content += `Total items: ${items.length}\n\n`;

            items.forEach((item, index) => {
                content += `Sample ${index + 1}:\n`;
                content += '='.repeat(40) + '\n';

                if (datasetType === 'entity-extraction') {
                    content += `TEXT: ${item.text || '(none)'}\n`;
                    content += 'ENTITIES:\n';
                    const entities = JSON.parse(item.entities);
                    if (entities.length === 0) {
                        content += '  (none)\n';
                    } else {
                        entities.forEach(entity => {
                            content += `  - ${entity.entity} (${entity.type})\n`;
                        });
                    }
                } else {
                    // Handle other dataset types
                    Object.entries(item).forEach(([key, value]) => {
                        const formattedKey = key.replace(/_/g, ' ').toUpperCase();
                        if (Array.isArray(value)) {
                            content += `${formattedKey}:\n`;
                            if (value.length === 0) {
                                content += `  (none)\n`;
                            } else {
                                value.forEach(v => content += `  - ${v}\n`);
                            }
                        } else {
                            content += `${formattedKey}: ${value || '(none)'}\n`;
                        }
                    });
                }
                content += '\n';
            });

            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${datasetType}_dataset.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // Select Ollama as default model if available
    const ollamaOption = document.querySelector('.model-option[data-model="ollama"]');
    if (ollamaOption) {
        ollamaOption.click();
    }

    // Function to show API key notification
    function showApiKeyNotification(message, isInfo = false) {
        apiKeyNotification.textContent = message;
        apiKeyNotification.classList.remove('hidden');

        if (isInfo) {
            apiKeyNotification.classList.add('info');
        } else {
            apiKeyNotification.classList.remove('info');
        }
    }

    // Function to hide API key notification
    function hideApiKeyNotification() {
        apiKeyNotification.classList.add('hidden');
    }

    // Handle API key option change
    useDefaultKeyRadio.addEventListener('change', function() {
        if (this.checked) {
            customKeyInput.classList.add('hidden');
            useDefaultApiKey = true;
            updateGenerateButtonState();

            // Show info notification about using default key
            showApiKeyNotification(`Using default API key for ${selectedModel.name}.`, true);
        }
    });

    useCustomKeyRadio.addEventListener('change', function() {
        if (this.checked) {
            customKeyInput.classList.remove('hidden');
            useDefaultApiKey = false;
            updateGenerateButtonState();

            // Hide notification
            hideApiKeyNotification();
        }
    });

    // Function to check if default API key is available
    function checkDefaultApiKey(modelId) {
        fetch('/check-default-key', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: modelId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (!data.has_default_key) {
                // If no default key, disable default option and select custom
                useDefaultKeyRadio.disabled = true;
                useCustomKeyRadio.checked = true;
                customKeyInput.classList.remove('hidden');
                useDefaultApiKey = false;

                // Show a notification
                showApiKeyNotification(`No default API key available for ${selectedModel.name}. Please enter a custom key.`);
            } else {
                // Enable default option
                useDefaultKeyRadio.disabled = false;
                // Show a notification that default key is available
                showApiKeyNotification(`Default API key is available for ${selectedModel.name}.`, true);
            }

            // Update generate button state
            updateGenerateButtonState();
        })
        .catch(error => {
            console.error('Error checking default API key:', error);
        });
    }

    // Initialize selection and buttons if they exist
    if (selectAllSourcesBtn) {
        selectAllSourcesBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.source-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const sourceItem = checkbox.closest('.source-item');
                if (sourceItem) {
                    sourceItem.classList.add('selected');
                }
            });
            updateSelectedSourceCount();
            // Refresh keywords when source selection changes
            loadKeywords();
            updateGenerateButtonState();
        });
    }

    if (deselectAllSourcesBtn) {
        deselectAllSourcesBtn.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('.source-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                const sourceItem = checkbox.closest('.source-item');
                if (sourceItem) {
                    sourceItem.classList.remove('selected');
                }
            });
            updateSelectedSourceCount();
            // Refresh keywords when source selection changes
            loadKeywords();
            updateGenerateButtonState();
        });
    }

    // Function to update the count of selected sources
    function updateSelectedSourceCount() {
        const selectedCheckboxes = document.querySelectorAll('.source-checkbox:checked');
        if (selectedSourceCountEl) {
            selectedSourceCountEl.textContent = selectedCheckboxes.length;
        }
    }

    // Function to get currently selected source IDs
    function getSelectedSourceIds() {
        const selectedSources = [];
        const checkboxes = document.querySelectorAll('.source-checkbox:checked');

        checkboxes.forEach(checkbox => {
            if (checkbox.dataset.sourceId) {
                selectedSources.push(checkbox.dataset.sourceId);
            }
        });

        return selectedSources;
    }

    // Add a utility function at the top level of the file
    // This will ensure a default format is used everywhere
    function getOutputFormat() {
        // Since we removed the format selection, always return JSON
        return 'json';
    }

    // Add Source functionality
    const existingDOMContentLoadedHandlers = document.onDOMContentLoaded;

    // Modal elements
    const modalBackdrop = document.getElementById('source-modal-backdrop');
    const closeBtn = document.getElementById('close-source-modal');
    const cancelBtn = document.getElementById('cancel-source');
    const addSourceBtn = document.getElementById('add-source-btn');
    const addFirstSourceBtn = document.getElementById('add-first-source-btn');
    const uploadSourceBtn = document.getElementById('upload-source');
    const modalTabs = document.querySelectorAll('.source-modal-tab');
    const uploadArea = document.getElementById('upload-area');
    const urlArea = document.getElementById('url-area');
    const textArea = document.getElementById('text-area');
    const fileInput = document.getElementById('file-input');
    const urlInput = document.getElementById('url-input');
    const addUrlBtn = document.getElementById('add-url');
    const urlList = document.getElementById('url-list');
    const textContent = document.getElementById('text-content');
    const textSourceName = document.getElementById('text-source-name');

    // Open modal
    if (addSourceBtn) {
        addSourceBtn.addEventListener('click', function() {
            modalBackdrop.classList.add('show');
        });
    }

    // Open modal from "no sources" message
    if (addFirstSourceBtn) {
        addFirstSourceBtn.addEventListener('click', function() {
            modalBackdrop.classList.add('show');
        });
    }

    // Close modal
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeModal);
    }

    function closeModal() {
        if (modalBackdrop) {
            modalBackdrop.classList.remove('show');
        }

        // Reset form elements
        if (fileInput) {
            fileInput.value = '';
        }
        resetUploadArea();

        // Clear URL input
        if (document.getElementById('url-input')) {
            document.getElementById('url-input').value = '';
        }
        if (document.getElementById('url-source-name')) {
            document.getElementById('url-source-name').value = '';
        }

        // Clear text input
        if (document.getElementById('text-content')) {
            document.getElementById('text-content').value = '';
        }
        if (document.getElementById('text-source-name')) {
            document.getElementById('text-source-name').value = '';
        }
    }

    // Tab switching
    if (modalTabs) {
        modalTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                modalTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const tabName = this.getAttribute('data-tab');
                if (tabName === 'upload') {
                    uploadArea.style.display = 'block';
                    urlArea.style.display = 'none';
                    textArea.style.display = 'none';
                } else if (tabName === 'url') {
                    uploadArea.style.display = 'none';
                    urlArea.style.display = 'block';
                    textArea.style.display = 'none';
                } else if (tabName === 'text') {
                    uploadArea.style.display = 'none';
                    urlArea.style.display = 'none';
                    textArea.style.display = 'block';
                }
            });
        });
    }

    // File input handling
    if (uploadArea) {
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = 'var(--primary)';
            this.style.backgroundColor = 'rgba(74, 107, 255, 0.05)';
        });

        uploadArea.addEventListener('dragleave', function() {
            this.style.borderColor = 'var(--panel-border)';
            this.style.backgroundColor = '';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = 'var(--panel-border)';
            this.style.backgroundColor = '';

            const files = e.dataTransfer.files;
            fileInput.files = files;
            // Show selected files
            showSelectedFiles(files);
        });

        fileInput.addEventListener('change', function() {
            showSelectedFiles(this.files);
        });
    }

    function showSelectedFiles(files) {
        // Create a list of selected files to show in the upload area
        let fileList = Array.from(files).map(file => file.name).join(', ');
        if (fileList) {
            let fileCountText = files.length > 1 ? `${files.length} files selected` : '1 file selected';
            uploadArea.innerHTML = `
                <i class="fas fa-check-circle" style="color: #28a745;"></i>
                <p>${fileCountText}</p>
                <span class="small-text">${fileList}</span>
            `;
        }
    }

    // URL handling - Removing the old code for adding URLs to a list
    // if (addUrlBtn) {
    //     addUrlBtn.addEventListener('click', function() {
    //         const url = urlInput.value.trim();
    //         if (url) {
    //             addUrlToList(url);
    //             urlInput.value = '';
    //         }
    //     });
    // }

    // function addUrlToList(url) {
    //     const urlItem = document.createElement('div');
    //     urlItem.className = 'url-item';
    //     urlItem.innerHTML = `
    //         <div class="url-text">${url}</div>
    //         <button class="remove-url" data-url="${url}">&times;</button>
    //     `;
    //     urlList.appendChild(urlItem);
    //
    //     // Add event listener to remove button
    //     urlItem.querySelector('.remove-url').addEventListener('click', function() {
    //         urlItem.remove();
    //     });
    // }

    // Upload button functionality
    if (uploadSourceBtn) {
        uploadSourceBtn.addEventListener('click', function() {
            // Check which tab is active
            const activeTab = document.querySelector('.source-modal-tab.active').getAttribute('data-tab');

            if (activeTab === 'upload') {
                // Handle file upload
                const files = fileInput.files;
                if (files.length > 0) {
                    uploadFiles(files);
                }
            } else if (activeTab === 'url') {
                // Handle URL upload - simplified to process a single URL directly
                const url = document.getElementById('url-input').value.trim();
                const sourceName = document.getElementById('url-source-name').value.trim();

                if (url) {
                    uploadUrl(url, sourceName);
                }
            } else if (activeTab === 'text') {
                // Handle text upload
                const textContent = document.getElementById('text-content').value.trim();
                const textSourceName = document.getElementById('text-source-name').value.trim();
                if (textContent) {
                    uploadText(textContent, textSourceName);
                }
            }
        });
    }

    // Helper function to add sources without replacing existing ones
    function addSourceWithoutReplacing(newSource) {
        return fetch('/get-sources')
            .then(response => response.json())
            .then(data => {
                let existingSources = data.sources || [];

                // Combine existing sources with the new source
                const combinedSources = [...existingSources, newSource];

                // Send the combined sources to the server
                return fetch('/process-sources', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ sources: combinedSources })
                });
            });
    }

    function uploadFiles(files) {
        // Create FormData object
        const formData = new FormData();
        for (let i = 0; i < files.length; i++) {
            formData.append('file', files[i]);
        }

        // Show loading state
        uploadSourceBtn.disabled = true;
        uploadSourceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

        // Make API call to upload files
        fetch('/upload-document', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Upload failed');
            }
            return response.json();
        })
        .then(data => {
            console.log('Upload processed successfully:', data);

            // Create source object using the processed data
            const source = {
                type: 'file',
                name: files[0].name,
                content: data.content,
                wordCount: data.word_count
            };

            // Add the processed source to the sources list
            return addSourceWithoutReplacing(source);
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to store file source');
            }
            return response.json();
        })
        .then(data => {
            console.log('Source added successfully:', data);

            // Close modal and reset upload area
            closeModal();
            resetUploadArea();

            // Immediately update sources and UI
            return fetch('/get-sources')
                .then(response => response.json())
                .then(data => {
                    sources = data.sources;
                    displaySources();
                    if (sourceList) sourceList.style.display = 'block';
                    if (noSourcesMessage) noSourcesMessage.style.display = 'none';
                    updateGenerateButtonState();
                    loadKeywords();
                    if (sourceCountEl) sourceCountEl.textContent = sources.length;
                    updateSelectedSourceCount();
                })
                .then(() => {
                    // Still poll to ensure everything is synced
                    return pollForSourceAvailability();
                });
        })
        .catch(error => {
            console.error('Error uploading files:', error);
            showError('Failed to upload files. Please try again.');
        })
        .finally(() => {
            // Reset button state
            uploadSourceBtn.disabled = false;
            uploadSourceBtn.innerHTML = 'Upload';
        });
    }

    // Add new polling function
    function pollForSourceAvailability(attempts = 0, maxAttempts = 10) {
        return new Promise((resolve, reject) => {
            const checkSources = () => {
                fetch('/get-sources')
                    .then(response => response.json())
                    .then(data => {
                        if (data.sources && data.sources.length > 0) {
                            // Sources are available, update display
                            sources = data.sources;
                            displaySources();

                            // Show the source list and hide "no sources" message
                            if (sourceList) sourceList.style.display = 'block';
                            if (noSourcesMessage) noSourcesMessage.style.display = 'none';

                            // Update UI elements
                            updateGenerateButtonState();
                            loadKeywords();

                            // Update source counts
                            if (sourceCountEl) sourceCountEl.textContent = sources.length;
                            updateSelectedSourceCount();

                            resolve();
                        } else if (attempts < maxAttempts) {
                            // Try again after a delay
                            setTimeout(() => {
                                pollForSourceAvailability(attempts + 1, maxAttempts).then(resolve).catch(reject);
                            }, 1000);
                        } else {
                            reject(new Error('Timeout waiting for sources'));
                        }
                    })
                    .catch(reject);
            };

            checkSources();
        });
    }

    function uploadUrl(url, sourceName) {
        // Show loading state
        uploadSourceBtn.disabled = true;
        uploadSourceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        // Make API call to process URL
        fetch('/process-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url: url, sourceName: sourceName })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to process URL');
            }
            return response.json();
        })
        .then(data => {
            console.log('URL processed successfully:', data);

            // Create source object using the processed data
            const source = {
                type: 'link',
                name: sourceName || url,
                url: url,
                content: data.content,
                wordCount: data.word_count
            };

            // Now add the processed source to the sources list
            return addSourceWithoutReplacing(source);
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to store URL source');
            }
            return response.json();
        })
        .then(data => {
            console.log('Source added successfully:', data);
            // Close modal
            closeModal();

            // Poll for source availability
            return pollForSourceAvailability();
        })
        .catch(error => {
            console.error('Error processing URL:', error);
            showError('Failed to process URL. Please try again.');
        })
        .finally(() => {
            // Reset button state
            uploadSourceBtn.disabled = false;
            uploadSourceBtn.innerHTML = 'Upload';
        });
    }

    // Keep the existing uploadUrls function for compatibility, but have it call our new uploadUrl function
    function uploadUrls(urls) {
        if (urls && urls.length > 0) {
            uploadUrl(urls[0]);
        }
    }

    function uploadText(textContent, sourceName) {
        // Show loading state
        uploadSourceBtn.disabled = true;
        uploadSourceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        // Make API call to add text content
        fetch('/process-text', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ text: textContent, sourceName: sourceName })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to add text content');
            }
            return response.json();
        })
        .then(data => {
            console.log('Text content processed successfully:', data);

            // Create source object using the processed data
            const source = {
                type: 'text',
                name: sourceName || 'Text Source',
                content: data.content,
                wordCount: data.word_count
            };

            // Now add the processed source to the sources list
            return addSourceWithoutReplacing(source);
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to store text source');
            }
            return response.json();
        })
        .then(data => {
            console.log('Source added successfully:', data);
            // Close modal
            closeModal();

            // Poll for source availability
            return pollForSourceAvailability();
        })
        .catch(error => {
            console.error('Error adding text content:', error);
            showError('Failed to add text content. Please try again.');
        })
        .finally(() => {
            // Reset button state
            uploadSourceBtn.disabled = false;
            uploadSourceBtn.innerHTML = 'Upload';
        });
    }

    function resetUploadArea() {
        if (uploadArea) {
            uploadArea.innerHTML = `
                <i class="fas fa-cloud-upload-alt"></i>
                <p>Drag and drop your files here or <span class="browse">browse</span></p>
                <span class="small-text">Supported formats: PDF, DOCX, TXT, MD</span>
            `;
        }
        if (fileInput) {
            fileInput.value = '';
        }
    }

    // Function to add global thinking process container
    function addGlobalThinkingProcess(thinkingProcess, datasetType) {
        if (!thinkingProcess) {
            // If no thinking process is provided, create a placeholder with a message
            thinkingProcess = "No thinking process was provided by the model. Please regenerate the dataset to see the model's reasoning.";
        }

        // Format the dataset type name nicely
        const datasetTypeName = datasetType.split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        const thinkingContainer = document.createElement('div');
        thinkingContainer.className = 'global-thinking-process';
        thinkingContainer.style.marginBottom = '20px';

        // Add a title for the thinking process
        const titleElement = document.createElement('h4');
        titleElement.textContent = `Model's Thinking Process for ${datasetTypeName} Generation`;
        titleElement.style.textAlign = 'center';
        titleElement.style.marginBottom = '10px';
        titleElement.style.color = '#4a6bff';
        thinkingContainer.appendChild(titleElement);

        // Add a description
        const description = document.createElement('p');
        description.textContent = "The Ollama model shows its reasoning process when generating this dataset. This helps you understand how the model approached the task.";
        description.style.textAlign = 'center';
        description.style.fontSize = '0.9rem';
        description.style.margin = '0 auto 15px auto';
        description.style.maxWidth = '600px';
        thinkingContainer.appendChild(description);

        // Add the thinking process button to the container
        addThinkingProcessButton(thinkingContainer, thinkingProcess);

        // Insert at the top of the QA preview
        qaPreview.insertBefore(thinkingContainer, qaPreview.firstChild);

        // Automatically expand the thinking process to make it visible by default
        const processDiv = thinkingContainer.querySelector('.thinking-process');
        const button = thinkingContainer.querySelector('.thinking-process-btn');
        if (processDiv && button) {
            processDiv.classList.remove('hidden');
            button.innerHTML = '<i class="fas fa-brain"></i> Hide Thinking Process';
        }
    }
});
