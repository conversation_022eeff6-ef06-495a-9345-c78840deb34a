<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Dashboard{% endblock %} - DADP</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin/admin_layout.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="header-flex">
                <button class="sidebar-toggle" id="sidebarToggle" title="Toggle Sidebar">
                    <i class="bi bi-list"></i>
                </button>
                <a class="sidebar-brand" href="{{ url_for('admin_routes.dashboard') }}">
                    <span>DADP</span>
                </a>
            </div>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.dashboard' else '' }}"
                   href="{{ url_for('admin_routes.dashboard') }}"
                   data-title="Dashboard" data-tooltip="Dashboard">
                    <i class="bi bi-grid-1x2-fill"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.manage_users' else '' }}"
                   href="{{ url_for('admin_routes.manage_users') }}"
                   data-title="Manage Users" data-tooltip="Manage Users">
                    <i class="bi bi-people-fill"></i>
                    <span>Manage Users</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.edit_instructions' else '' }}"
                   href="{{ url_for('admin_routes.edit_instructions') }}"
                   data-title="edit_instructions" data-tooltip="edit_instructions">
                   <i class="bi bi-pencil"></i>
                   <span>Edit Instructions</span>

                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.admin_ocr_directory' else '' }}"
                   href="{{ url_for('admin_routes.admin_ocr_directory') }}"
                   data-title="OCR Directory" data-tooltip="OCR Directory">
                    <i class="bi bi-folder2-open"></i>
                    <span>OCR Directory</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.fetch_data_route' else '' }}"
                   href="{{ url_for('admin_routes.fetch_data_route') }}"
                   data-title="Fetch Data" data-tooltip="Fetch Data">
                    <i class="bi bi-cloud-download"></i>
                    <span>Fetch Data</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.admin_synthetic_upload' else '' }}"
                   href="{{ url_for('admin_routes.admin_synthetic_upload') }}"
                   data-title="Synthetic Data" data-tooltip="Synthetic Data">
                    <i class="bi bi-database-fill-dash"></i>
                    <span>Synthetic Data</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'admin_routes.auditor_tracking' else '' }}"
                   href="{{ url_for('admin_routes.auditor_tracking') }}"
                   data-title="Auditor Tracking" data-tooltip="Auditor Tracking">
                    <i class="bi bi-bar-chart-fill"></i>
                    <span>Data Delivery</span>
                </a>
            </li>
        </ul>

        <div class="sidebar-footer mt-auto">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth_routes.logout') }}" data-title="Logout" data-tooltip="Logout">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>Logout</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle me-2"></i> <span>{{ session.username|default('User') }}</span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="userDropdown">
                        <li><span class="dropdown-item-text text-muted">{{ session.role|capitalize|default('Role') }}</span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'user_routes.change_password' else '' }}" href="{{ url_for('user_routes.change_password') }}" data-tooltip="Change Password">
                                <i class="bi bi-key-fill me-2"></i> <span>Change Password</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <div class="main-content">
        <div class="flash-container container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show shadow-sm" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        {% block content %}{% endblock %}
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/admin/admin_layout.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>