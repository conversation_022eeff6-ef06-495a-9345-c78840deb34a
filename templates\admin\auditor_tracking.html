{% extends "admin/admin_base.html" %}

{% block title %}Data Delivery{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/auditor_tracking.css') }}">
{% endblock %}

{% block content %}
<!-- Merge Result Alert (Initially Hidden) -->
<div id="merge-result" class="alert" style="display: none;"></div>

<div class="page-container">
    <div class="unified-container">
        <h1 class="page-title"><i class="bi bi-cloud-arrow-up me-2"></i>Data Delivery</h1>
        
        <!-- Top Controls -->
        <div class="d-flex justify-content-end mb-3">
            <a href="{{ url_for('admin_routes.auditor_tracking', refresh=1) }}" class="refresh-btn">
                <i class="bi bi-arrow-clockwise"></i> Refresh Data
            </a>
        </div>

        <div class="content-row">
            <!-- Dataset Status Section -->
            <div class="status-column">
                <div class="status-section-header">
                    <i class="bi bi-database-check me-2"></i>Dataset Status
                </div>
                
                <!-- Merged Datasets Section -->
                <div class="status-section">
                    <h4 class="status-heading">
                        <span class="merged-badge"></span> Merged Datasets <span class="badge bg-success ms-2">{{ merged_datasets|length }}</span>
                    </h4>
                    {% if merged_datasets %}
                    <ul class="dataset-list">
                        {% for dataset in merged_datasets %}
                        <li class="dataset-item">
                            <a class="dataset-name"><i class="bi bi-check-circle-fill text-success me-2"></i>{{ dataset.name }}</a>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted small text-center py-3"><i class="bi bi-exclamation-circle me-2"></i>No merged datasets available</p>
                    {% endif %}
                </div>
                
                <!-- Pending Datasets Section -->
                <div class="status-section">
                    <h4 class="status-heading">
                        <span class="pending-badge"></span> Pending Datasets <span class="badge bg-warning text-dark ms-2">{{ pending_datasets|length }}</span>
                    </h4>
                    {% if pending_datasets %}
                    <ul class="dataset-list">
                        {% for dataset in pending_datasets %}
                        <li class="dataset-item">
                            <a class="dataset-name"><i class="bi bi-hourglass-split text-warning me-2"></i>{{ dataset.name }}</a>
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted small text-center py-3"><i class="bi bi-exclamation-circle me-2"></i>No pending datasets available</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Main Content Area -->
            <div class="main-column">
                <!-- Dataset Selection -->
                <div class="dataset-selection">
                    <label for="dataset-select"><i class="bi bi-database me-2"></i>Select Dataset to Process</label>
                    <select id="dataset-select" class="form-select">
                        {% for dataset in datasets %}
                        <option value="{{ dataset.id }}" {% if dataset.id == selected_dataset %}selected{% endif %}>{{ dataset.name }}</option>
                        {% endfor %}
                    </select>
                    <small class="text-muted"><i class="bi bi-info-circle me-1"></i>Choose a dataset to view completion statistics</small>
                </div>
        
                <div class="stats-container">
                    <!-- Batch Statistics -->
                    <h2 class="batch-heading text-center my-3">
                        <i class="bi bi-folder-check me-2"></i>Dataset: {{ selected_dataset }}
                    </h2>
                    
                    <!-- Completion Stats -->
                    <div class="stats-row">
                        <div class="completion-count">
                            <i class="bi bi-pie-chart-fill me-2 text-primary"></i>{{ total_completed }} / {{ total_batches }}
                        </div>
                        {% if total_completed == total_batches and total_batches > 0 %}
                            <div class="status-badge complete">
                                <i class="bi bi-check-circle-fill me-1"></i>Complete
                            </div>
                        {% else %}
                            <div class="status-badge pending">
                                <i class="bi bi-hourglass-split me-1"></i>{{ ((total_completed / total_batches * 100) if total_batches > 0 else 0)|round(1) }}%
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Progress Bar -->
                    {% if total_batches > 0 %}
                    <div class="progress">
                        <div class="progress-bar" 
                             data-progress="{{ (total_completed / total_batches * 100) if total_batches > 0 else 0 }}"
                             role="progressbar" aria-valuenow="{{ (total_completed / total_batches * 100) if total_batches > 0 else 0 }}" 
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="text-muted small">
                        <i class="bi bi-clipboard-data me-1"></i>{{ total_batches - total_completed }} batches remaining
                    </div>
                    {% else %}
                    <div class="text-muted mt-3 text-center">
                        <i class="bi bi-exclamation-triangle me-2"></i>No batches available for this dataset
                    </div>
                    {% endif %}
                    
                    <!-- Status Info -->
                    <div class="info-row mt-4">
                        {% if total_completed == total_batches and total_batches > 0 %}
                            <div class="info-icon success"><i class="bi bi-check-circle"></i></div>
                            <div>Ready to merge all JSON files</div>
                        {% elif total_batches > 0 %}
                            <div class="info-icon warning"><i class="bi bi-exclamation-circle"></i></div>
                            <div>{{ total_batches - total_completed }} batches pending completion</div>
                        {% else %}
                            <div class="info-icon info"><i class="bi bi-info-circle"></i></div>
                            <div>No data available for this dataset</div>
                        {% endif %}
                    </div>
                    
                    <!-- Output Path -->
                    <div class="output-path">
                        <i class="bi bi-folder me-2"></i>Output: /Data/DATP Datasets/Final_Dataset/{{ selected_dataset }}.json
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="actions-row">
                        <div class="storage-action-container">
                            <select id="storage-destination" class="form-select">
                                <option value="current"><i class="bi bi-hdd"></i> Current Storage</option>
                                <option value="google-drive"><i class="bi bi-google"></i> Google Drive</option>
                                <option value="dropbox" disabled><i class="bi bi-dropbox"></i> Dropbox (Coming Soon)</option>
                                <option value="database" disabled><i class="bi bi-database"></i> Database (Coming Soon)</option>
                            </select>
                            <button id="merge-json-btn" class="merge-btn" 
                                {% if not selected_dataset or total_completed != total_batches or total_batches == 0 %}disabled{% endif %}>
                                <i class="bi bi-file-earmark-code"></i> Merge JSON Files
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Variables for auditor_tracking.js
    var AUDITOR_TRACKING_URL = "{{ url_for('admin_routes.auditor_tracking') }}";
    var MERGE_DATASET_URL = "{{ url_for('admin_routes.merge_dataset_json') }}";
    var SELECTED_DATASET = "{{ selected_dataset }}";
    var CHECK_DRIVE_URL = "{{ url_for('admin_routes.check_google_drive_connection') }}";
</script>
<script src="{{ url_for('static', filename='js/admin/auditor_tracking.js') }}"></script>
{% endblock %}
