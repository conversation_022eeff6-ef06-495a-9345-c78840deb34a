{% extends "admin/admin_base.html" %}

{% block title %}Admin Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/dashboard.css') }}">
{% endblock %}

{% block content %}
<!-- Professional Title Section -->
<div class="container-fluid">
    <div class="row justify-content-center align-items-center">
        <div class="col-12 text-center">
            <h1 class="dashboard-title">Admin Dashboard</h1>
        </div>
    </div>
</div>

<!-- Data Connector Section -->
<div class="card mb-4" style="margin-top: 2rem;">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-hdd-stack me-2"></i>Data Connectors</h5>
    </div>
    <div class="card-body">
        <p>Connect to your data sources to manage datasets and annotation tasks.</p>
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="bi bi-database-fill-gear fs-1 text-primary mb-3"></i>
                        <h6 class="card-title">Google-Drive Database</h6>
                        <p class="card-text small text-muted">Connect to manage structured annotation data and user information.</p>
                        <button class="btn btn-primary" id="btnConnectGoogleDrive">
                            <i class="bi bi-plug-fill me-1"></i> Connect to Google Drive
                        </button>
                        <button class="btn btn-outline-danger ms-2" id="btnResetGoogleDrive">
                            <i class="bi bi-x-circle me-1"></i> Reset Connection
                        </button>
                        <div class="form-text mt-2" id="googleDriveConnectionStatus">Not connected</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                     <div class="card-body text-center">
                        <i class="bi bi-folder-symlink-fill fs-1 text-success mb-3"></i>
                        <h6 class="card-title">NAS Storage</h6>
                        <p class="card-text small text-muted">Connect to access and manage image datasets stored on Network Attached Storage.</p>
                        <button class="btn btn-success" id="btnConnectNas">
                            <i class="bi bi-plug-fill me-1"></i> Connect to NAS
                        </button>
                        <div class="form-text mt-2" id="nasConnectionStatus">Not connected</div>

                        
                        <!-- Replace with OCR directory button -->
                        <div class="mt-3 d-none" id="ocrDirectorySection">
                            <button class="btn btn-outline-success w-100" id="btnGoToOcrDirectory">
                                <i class="bi bi-folder2-open me-2"></i> Click here to select annotation dataset
                            </button>
                        </div>
                   </div>
               </div>
           </div>
       </div>
   </div>
</div>

<!-- Auditor Settings Section -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>Auditor Settings</h5>
        <span class="badge bg-primary-soft" id="settingsStatus">
            <i class="bi bi-circle-fill me-1"></i> Settings Active
        </span>
    </div>
    <div class="card-body">
        <div class="settings-container">
            <div class="mb-4">
                <h6 class="fw-bold mb-3">Select the folder where auditors will access images for review tasks.</h6>

                <div class="folder-input-container">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="bi bi-folder2"></i>
                        </span>
                        <input type="text"
                               class="form-control"
                               id="auditorImageFolder"
                               value="{{ config.AUDITOR_IMAGE_FOLDER }}"
                               data-original-value="{{ config.AUDITOR_IMAGE_FOLDER }}"
                               readonly>
                        <button class="btn btn-outline-primary"
                                type="button"
                                id="browseAuditorFolder"
                                {% if not config.NAS_CONNECTED %}disabled{% endif %}>
                            <i class="bi bi-folder2-open me-1"></i> Browse
                        </button>
                    </div>
                    <div class="form-text" id="folderPathHelp">
                        {% if config.NAS_CONNECTED %}
                        <i class="bi bi-info-circle me-1"></i> Click Browse to select a folder from connected NAS
                        {% else %}
                        <i class="bi bi-exclamation-circle me-1 text-warning"></i> Connect to NAS first to browse folders
                        {% endif %}
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-primary"
                            id="saveAuditorFolderBtn"
                            disabled>
                        <i class="bi bi-save me-1"></i> Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- NAS Connection Modal -->
<div class="modal fade" id="nasConnectionModal" tabindex="-1" aria-labelledby="nasConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="nasConnectionModalLabel">Connect to NAS</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="nasConnectionForm">
                    <div class="mb-3">
                        <label for="nasType" class="form-label">NAS Type</label>
                        <select class="form-select" id="nasType" name="nas_type" required>
                            <option value="ftp">FTP</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="nasUrl" class="form-label">NAS URL</label>
                        <input type="text" class="form-control" id="nasUrl" name="nas_url"
                               placeholder="Example: ************:6001" required>
                        <div class="form-text">IP address or hostname with port (if needed)</div>
                    </div>
                    <div class="mb-3">
                        <label for="nasUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="nasUsername" name="nas_username" required>
                    </div>
                    <div class="mb-3">
                        <label for="nasPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="nasPassword" name="nas_password" required>
                    </div>
                    <div class="alert alert-info mb-3">
                        <small>
                            <i class="bi bi-info-circle me-1"></i>
                            These credentials will be used for the current session only.
                        </small>
                    </div>
                </form>
                <div id="connectionResultArea" class="alert d-none mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary-NAS" id="btnTestNasConnection">
                    <i class="bi bi-plug-fill me-1"></i> Connect
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Google Drive Connection Modal -->
<div class="modal fade" id="googleDriveConnectionModal" tabindex="-1" aria-labelledby="googleDriveConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="googleDriveConnectionModalLabel">Connect to Google Drive</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="googleDriveConnectionForm">
                    <div class="mb-3">
                        <label for="driveClientId" class="form-label">Client ID</label>
                        <input type="text" class="form-control" id="driveClientId" name="client_id" required>
                    </div>
                    <div class="mb-3">
                        <label for="driveClientSecret" class="form-label">Client Secret</label>
                        <input type="text" class="form-control" id="driveClientSecret" name="client_secret" required>
                    </div>
                    <div class="mb-3">
                        <label for="driveFolderId" class="form-label">Root Folder ID (optional)</label>
                        <input type="text" class="form-control" id="driveFolderId" name="folder_id"
                               placeholder="Google Drive Folder ID">
                        <div class="form-text">Leave empty to access all folders</div>
                    </div>
                    <div class="alert alert-info mb-3">
                        <small>
                            <i class="bi bi-info-circle me-1"></i>
                            You'll need to authorize this application in a new browser window after connecting.
                        </small>
                    </div>
                </form>
                <div id="driveConnectionResultArea" class="alert d-none mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="btnConfigureGoogleDrive">
                    <i class="bi bi-plug-fill me-1"></i> Configure & Connect
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Define routes with Flask URL generation
    const routes = {
        disconnect_nas: "{{ url_for('admin_routes.disconnect_nas') }}",
        connect_nas: "{{ url_for('admin_routes.connect_nas') }}",
        get_nas_folders: "{{ url_for('admin_routes.get_nas_folders') }}",
        browse_nas_directory: "{{ url_for('admin_routes.browse_nas_directory') }}",
        set_auditor_image_folder: "{{ url_for('admin_routes.set_auditor_image_folder') }}",
        check_nas_connection: "{{ url_for('admin_routes.check_nas_connection') }}",
        configure_google_drive: "{{ url_for('admin_routes.configure_google_drive') }}",
        check_google_drive_connection: "{{ url_for('admin_routes.check_google_drive_connection') }}",
        reset_google_drive: "{{ url_for('admin_routes.reset_google_drive') }}"
    };
</script>
<script src="{{ url_for('static', filename='js/admin/dashboard.js') }}"></script>
{% endblock %}