{% extends "admin/admin_base.html" %}

{% block title %}Data Sources - DADP{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin/data_sources.css') }}" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Background elements -->
<div class="noise-texture"></div>
<div class="background-grid"></div>
<div class="floating-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
</div>

<div class="content-wrapper">
    <div class="container-fluid">
        <!-- Supervision mode header -->
        <div class="page-header">
            <h1 class="page-title">Data Sources</h1>
        </div>


        <!-- Data Sources Cards -->
        <div class="sources-grid">
            <!-- Telegram Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-telegram source-icon"></i>
                        <h3>Telegram</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Connect to Telegram channels and download media content for processing.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i>Access channel messages</li>
                        <li><i class="fas fa-check"></i>Download media files</li>
                        <li><i class="fas fa-check"></i>Filter by date and type</li>
                        <li><i class="fas fa-check"></i>Batch processing</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge connected">Connected</span>
                    <a href="{{ url_for('fetch_data.telegram_connect') }}" class="btn-source">Connect <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>

            <!-- Twitter Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-twitter source-icon"></i>
                        <h3>Twitter</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Track hashtags and download media from Twitter for analysis.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Track hashtags</li>
                        <li><i class="fas fa-check"></i> Monitor accounts</li>
                        <li><i class="fas fa-check"></i> Download media</li>
                        <li><i class="fas fa-check"></i> Sentiment analysis</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>

            <!-- Database Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-database source-icon"></i>
                        <h3>Database</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Connect to external databases and import data for processing.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> SQL databases</li>
                        <li><i class="fas fa-check"></i> NoSQL databases</li>
                        <li><i class="fas fa-check"></i> Custom Database</li>
                        <li><i class="fas fa-check"></i> Scheduled imports</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <!-- YouTube Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-youtube source-icon"></i>
                        <h3>YouTube</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Access YouTube videos and channels for content analysis.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Download videos</li>
                        <li><i class="fas fa-check"></i> Channel analysis</li>
                        <li><i class="fas fa-check"></i> Comment extraction</li>
                        <li><i class="fas fa-check"></i> Metadata processing</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <!-- LinkedIn Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-linkedin source-icon"></i>
                        <h3>LinkedIn</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Monitor LinkedIn posts and profiles for professional insights.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Profile tracking</li>
                        <li><i class="fas fa-check"></i> Post analysis</li>
                        <li><i class="fas fa-check"></i> Media collection</li>
                        <li><i class="fas fa-check"></i> Engagement metrics</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <!-- NAS Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-server source-icon"></i>
                        <h3>NAS</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Connect to network storage devices for secure data import and processing.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Bulk file import</li>
                        <li><i class="fas fa-check"></i> Scheduled syncing</li>
                        <li><i class="fas fa-check"></i> Secure connections</li>
                        <li><i class="fas fa-check"></i> Automated backup</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <!-- Google Drive Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-google-drive source-icon"></i>
                        <h3>Google Drive</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Import files and documents from Google Drive for analysis.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Document import</li>
                        <li><i class="fas fa-check"></i> Media collection</li>
                        <li><i class="fas fa-check"></i> Folder monitoring</li>
                        <li><i class="fas fa-check"></i> Auto-synchronization</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
            
            <!-- Instagram Card -->
            <div class="source-card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <i class="fab fa-instagram source-icon"></i>
                        <h3>Instagram</h3>
                    </div>
                </div>
                <div class="card-body">
                    <p>Track Instagram posts and stories for visual content analysis.</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Post tracking</li>
                        <li><i class="fas fa-check"></i> Story collection</li>
                        <li><i class="fas fa-check"></i> Hashtag monitoring</li>
                        <li><i class="fas fa-check"></i> Image analysis</li>
                    </ul>
                </div>
                <div class="card-footer">
                    <span class="status-badge coming-soon">Coming Soon</span>
                </div>
            </div>
        </div>

        <!-- Compliance Statement -->
        <div class="compliance-section">
            <div class="compliance-statement">
                <div class="compliance-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="compliance-text">
                    <h4>Our Commitment to Ethics:</h4>
                    <p>ProcessVenue adheres to the highest standards of data privacy and ethical practices. We're committed to compliant and transparent data handling, respecting all privacy regulations, and never engaging in unauthorized data collection. Your trust is our priority—we safeguard it through ethical operations and strict adherence to our <a href="#" class="terms-link">Terms & Conditions</a>.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/data_sources.js') }}"></script>
{% endblock %}