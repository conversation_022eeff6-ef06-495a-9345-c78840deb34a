{% extends "admin/admin_base.html" %}

{% block title %}Telegram Channels - DADP{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin/telegram_channels_list.css') }}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <div class="container-fluid">
        <div class="page-container">
            <div class="section-header">
                <h1>Telegram Channels <span class="channel-count-badge" id="channel-count">0</span></h1>
                <div>
                    <button id="disconnect-btn" class="btn btn-danger">
                        <i class="fas fa-unlink"></i> Disconnect
                    </button>
                </div>
            </div>

            <div class="dashboard">
                <!-- Channels Area (68%) -->
                <div class="channels-container">
                    <div class="card">
                        <div class="card-header bg-primary">
                            <h5>Available Channels</h5>
                            <button id="refresh-channels-btn" class="btn btn-light">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- Loading State -->
                            <div id="channels-loading" class="text-center py-4">
                                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                    <span class="visually-hidden">Loading channels...</span>
                                </div>
                                <h5 class="text-muted">Loading your channels...</h5>
                                <p class="text-muted small">This may take a moment depending on the number of channels</p>
                            </div>

                            <!-- Channel Grid -->
                            <div id="channels-grid" class="channel-grid">
                                <!-- Channels will be loaded dynamically -->
                            </div>

                            <!-- No Channels Message -->
                            <div id="no-channels-message" class="alert alert-info my-4" style="display: none;">
                                <i class="fas fa-info-circle me-2"></i> No channels found. You might need to join or create some Telegram channels first.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Area (32%) -->
                <div class="analysis-container">
                    <div class="card h-100">
                        <div class="card-header bg-primary">
                            <h5 id="analysis-title">Channel Analysis</h5>
                            <button id="download-analytics-btn" class="btn btn-light">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                        <div class="card-body">
                            <!-- No Channel Selected State -->
                            <div id="no-channel-selected" class="text-center py-5">
                                <div class="channel-icon mx-auto mb-3">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <h4 class="mb-2">No Channel Selected</h4>
                                <p class="text-muted">Select a channel from the list to view detailed analytics</p>
                            </div>

                            <!-- Loading State -->
                            <div id="analysis-loading" style="display: none;" class="text-center py-5">
                                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                    <span class="visually-hidden">Loading analytics data...</span>
                                </div>
                                <h5 class="mb-2">Analyzing channel data...</h5>
                                <p class="text-muted small">This may take a moment depending on the channel size</p>
                            </div>

                            <!-- Analysis Content -->
                            <div id="analysis-content" style="display: none;">
                                <!-- Channel Summary -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="channel-icon me-3">
                                                        <i class="fas fa-users"></i>
                                                    </div>
                                                    <div>
                                                        <h4 id="selected-channel-name" class="mb-0 fw-bold">Channel Name</h4>
                                                        <p id="selected-channel-details" class="text-muted mb-0">
                                                            Username • Participants
                                                        </p>
                                                    </div>
                                                </div>

                                                <div class="row g-2 mt-3">
                                                    <div class="col-6 col-md-3">
                                                        <div class="stat-badge w-100">
                                                            <span class="badge bg-primary" id="total-images-stat">0</span>
                                                            <small>Total Images</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="stat-badge w-100">
                                                            <span class="badge bg-success" id="active-days-stat">0</span>
                                                            <small>Active Days</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="stat-badge w-100">
                                                            <span class="badge bg-info text-nowrap" id="first-date-stat">N/A</span>
                                                            <small>First Post</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-6 col-md-3">
                                                        <div class="stat-badge w-100">
                                                            <span class="badge bg-warning text-nowrap" id="last-date-stat">N/A</span>
                                                            <small>Latest Post</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Simplified Date Analysis -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-header bg-light">
                                                <h5 class="mb-0">Date Analysis</h5>
                                            </div>
                                            <div class="card-body">
                                                <ul class="nav nav-tabs" id="dateTabs" role="tablist">
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link active" id="year-tab" data-bs-toggle="tab" data-bs-target="#year-content" type="button" role="tab" aria-controls="year-content" aria-selected="true">By Year</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="month-tab" data-bs-toggle="tab" data-bs-target="#month-content" type="button" role="tab" aria-controls="month-content" aria-selected="false">By Month</button>
                                                    </li>
                                                    <li class="nav-item" role="presentation">
                                                        <button class="nav-link" id="date-tab" data-bs-toggle="tab" data-bs-target="#date-content" type="button" role="tab" aria-controls="date-content" aria-selected="false">By Date</button>
                                                    </li>
                                                </ul>

                                                <div class="tab-content mt-3" id="dateTabsContent">
                                                    <!-- Year Analysis -->
                                                    <div class="tab-pane fade show active" id="year-content" role="tabpanel" aria-labelledby="year-tab">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-striped table-hover">
                                                                <thead class="table-primary">
                                                                    <tr>
                                                                        <th>Year</th>
                                                                        <th>Images</th>
                                                                        <th>Percentage</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="year-analysis-table">
                                                                    <tr>
                                                                        <td colspan="3" class="text-center">Loading...</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <!-- Month Analysis -->
                                                    <div class="tab-pane fade" id="month-content" role="tabpanel" aria-labelledby="month-tab">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-striped table-hover">
                                                                <thead class="table-primary">
                                                                    <tr>
                                                                        <th>Month</th>
                                                                        <th>Images</th>
                                                                        <th>Percentage</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="month-analysis-table">
                                                                    <tr>
                                                                        <td colspan="3" class="text-center">Loading...</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <!-- Date Analysis -->
                                                    <div class="tab-pane fade" id="date-content" role="tabpanel" aria-labelledby="date-tab">
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-striped table-hover">
                                                                <thead class="table-primary">
                                                                    <tr>
                                                                        <th>Date</th>
                                                                        <th>Images</th>
                                                                        <th>Percentage</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="date-analysis-table">
                                                                    <tr>
                                                                        <td colspan="3" class="text-center">Loading...</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4 text-end">
                                    <button class="btn btn-primary" id="view-images-btn">
                                        <i class="fas fa-images"></i> View Images
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Message -->
            <div id="status-message" class="alert status-message" style="display: none;"></div>

            <!-- Loading Spinner -->
            <div id="loading-spinner" class="position-fixed top-50 start-50 translate-middle" style="display: none; z-index: 9999;">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/telegram_channels_list.js') }}"></script>
{% endblock %}
