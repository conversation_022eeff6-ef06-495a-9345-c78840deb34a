{% extends "admin/admin_base.html" %}

{% block title %}Telegram Connection - DADP{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin/telegram_connect.css') }}" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="noise-texture"></div>
<div class="background-grid"></div>
<div class="floating-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
</div>

<div class="content-wrapper">
    <div class="container-fluid">
        <div class="page-header">
            <h1 class="page-title">Telegram Connection</h1>
            <a href="{{ url_for('admin_routes.fetch_data_route') }}" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Data Sources
            </a>
        </div>

        <!-- Connection Forms Container -->
        <div class="connection-forms-container">
            <!-- Right side container for forms -->
            <div class="form-container">
                <!-- Connection Form -->
                <div id="connection-form" class="connection-card">
                    <div class="card-header">
                        <h5 class="card-title"><i class="fab fa-telegram"></i> Connect to Telegram</h5>
                    </div>
                    <div class="card-body">
                        <div id="session-status" class="session-status alert-info" style="display: none;">
                            <i class="fas fa-info-circle"></i>
                            <div>Checking for existing session...</div>
                        </div>
                        <form id="telegram-form">
                            <div class="form-group">
                                <label for="api_id" class="form-label">API ID</label>
                                <input type="text" class="form-control" id="api_id" required>
                                <div class="form-text">Enter your Telegram API ID from my.telegram.org</div>
                            </div>
                            <div class="form-group">
                                <label for="api_hash" class="form-label">API Hash</label>
                                <input type="text" class="form-control" id="api_hash" required>
                                <div class="form-text">Enter your Telegram API Hash from my.telegram.org</div>
                            </div>
                            <div class="form-group">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" required>
                                <div class="form-text">Enter your phone number with country code (e.g., +1234567890)</div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plug"></i> Connect
                                </button>
                                <button type="button" id="reset-session-btn" class="btn btn-secondary">
                                    <i class="fas fa-trash-alt"></i> Reset Session
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Verification Code Form -->
                <div id="verification-form" class="connection-card" style="display: none;">
                    <div class="card-header">
                        <h5 class="card-title"><i class="fas fa-shield-alt"></i> Enter Verification Code</h5>
                    </div>
                    <div class="card-body">
                        <div class="session-status alert-info">
                            <i class="fas fa-info-circle"></i>
                            <div>A verification code has been sent to your Telegram app or SMS. Please enter it below.</div>
                        </div>
                        <form id="code-form">
                            <div class="form-group">
                                <label for="code" class="form-label">Verification Code</label>
                                <input type="text" class="form-control" id="code" required>
                                <div class="form-text">Enter the code exactly as received</div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check"></i> Verify Code
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 2FA Password Form -->
                <div id="password-form" class="connection-card" style="display: none;">
                    <div class="card-header">
                        <h5 class="card-title"><i class="fas fa-lock"></i> Enter 2FA Password</h5>
                    </div>
                    <div class="card-body">
                        <div class="session-status alert-info">
                            <i class="fas fa-info-circle"></i>
                            <div>Your account is protected with Two-Factor Authentication. Please enter your password.</div>
                        </div>
                        <form id="password-form-inputs">
                            <div class="form-group">
                                <label for="password" class="form-label">2FA Password</label>
                                <input type="password" class="form-control" id="password" required>
                                <div class="form-text">Enter the password you set up in Telegram's Privacy & Security settings</div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-unlock-alt"></i> Verify Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Message -->
<div id="status-message" class="status-message" style="display: none;">
    <i class="fas fa-info-circle me-2"></i>
    <span class="message-text"></span>
</div>

<!-- Loading Spinner -->
<div id="loading-spinner" class="loading-spinner" style="display: none;">
    <div class="spinner">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/telegram_connect.js') }}"></script>
{% endblock %}