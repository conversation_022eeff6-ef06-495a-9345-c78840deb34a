{% extends "admin/admin_base.html" %}

{% block title %}Telegram Images - DADP{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin/telegram_images.css') }}" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="content-wrapper">
  <div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <h1 id="channel-title">Channel Images</h1>
        <div>
            <a href="{{ url_for('fetch_data.telegram_channels_list') }}" class="btn btn-primary back-button">
                <i class="fas fa-arrow-left"></i> Back to Channels
            </a>
        </div>
    </div>

    <!-- Date Filter -->
    <div class="filter-section">
        <div class="filter-header">
            <i class="fas fa-filter"></i> Filter Images by Date
        </div>
        <div class="filter-content">
            <div class="row">
                <!-- Year Dropdown -->
                <div class="col-md-4">
                    <label for="yearSelect" class="form-label">Year</label>
                    <select class="form-select" id="yearSelect" aria-label="Select year">
                        <option value="">All Years</option>
                    </select>
                </div>

                <!-- Month Dropdown -->
                <div class="col-md-4">
                    <label for="monthSelect" class="form-label">Month</label>
                    <select class="form-select" id="monthSelect" aria-label="Select month">
                        <option value="">All Months</option>
                    </select>
                </div>

                <!-- Date Dropdown -->
                <div class="col-md-4">
                    <label for="dateSelect" class="form-label">Date</label>
                    <select class="form-select" id="dateSelect"aria-label="Select date">
                        <option value="">All Dates</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons-container" style="display: none;">
        <div>
            <button id="select-all-btn" class="btn btn-outline-primary">
                <i class="fas fa-check-square"></i> Select All
            </button>
            <button id="deselect-all-btn" class="btn btn-outline-secondary">
                <i class="fas fa-square"></i> Deselect All
            </button>
            <span class="selected-count" id="selected-count">0 images selected</span>
        </div>
        <div class="action-buttons-right">
            <button id="download-selected-btn" class="btn btn-primary">
                <i class="fas fa-download"></i> Download Selected
            </button>
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" id="uploadStorageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cloud-upload-alt"></i> Upload to DataStorage
                </button>
                <ul class="dropdown-menu" aria-labelledby="uploadStorageDropdown">
                    <li><a class="dropdown-item" href="#" id="upload-drive-btn"><i class="fab fa-google-drive me-2"></i>Google Drive</a></li>
                    <li><a class="dropdown-item coming-soon" href="#"><i class="fab fa-aws me-2"></i>AWS <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                    <li><a class="dropdown-item coming-soon" href="#"><i class="fas fa-server me-2"></i>Azure <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                    <li><a class="dropdown-item coming-soon" href="#"><i class="fas fa-database me-2"></i>NAS <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- No Images Message -->
    <div id="no-images-message" class="alert-message alert-info">
        <i class="fas fa-info-circle me-2"></i>Please select a date to view images
    </div>

    <!-- Selection Controls (moved above) -->
    <div id="selection-controls" class="card selection-controls" style="display: none;">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center flex-wrap">
                <!-- This section is now moved to the "Action Buttons" above -->
            </div>
        </div>
    </div>

    <!-- Images Grid -->
    <div id="images-grid" class="images-grid">
        <!-- Images will be added dynamically -->
        <!-- Loading Skeleton (will be replaced by actual images) -->
        <div class="skeleton-card">
            <div class="skeleton skeleton-image"></div>
            <div class="skeleton-body">
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text-sm"></div>
                <div class="skeleton-actions">
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                </div>
            </div>
        </div>
        <div class="skeleton-card">
            <div class="skeleton skeleton-image"></div>
            <div class="skeleton-body">
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text-sm"></div>
                <div class="skeleton-actions">
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                </div>
            </div>
        </div>
        <div class="skeleton-card">
            <div class="skeleton skeleton-image"></div>
            <div class="skeleton-body">
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text-sm"></div>
                <div class="skeleton-actions">
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                </div>
            </div>
        </div>
        <div class="skeleton-card">
            <div class="skeleton skeleton-image"></div>
            <div class="skeleton-body">
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text-sm"></div>
                <div class="skeleton-actions">
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                    <div class="skeleton skeleton-button"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add a new message for channels with no images -->
    <div id="empty-channel-message" class="alert-message" style="display: none;">
        <i class="fas fa-image"></i>
        <div>
            <h3>No Images Available</h3>
            <p>This channel does not contain any images.</p>
        </div>
    </div>
  </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel"><i class="fas fa-image me-2"></i>Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modal-image" src="" alt="Full image" class="modal-image mb-3">
                <p id="modal-caption" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-primary" id="download-btn">
                    <i class="fas fa-download me-2"></i>Download
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Results Modal -->
<div class="modal fade" id="uploadResultsModal" tabindex="-1" aria-labelledby="uploadResultsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadResultsModalLabel">
                    <i class="fas fa-cloud me-2"></i>Upload Results
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Upload Progress -->
                <div id="upload-status" class="upload-status">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="upload-text">
                        Uploading images to Google Drive...
                    </div>
                </div>

                <!-- Upload Results -->
                <div id="upload-results" class="upload-results" style="display: none;">
                    <!-- Folder Information -->
                    <div class="result-section folder-info">
                        <div class="section-header">
                            <i class="fas fa-folder me-2"></i>
                            <h6>Folder Information</h6>
                        </div>
                        <div class="section-content">
                            <div class="info-row">
                                <span class="info-label">Folder name:</span>
                                <span id="folder-name" class="info-value"></span>
                            </div>
                            <div id="folder-info" class="info-row">
                                <!-- Date folder information will be added here -->
                            </div>
                            <div class="info-row">
                                <span class="info-label">Folder link:</span>
                                <a id="folder-link" target="_blank" href="#" class="info-value link-primary"></a>
                            </div>
                        </div>
                    </div>

                    <!-- Uploaded Files -->
                    <div class="result-section uploaded-files">
                        <div class="section-header">
                            <i class="fas fa-file-image me-2"></i>
                            <h6>Uploaded Files</h6>
                        </div>
                        <div class="section-content">
                            <ul id="uploaded-files-list" class="files-list">
                                <!-- Uploaded files will be added here -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Upload Error -->
                <div id="upload-error" class="upload-error" style="display: none;">
                    <div class="error-content">
                        <i class="fas fa-exclamation-circle"></i>
                        <div class="error-message">
                            <h6>Upload Failed</h6>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <a id="view-folder-btn" href="#" target="_blank" class="btn btn-primary" style="display: none;">
                    <i class="fas fa-external-link-alt me-2"></i>View in Google Drive
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Status Message -->
<div id="status-message" class="status-message" style="display: none;"></div>

<!-- Loading Spinner -->
<div id="loading-spinner" class="loading-spinner" style="display: none;">
    <div class="spinner">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<!-- Image Editor Modal -->
<div class="modal fade" id="imageEditorModal" tabindex="-1" aria-labelledby="imageEditorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageEditorModalLabel"><i class="fas fa-edit me-2"></i>Image Editor</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="height: calc(95vh - 130px); overflow: hidden;">
                <div class="row h-100">
                    <div class="col-md-9">
                        <div class="img-container" style="width: 100%; height: 600px;">
                            <img id="editor-image" src="" alt="Image for editing">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <!-- Rotate Button -->
                        <button class="btn btn-primary w-100 mb-3" onclick="rotateImage()">
                            <i class="fas fa-sync-alt me-2"></i>Rotate 90°
                        </button>

                        <!-- Crop Controls -->
                        <div class="d-flex gap-2 mb-3">
                            <button class="btn btn-outline-primary flex-grow-1" onclick="startNewCrop()">
                                <i class="fas fa-crop-alt me-1"></i>Crop
                            </button>
                            <button class="btn btn-outline-success flex-grow-1" onclick="addCurrentCrop()">
                                <i class="fas fa-plus me-1"></i>Add
                            </button>
                            <button class="btn btn-outline-danger flex-grow-1" onclick="resetCrop()">
                                <i class="fas fa-undo me-1"></i>Reset
                            </button>
                        </div>

                        <!-- Crop List -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-list me-2"></i>Crop Selections</h6>
                            </div>
                            <div class="card-body p-2">
                                <div id="crop-list" class="list-group">
                                    <!-- Crop items will be added here -->
                                </div>
                                <div id="no-crops-message" class="text-center text-muted py-3">
                                    <i class="fas fa-info-circle mb-2 d-block" style="font-size: 1.5rem;"></i>
                                    No crops selected yet. Use the crop tool to select areas of the image.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="saveAllCropsLocally()">
                    <i class="fas fa-download me-2"></i>Save Locally
                </button>
                <div class="dropdown">
                    <button class="btn btn-success dropdown-toggle" type="button" id="uploadStorageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cloud-upload-alt"></i> Upload to DataStorage
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="uploadStorageDropdown">
                        <li><a class="dropdown-item" href="#" id="editor-upload-drive-btn"><i class="fab fa-google-drive me-2"></i>Google Drive</a></li>
                        <li><a class="dropdown-item coming-soon" href="#"><i class="fab fa-aws me-2"></i>AWS <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                        <li><a class="dropdown-item coming-soon" href="#"><i class="fas fa-server me-2"></i>Azure <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                        <li><a class="dropdown-item coming-soon" href="#"><i class="fas fa-database me-2"></i>NAS <span class="badge bg-secondary ms-1">Coming Soon</span></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
<script src="{{ url_for('static', filename='js/admin/telegram_images.js') }}"></script>
{% endblock %}