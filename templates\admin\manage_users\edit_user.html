{% extends "admin/admin_base.html" %}

{% block title %}Edit User - {{ user.username }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/edit_user.css') }}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-9">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="bi bi-person-gear me-2"></i>Edit User: {{ user.username }}</h4>
                    <span class="badge {% if user.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                        <i class="bi {% if user.is_active %}bi-check-circle-fill{% else %}bi-dash-circle-fill{% endif %} me-1"></i>
                        {{ 'Active' if user.is_active else 'Suspended' }}
                    </span>
                </div>

                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="info-card">
                                <h5 class="border-bottom pb-2 mb-3"><i class="bi bi-info-circle me-2"></i>User Information</h5>
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Username:</strong></div>
                                    <div>{{ user.username }}</div>
                                </div>
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Role:</strong></div>
                                    <div>
                                        <span class="badge {% if user.role == 'admin' %}bg-danger{% elif user.role == 'auditor' %}bg-info{% else %}bg-primary{% endif %}">
                                            <i class="bi {% if user.role == 'admin' %}bi-shield-fill{% elif user.role == 'auditor' %}bi-eye-fill{% else %}bi-person-fill{% endif %} me-1"></i>
                                            {{ user.role|capitalize }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Last Login:</strong></div>
                                    <div>{{ user.last_login or 'Never' }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stats-card">
                                <h5 class="border-bottom pb-2 mb-3"><i class="bi bi-graph-up me-2"></i>User Activity</h5>
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Status:</strong></div>
                                    <div>
                                        <span class="badge {% if user.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                            <i class="bi {% if user.is_active %}bi-check-circle-fill{% else %}bi-dash-circle-fill{% endif %} me-1"></i>
                                            {{ 'Active' if user.is_active else 'Suspended' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Account Type:</strong></div>
                                    <div>Custom User</div>
                                </div>
                                {% if user.role == 'annotator' and user.annotation_mode %}
                                <div class="mb-2 d-flex align-items-center">
                                    <div style="width: 120px;"><strong>Mode:</strong></div>
                                    <div>
                                        <span class="badge bg-info">
                                            <i class="bi bi-tag-fill me-1"></i>
                                            {{ user.annotation_mode|capitalize }}
                                        </span>
                                    </div>
                                </div>
                                {% endif %}
                                <!-- You can add more user statistics here in the future -->
                            </div>
                        </div>
                    </div>

                    <form method="post" action="{{ url_for('admin_routes.edit_user', username=user.username) }}">
                        <h5 class="section-title"><i class="bi bi-pencil-square me-2"></i>Edit User Details</h5>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                                    <input type="text" class="form-control" id="full_name" name="full_name" value="{{ user.full_name or '' }}" placeholder="Enter full name">
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ user.email or '' }}" placeholder="Enter email address">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-shield"></i></span>
                                    <select class="form-select" id="role" name="role">
                                        <option value="annotator" {% if user.role == 'annotator' %}selected{% endif %}>Annotator</option>
                                        <option value="auditor" {% if user.role == 'auditor' %}selected{% endif %}>Auditor</option>
                                        <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Administrator</option>
                                    </select>
                                </div>
                                <div class="form-text">Select the user's role in the system</div>
                            </div>

                            {% if user.role == 'annotator' %}
                            <div class="col-md-6 mb-3 annotator-options">
                                <label for="annotation_mode" class="form-label">Annotation Mode</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-tag"></i></span>
                                    <select class="form-select" id="annotation_mode" name="annotation_mode">
                                        <option value="manual" {% if user.annotation_mode == 'manual' %}selected{% endif %}>Manual Labelling</option>
                                        <option value="verification" {% if user.annotation_mode == 'verification' %}selected{% endif %}>Label Verification</option>
                                        <option value="supervision" {% if user.annotation_mode == 'supervision' %}selected{% endif %}>Supervision</option>
                                    </select>
                                </div>
                                <div class="form-text">Select the annotation mode for this user</div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- User status is now managed through the suspend/unsuspend actions in the user list -->

                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('admin_routes.manage_users') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i> Back to Users
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/edit_user.js') }}"></script>
{% endblock %}