{% extends "admin/admin_base.html" %}

{% block title %}Flush Database and Cache - DADP Data Annotation Platform{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">⚠️ Flush Database and Cache</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h5 class="alert-heading">Warning: This action cannot be undone!</h5>
                        <p>You are about to delete all records from the following tables:</p>
                        <ul>
                            <li>Manual Processing Records (image_processed_manual)</li>
                            <li>Verification Records (image_processed_verification)</li>
                            <li>Audit History (audit_history)</li>
                            <li>Admin Instructions (admin_instructions)</li>
                            <li>Dataset (dataset)</li>
                            <li>Supervision (Supervision)</li>
                            <li>SQLite Sequence (sqlite_sequence)</li>
                        </ul>
                        <p>Additionally, the Redis cache database will be completely flushed.</p>
                        <hr>
                        <p class="mb-0">All data in these tables and the cache will be permanently removed. This action cannot be reversed.</p>
                    </div>

                    <form method="post" action="{{ url_for('admin_routes.admin_flush_db') }}">
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{{ url_for('admin_routes.manage_users') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-danger">Confirm Database and Cache Flush</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}