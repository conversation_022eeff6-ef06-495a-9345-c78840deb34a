{% extends "admin/admin_base.html" %}

{% block title %}Manage Users{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/manage_users.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <!-- Remove the old page header and replace with new clean heading style -->
    <div class="clean-header text-center mb-5">
        <h1 class="main-title">User Management</h1>
    </div>

    <!-- Role Cards -->
    <div class="role-cards mb-4">
        <!-- Admin Card -->
        <div class="role-card admin" data-role="admin">
            <div class="role-icon">
                <i class="bi bi-shield-lock fs-4"></i>
            </div>
            <h3>Administrators</h3>
            <div class="role-count">{{ users|selectattr('role', 'equalto', 'admin')|list|length }}</div>
            <p class="text-muted mb-0 small">System administrators</p>
        </div>

        <!-- Annotator Card -->
        <div class="role-card annotator" data-role="annotator">
            <div class="role-icon">
                <i class="bi bi-person-badge fs-4"></i>
            </div>
            <h3>Annotators</h3>
            <div class="role-count">{{ users|selectattr('role', 'equalto', 'annotator')|list|length }}</div>
            <p class="text-muted mb-0 small">Data annotation/verification</p>
        </div>

        <!-- Auditor Card -->
        <div class="role-card auditor" data-role="auditor">
            <div class="role-icon">
                <i class="bi bi-eye fs-4"></i>
            </div>
            <h3>Auditors</h3>
            <div class="role-count">{{ users|selectattr('role', 'equalto', 'auditor')|list|length }}</div>
            <p class="text-muted mb-0 small">Quality control users</p>
        </div>
    </div>

    <!-- Users Table Card -->
    <div class="card users-card">
        <div class="card-header users-header">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0"><i class="bi bi-table me-2"></i>Registered Users</h5>
                    <span class="badge bg-primary-soft ms-3 rounded-pill">{{ users|length }} Total</span>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('admin_routes.admin_flush_db') }}" class="btn btn-flush-db btn-sm">
                        <i class="bi bi-database-x me-1"></i> Flush DB
                    </a>
                    <a href="{{ url_for('auth_routes.user_register') }}" class="btn btn-add-user btn-sm">
                        <i class="bi bi-person-plus me-1"></i> Add New User
                    </a>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Search and Filters -->
            <div class="search-container mb-3">
                <div class="row g-2 align-items-center">
                    <div class="col-md-5 col-lg-4">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" id="userSearchInput" class="form-control border-start-0 ps-0" placeholder="Search by username, name, email, role...">
                            <button class="btn btn-outline-secondary border-start-0 d-none" type="button" id="clearSearchBtn">
                                <i class="bi bi-x-lg"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-auto ms-md-auto">
                         <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary active" data-filter-status="all">
                                <i class="bi bi-filter me-1"></i> All Status
                            </button>
                            <button type="button" class="btn btn-outline-success" data-filter-status="active">
                                <i class="bi bi-check-circle me-1"></i> Active
                            </button>
                            <button type="button" class="btn btn-outline-danger" data-filter-status="suspended">
                                <i class="bi bi-pause-circle me-1"></i> Suspended
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="table-responsive">
                <table class="table table-hover table-sm" id="usersTable">
                    <thead class="table-light">
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Role</th>
                            <th>Email</th>
                            <th>Mode</th>
                            <th>Last Login</th>
                            <th>Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr class="user-row align-middle" data-role="{{ user.role }}" data-status="{{ 'active' if user.is_active else 'suspended' }}">
                            <td class="user-username fw-medium">{{ user.username }}</td>
                            <td class="user-fullname">{{ user.full_name or '-' }}</td>
                            <td class="user-role">
                                <span class="badge rounded-pill {% if user.role == 'admin' %}bg-danger-soft text-danger{% elif user.role == 'annotator' %}bg-primary-soft text-primary{% else %}bg-warning-soft text-warning{% endif %}">
                                    <i class="bi {% if user.role == 'admin' %}bi-shield-lock{% elif user.role == 'annotator' %}bi-person-badge{% else %}bi-eye{% endif %} me-1"></i>{{ user.role|capitalize }}
                                </span>
                            </td>
                            <td class="user-email">{{ user.email or '-' }}</td>
                            <td>
                                {% if user.role == 'annotator' %}
                                    {% if user.annotation_mode == 'supervision' %}
                                    <span class="badge rounded-pill bg-warning-soft text-warning"><i class="bi bi-eye me-1"></i>Supervision</span>
                                    {% elif user.verification_mode or user.annotation_mode == 'verification' %}
                                    <span class="badge rounded-pill bg-info-soft text-info"><i class="bi bi-check2-square me-1"></i>Verification</span>
                                    {% else %}
                                    <span class="badge rounded-pill bg-secondary-soft text-secondary"><i class="bi bi-pencil-square me-1"></i>Manual</span>
                                    {% endif %}
                                {% else %}
                                <span class="small text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="user-last-login">{% if user.last_login %}<span class="badge bg-light text-dark">{{ user.last_login|format_datetime }}</span>{% else %}<span class="badge bg-secondary-soft">Never</span>{% endif %}</td>
                            <td>
                                <span class="badge rounded-pill {% if user.is_active %}bg-success-soft text-success{% else %}bg-danger-soft text-danger{% endif %}">
                                    {{ 'Active' if user.is_active else 'Suspended' }}
                                </span>
                            </td>
                            <td class="text-end">
                                <div class="action-buttons">
                                    <a href="{{ url_for('admin_routes.edit_user', username=user.username) }}"
                                       class="btn btn-outline-primary" title="Edit User">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    {% if user.username != session.username %}
                                        {% if user.is_active %}
                                        <button type="button" class="btn btn-outline-danger suspend-btn" title="Suspend User"
                                                data-bs-toggle="modal" data-bs-target="#suspendModal-{{ user.username }}">
                                            <i class="bi bi-pause-circle"></i>
                                        </button>
                                        {% else %}
                                        <form action="{{ url_for('admin_routes.suspend_user', username=user.username) }}" method="post" class="d-inline">
                                            <input type="hidden" name="action" value="unsuspend">
                                            <button type="submit" class="btn btn-outline-success" title="Reactivate User">
                                                <i class="bi bi-play-circle"></i>
                                            </button>
                                        </form>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div> {# End table-responsive #}

            <!-- Loading Overlay -->
            <div class="loading-overlay d-none">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>

            <!-- Empty State -->
            {% if not users %}
            <div class="text-center p-4">
                 <i class="bi bi-people fs-1 text-muted"></i>
                <p class="mt-2 mb-3">No registered users found.</p>
                <a href="{{ url_for('auth_routes.user_register') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus me-1"></i> Add First User
                </a>
            </div>
            {% endif %}

            <!-- No Results Message -->
            <div id="noResultsMessage" class="text-center p-4 d-none">
                 <i class="bi bi-search fs-1 text-muted"></i>
                <p class="mt-2 mb-0">No users match your search criteria.</p>
            </div>
        </div> {# End card-body #}
    </div> {# End card #}
</div> {# End container #}

<!-- Suspend Confirmation Modals -->
{% for user in users %}
    {% if user.is_active and user.username != session.username %}
    <div class="modal fade" id="suspendModal-{{ user.username }}" tabindex="-1"
         aria-labelledby="suspendModalLabel-{{ user.username }}" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-header" style="background: linear-gradient(135deg, #dc3545, #e74c3c); color: white; border: none;">
                    <h5 class="modal-title fw-bold" id="suspendModalLabel-{{ user.username }}">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>Confirm Suspension
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="suspension-icon me-3 p-3 rounded-circle bg-danger bg-opacity-10 text-danger">
                            <i class="bi bi-person-x-fill fs-3"></i>
                        </div>
                        <div>
                            <h5 class="mb-1">Suspend User: <span class="fw-bold text-danger">{{ user.username }}</span></h5>
                            <p class="mb-0 text-muted">{{ user.full_name or 'No name provided' }}</p>
                        </div>
                    </div>
                    <div class="alert alert-warning p-3 d-flex align-items-center">
                        <i class="bi bi-info-circle-fill me-2 text-warning fs-5"></i>
                        <p class="mb-0">The user will no longer be able to log in, but their account information will be preserved.</p>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0 d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary px-4 py-2 fw-medium" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <form action="{{ url_for('admin_routes.suspend_user', username=user.username) }}" method="post">
                        <button type="submit" class="btn btn-danger px-4 py-2 fw-bold suspension-confirm-btn">
                            <i class="bi bi-pause-circle-fill me-1"></i>Suspend User
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
{% endfor %}

{% endblock %}


{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/manage_users.js') }}"></script>
{% endblock %}