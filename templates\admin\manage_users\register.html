{% extends "admin/admin_base.html" %}

{% block title %}Register New User{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common/user_register.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8 col-md-10">
        <div class="card shadow-sm">
             <div class="card-header">
                 <h4 class="mb-0"><i class="bi bi-person-plus-fill me-2"></i>Register New User</h4>
             </div>
            <div class="card-body p-4">
                <form method="post" action="{{ url_for('auth_routes.user_register') }}" id="adminRegisterForm" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-6 form-floating">
                            <input type="text" class="form-control" id="username" name="username" placeholder="Username" required pattern="[a-zA-Z0-9_]+">
                            <label for="username">Username</label>
                            <div class="form-text">Letters, numbers, & underscores only.</div>
                            <div class="invalid-feedback">Invalid username format.</div>
                        </div>
                        <div class="col-md-6 form-floating">
                             <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Full Name">
                            <label for="full_name">Full Name</label>
                        </div>

                         <div class="col-md-6 form-floating">
                            <input type="password" class="form-control" id="password" name="password" placeholder="Password" required minlength="6">
                            <label for="password">Password</label>
                            <div class="form-text">Minimum 6 characters.</div>
                            <div class="invalid-feedback">Password must be at least 6 characters.</div>
                        </div>
                         <div class="col-md-6 form-floating">
                             <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                            <label for="confirm_password">Confirm Password</label>
                            <div class="invalid-feedback">Passwords do not match.</div>
                        </div>

                        <div class="col-md-6 form-floating">
                            <input type="email" class="form-control" id="email" name="email" placeholder="Email Address">
                            <label for="email">Email Address</label>
                             <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>

                         <div class="col-md-6 form-floating">
                            <select class="form-select" id="role" name="role">
                                <option value="annotator" selected>Annotator</option>
                                <option value="auditor">Auditor</option>
                                <option value="admin">Administrator</option>
                            </select>
                            <label for="role">Assign Role</label>
                        </div>

                        <div class="col-12 annotator-options" style="display: none; border-left: 3px solid var(--bs-primary); padding-left: 1rem; margin-top: 1rem;">
                             <p class="mb-2 fw-bold text-primary"><i class="bi bi-person-badge me-1"></i>Annotator Settings</p>
                             <div class="row g-3">
                                <div class="col-md-6 form-floating">
                                    <select class="form-select" id="annotation_mode" name="annotation_mode" style="width: 100%;">
                                        <option value="manual" selected>Manual Labelling</option>
                                        <option value="verification">Label Verification</option>
                                        <option value="supervision">Supervision</option>
                                    </select>
                                    <label for="annotation_mode">Default Annotation Mode</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end gap-2 mt-4 pt-3 border-top">
                        <a href="{{ url_for('admin_routes.manage_users') }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" class="btn btn-primary"><i class="bi bi-person-plus-fill me-1"></i> Register User</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    <script src="{{ url_for('static', filename='js/common/register.js') }}"></script>
{% endblock %}
