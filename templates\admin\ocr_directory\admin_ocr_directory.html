{% extends "admin/admin_base.html" %}

{% block title %}OCR Directory Management{% endblock %}

{% block extra_css %}
    {# Add specific CSS if needed, otherwise rely on admin_layout.css and bootstrap #}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin/dashboard.css') }}"> {# Reuse existing styles if applicable #}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin/ocr_directory.css') }}">
{% endblock %}

{% block content %}
<!-- Updated title section matching Data Sources style -->
<div class="page-title-section">
    <h1 class="ocr-title">OCR Directory Management</h1>
</div>

<!-- Dataset Selection Section -->
<div class="dataset-selection-section">
    <h2 class="section-title mb-4"><i class="bi bi-database me-2"></i>Dataset Selection</h2>
    
    <!-- Mode Selection Tabs -->
    <ul class="nav nav-tabs mb-4" id="modeSelectTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="manual-tab" data-bs-toggle="tab" data-bs-target="#manual-tab-pane" type="button" role="tab" aria-controls="manual-tab-pane" aria-selected="true" onclick="loadDatasets('manual')">
                <i class="bi bi-pencil-square me-1"></i>Manual Labelling
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="verification-tab" data-bs-toggle="tab" data-bs-target="#verification-tab-pane" type="button" role="tab" aria-controls="verification-tab-pane" aria-selected="false" onclick="loadDatasets('verification')">
                <i class="bi bi-check2-square me-1"></i>Verification
            </button>
        </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="modeSelectTabContent">
        <!-- Manual Mode Tab Pane -->
        <div class="tab-pane fade show active" id="manual-tab-pane" role="tabpanel" aria-labelledby="manual-tab" tabindex="0">
            <div class="dataset-selection-container">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <label for="manualDatasetSelect" class="form-label">Select Dataset for Manual Labelling:</label>
                        <select class="form-select" id="manualDatasetSelect">
                            <option value="">Loading datasets...</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" id="selectManualDatasetBtn" class="btn btn-primary" onclick="selectDataset('manual')" disabled>
                            <i class="bi bi-check-circle-fill me-1"></i> Assign Dataset
                        </button>
                    </div>
                </div>
                <div id="manualDatasetInfo" class="dataset-info">
                    <p class="text-muted">Please select a dataset from the dropdown to see its details.</p>
                </div>
            </div>
        </div>
        
        <!-- Verification Mode Tab Pane -->
        <div class="tab-pane fade" id="verification-tab-pane" role="tabpanel" aria-labelledby="verification-tab" tabindex="0">
            <div class="dataset-selection-container">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <label for="verificationDatasetSelect" class="form-label">Select Dataset for Verification:</label>
                        <select class="form-select" id="verificationDatasetSelect">
                            <option value="">Loading datasets...</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" id="selectVerificationDatasetBtn" class="btn btn-primary" onclick="selectDataset('verification')" disabled>
                            <i class="bi bi-check-circle-fill me-1"></i> Assign Dataset
                        </button>
                    </div>
                </div>
                <div id="verificationDatasetInfo" class="dataset-info">
                    <p class="text-muted">Please select a dataset from the dropdown to see its details.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Existing Mode Cards for creating new datasets from NAS browser -->
<div class="section-divider my-5"></div>
<h2 class="section-title mb-4"><i class="bi bi-folder-plus me-2"></i>Create New Dataset</h2>

<div class="mode-cards">
    <!-- Manual Mode Card -->
    <div class="mode-card">
        <div class="mode-card-content">
            <h5><i class="bi bi-pencil-square"></i>Manual Labelling Mode</h5>
            <div class="mb-3">
                <label class="form-label">Image Folder</label>
                <div class="input-group">
                    <div class="form-control nas-path-display" id="manualFolderPath">
                        {% if manual_folder %}{{ manual_folder }}{% else %}No folder selected{% endif %}
                    </div>
                    <button type="button" class="btn btn-primary" onclick="openNasBrowser('manual-folder')">
                        <i class="bi bi-folder2-open me-1"></i> Browse
                        </button>
                    </div>
                </div>
        </div>

        <div class="mode-card-actions">

                <button type="button" id="setManualFolderBtn" class="btn btn-success" onclick="setManualFolder()" {% if not manual_folder %}disabled{% endif %}>
                    <i class="bi bi-check-circle-fill me-1"></i> Set Manual Source
                </button>
            </div>
        </div>

    <!-- Verification Mode Card -->
    <div class="mode-card">
        <h5><i class="bi bi-check2-square"></i>Verification Mode</h5>
            <div class="mb-3">
                <label class="form-label">Image Folder</label>
                <div class="input-group">
                    <div class="form-control nas-path-display" id="verificationImageFolderPath">
                        {% if verification_image_folder %}{{ verification_image_folder }}{% else %}No folder selected{% endif %}
                    </div>
                    <button type="button" class="btn btn-primary" onclick="openNasBrowser('verification-image-folder')">
                        <i class="bi bi-folder2-open me-1"></i> Browse
                    </button>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label">Label File (JSON)</label>
                <div class="input-group">
                    <div class="form-control nas-path-display" id="verificationLabelFilePath">
                        {% if verification_label_folder %}{{ verification_label_folder }}{% else %}No file selected{% endif %}
                    </div>
                    <button type="button" class="btn btn-primary" onclick="openNasBrowser('verification-label-file')">
                        <i class="bi bi-file-earmark-text me-1"></i> Browse
                        </button>
                    </div>
                </div>
            <div class="d-flex justify-content-between align-items-center">

            <button type="button" id="setVerificationFoldersBtn" class="btn btn-success" onclick="setVerificationFolders()" {% if not verification_image_folder or not verification_label_folder %}disabled{% endif %}>
                    <i class="bi bi-check-circle-fill me-1"></i> Set Verification Sources
                </button>
        </div>
            </div>
        </div>



<!-- NAS Browser Modal -->
<div class="modal fade nas-browser-modal" id="nasBrowserModal" tabindex="-1" aria-labelledby="nasBrowserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="nasBrowserModalLabel">NAS File Browser</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Selection mode indicator -->
                <div class="alert alert-info" id="selectionModeInfo">
                    Browse to select a folder
                </div>

                <!-- Breadcrumb navigation -->
                <nav aria-label="breadcrumb" class="nas-breadcrumb">
                    <ol class="breadcrumb mb-0" id="nasBreadcrumb">
                        <li class="breadcrumb-item"><a href="#" onclick="browseNasDirectory('/')">Root</a></li>
                    </ol>
                </nav>

                <!-- Directory contents -->
                <div class="nas-browser-container" id="nasBrowserContainer">
                    <div class="nas-loading" id="nasLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div id="nasDirectoryContents"></div>
                </div>

                <!-- Current selection display -->
                <div class="mt-3">
                    <label class="form-label">Selected Path:</label>
                    <div class="form-control" id="currentSelectionPath" style="white-space: normal; word-break: break-all; overflow-y: auto; max-height: 100px;">/</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="selectPathButton" onclick="selectCurrentPath()">Select</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}


{% block extra_js %}
<!-- Store data in data attributes instead of directly in JavaScript -->
<div id="initialDataContainer"
     data-manual-folder="{{ manual_folder|tojson|safe if manual_folder else '' }}"
     data-verification-image-folder="{{ verification_image_folder|tojson|safe if verification_image_folder else '' }}"
     data-verification-label-folder="{{ verification_label_folder|tojson|safe if verification_label_folder else '' }}"
     style="display: none;">
</div>

<script src="{{ url_for('static', filename='js/admin/admin_ocr_directory.js') }}"></script>

<!-- Add JavaScript for Dataset Selection Section -->
<script>
// Global variables for dataset dropdowns
let manualDatasets = [];
let verificationDatasets = [];
let currentManualDatasetId = null;
let currentVerificationDatasetId = null;

// Load datasets for the selected mode
function loadDatasets(mode) {
    const dropdownId = mode === 'manual' ? 'manualDatasetSelect' : 'verificationDatasetSelect';
    const infoId = mode === 'manual' ? 'manualDatasetInfo' : 'verificationDatasetInfo';
    const btnId = mode === 'manual' ? 'selectManualDatasetBtn' : 'selectVerificationDatasetBtn';
    
    // Set loading state
    document.getElementById(dropdownId).innerHTML = '<option value="">Loading datasets...</option>';
    document.getElementById(infoId).innerHTML = '<p class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></p>';
    document.getElementById(btnId).disabled = true;
    
    // Fetch datasets
    fetch(`/admin/get-datasets?mode=${mode}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const datasets = data.datasets || [];
                const currentDatasetId = data.current_dataset_id;
                
                // Store datasets in global variable
                if (mode === 'manual') {
                    manualDatasets = datasets;
                    currentManualDatasetId = currentDatasetId;
                } else {
                    verificationDatasets = datasets;
                    currentVerificationDatasetId = currentDatasetId;
                }
                
                // Populate dropdown
                populateDatasetDropdown(mode, datasets, currentDatasetId);
                
                // Show initial info if current dataset is selected
                if (currentDatasetId) {
                    const selectedDataset = datasets.find(d => d.id === currentDatasetId);
                    if (selectedDataset) {
                        // Get folder path from dropdown option
                        const dropdown = document.getElementById(dropdownId);
                        for (let i = 0; i < dropdown.options.length; i++) {
                            if (parseInt(dropdown.options[i].value) === currentDatasetId) {
                                const folderPath = dropdown.options[i].getAttribute('data-folder-path') || '';
                                selectedDataset.folder_path = folderPath;
                                break;
                            }
                        }
                        
                        showDatasetInfo(mode, selectedDataset);
                    } else {
                        document.getElementById(infoId).innerHTML = '<p class="text-muted">Please select a dataset from the dropdown to see its details.</p>';
                    }
                } else {
                    document.getElementById(infoId).innerHTML = '<p class="text-muted">Please select a dataset from the dropdown to see its details.</p>';
                }
            } else {
                document.getElementById(dropdownId).innerHTML = '<option value="">No datasets available</option>';
                document.getElementById(infoId).innerHTML = `<p class="text-danger">Error loading datasets: ${data.message}</p>`;
            }
        })
        .catch(error => {
            console.error('Error fetching datasets:', error);
            document.getElementById(dropdownId).innerHTML = '<option value="">Error loading datasets</option>';
            document.getElementById(infoId).innerHTML = `<p class="text-danger">Error: ${error.message}</p>`;
        });
}

// Populate dataset dropdown
function populateDatasetDropdown(mode, datasets, currentDatasetId) {
    const dropdownId = mode === 'manual' ? 'manualDatasetSelect' : 'verificationDatasetSelect';
    const dropdown = document.getElementById(dropdownId);
    
    let options = '<option value="">-- Select a dataset --</option>';
    
    if (datasets.length === 0) {
        dropdown.innerHTML = '<option value="">No datasets available</option>';
        return;
    }
    
    datasets.forEach(dataset => {
        const isSelected = dataset.id === currentDatasetId;
        const completionStatus = getCompletionStatus(dataset);
        options += `<option value="${dataset.id}" ${isSelected ? 'selected' : ''} 
                    data-progress="${dataset.progress_percentage}" 
                    data-status="${completionStatus}"
                    data-folder-path="${dataset.folder_path || ''}">
                    ${dataset.name} (${dataset.progress_percentage}% complete${completionStatus === 'completed' ? ' - COMPLETED' : ''})
                   </option>`;
    });
    
    dropdown.innerHTML = options;
    
    // Add change event to show dataset info
    dropdown.onchange = function() {
        const selectedId = parseInt(this.value);
        const selectedOption = this.options[this.selectedIndex];
        const folderPath = selectedOption.getAttribute('data-folder-path') || '';
        
        // Find dataset and add folder path
        const selectedDataset = datasets.find(d => d.id === selectedId);
        
        if (selectedDataset) {
            // Ensure folder_path is included in the dataset object
            selectedDataset.folder_path = folderPath;
            
            showDatasetInfo(mode, selectedDataset);
            document.getElementById(mode === 'manual' ? 'selectManualDatasetBtn' : 'selectVerificationDatasetBtn').disabled = false;
        } else {
            document.getElementById(mode === 'manual' ? 'manualDatasetInfo' : 'verificationDatasetInfo').innerHTML = 
                '<p class="text-muted">Please select a dataset from the dropdown to see its details.</p>';
            document.getElementById(mode === 'manual' ? 'selectManualDatasetBtn' : 'selectVerificationDatasetBtn').disabled = true;
        }
    };
}

// Get completion status text
function getCompletionStatus(dataset) {
    if (dataset.total_batches === 0) return 'empty';
    if (dataset.completed_batches >= dataset.total_batches) return 'completed';
    return 'in-progress';
}

// Show dataset info
function showDatasetInfo(mode, dataset) {
    const infoId = mode === 'manual' ? 'manualDatasetInfo' : 'verificationDatasetInfo';
    const infoContainer = document.getElementById(infoId);
    
    const status = getCompletionStatus(dataset);
    const statusClass = status === 'completed' ? 'text-success' : (status === 'in-progress' ? 'text-primary' : 'text-muted');
    const statusIcon = status === 'completed' ? 'bi-check-circle-fill' : (status === 'in-progress' ? 'bi-clock-history' : 'bi-dash-circle');
    
    let infoHtml = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">${dataset.name}</h5>
                <span class="${statusClass}"><i class="bi ${statusIcon} me-1"></i>${status === 'completed' ? 'Completed' : (status === 'in-progress' ? 'In Progress' : 'Empty')}</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Total Batches:</strong> ${dataset.total_batches}</p>
                        <p><strong>Completed Batches:</strong> ${dataset.completed_batches}</p>
                    </div>
                    <div class="col-md-6">
                        ${mode === 'verification' ? `<p><strong>Label File:</strong> ${dataset.label_file || 'None'}</p>` : ''}
                        <p><strong>Completion:</strong> ${dataset.progress_percentage}%</p>
                    </div>
                </div>
                <div class="progress mb-3">
                    <div class="progress-bar ${status === 'completed' ? 'bg-success' : 'bg-primary'}" role="progressbar" style="width: ${dataset.progress_percentage}%" 
                         aria-valuenow="${dataset.progress_percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <div class="alert ${status === 'completed' ? 'alert-success' : (status === 'in-progress' ? 'alert-info' : 'alert-secondary')} mb-0">
                    ${status === 'completed' ? 
                      '<i class="bi bi-exclamation-triangle-fill me-2"></i>All batches in this dataset have been completed. You may select a different dataset.' : 
                      (status === 'in-progress' ? 
                       '<i class="bi bi-info-circle-fill me-2"></i>This dataset is partially processed and ready for annotators to continue working on.' :
                       '<i class="bi bi-exclamation-circle-fill me-2"></i>This dataset has no batches available.')}
                </div>
                <div class="mt-3 d-flex justify-content-end">
                    <a href="/browser?folder=${encodeURIComponent(dataset.folder_path || '')}" class="btn btn-outline-primary">
                        <i class="bi bi-images me-1"></i> Browse Images
                    </a>
                </div>
            </div>
        </div>
    `;
    
    infoContainer.innerHTML = infoHtml;
}

// Select a dataset for annotation or verification
function selectDataset(mode) {
    const dropdownId = mode === 'manual' ? 'manualDatasetSelect' : 'verificationDatasetSelect';
    const btnId = mode === 'manual' ? 'selectManualDatasetBtn' : 'selectVerificationDatasetBtn';
    const dropdown = document.getElementById(dropdownId);
    const button = document.getElementById(btnId);
    
    const datasetId = dropdown.value;
    if (!datasetId) {
        alert('Please select a dataset first.');
        return;
    }
    
    // Save original button text
    const originalText = button.innerHTML;
    
    // Update button to show loading state
    button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Selecting...';
    button.disabled = true;
    
    // Send request to select dataset
    const formData = new FormData();
    formData.append('dataset_id', datasetId);
    formData.append('mode', mode);
    
    fetch('/admin/select-dataset', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Success - reload the page
            alert(data.message);
            window.location.reload();
        } else {
            // Error
            alert(`Error: ${data.message}`);
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error selecting dataset:', error);
        alert(`Error: ${error.message}`);
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Load datasets when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize with manual mode selected
    loadDatasets('manual');
});
</script>
{% endblock %}
