{% extends "admin/admin_base.html" %}

{% block title %}Browser{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin/browser.css') }}">
{% endblock %}

{% block content %}
<div class="browser-container">
    <!-- Folder navigation -->
    <div class="folder-nav">
        <div class="d-flex justify-content-between align-items-center">
            <div class="folder-actions">
                {% if parent_folder %}
                <a href="{{ url_for('admin_routes.browser', folder=parent_folder|urlencode) }}" 
                   class="btn btn-outline-primary btn-sm back-btn">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
                {% endif %}
                
                <a href="{{ url_for('admin_routes.browser', folder=folder|urlencode, refresh=1) }}" 
                   class="btn btn-outline-primary btn-sm ms-2">
                    <i class="bi bi-arrow-clockwise"></i> Refresh
                </a>
            </div>

            {% if available_folders %}
            <div class="folder-dropdown">
                <button class="btn btn-primary btn-sm dropdown-toggle" 
                        type="button" 
                        id="folderDropdown" 
                        data-bs-toggle="dropdown">
                    <i class="bi bi-folder2"></i> Available Folders
                </button>
                <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="folderDropdown">
                    {% for avail_folder in available_folders %}
                    <li>
                        <a class="dropdown-item" 
                           href="{{ url_for('admin_routes.browser', folder=avail_folder|urlencode) }}">
                            <i class="bi bi-folder2"></i> {{ avail_folder }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Current folder name -->
    <div class="folder-title text-center mb-4">
        <h5 class="mb-0">
            <i class="bi bi-folder2"></i>
            {% if folder %}
                {% set folder_parts = folder.split('/') %}
                {% set folder_name = folder_parts[-1] if folder_parts[-1] else folder_parts[-2] if folder_parts|length > 1 else folder %}
                {{ folder_name }}
            {% else %}
                Root Directory
            {% endif %}
        </h5>
    </div>

    <!-- Images grid -->
    <div class="image-grid-container">
        {% if images %}
        <div class="image-grid">
            {% for image in images %}
            <div class="image-card" 
                 data-image-path="{{ url_for('annotator_routes.get_image', image_path=image.path) }}"
                 data-image-name="{{ image.name }}"
                 role="button">
                <div class="image-preview">
                    <img src="{{ url_for('annotator_routes.get_image', image_path=image.path) }}"
                         alt="{{ image.name }}"
                         loading="lazy">
                    <div class="image-overlay">
                        <span class="btn btn-light btn-sm">
                            <i class="bi bi-zoom-in"></i> View
                        </span>
                    </div>
                </div>
                <div class="image-info">
                    <span class="image-name" title="{{ image.name }}">{{ image.name }}</span>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-images">
            <div class="alert alert-info d-flex align-items-center">
                <i class="bi bi-info-circle-fill me-2"></i>
                <span>No images found in this folder.</span>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if total_pages > 1 %}
    <div class="pagination-container">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item {{ 'disabled' if page == 1 else '' }}">
                    <a class="page-link" 
                       href="{{ url_for('admin_routes.browser', folder=folder|urlencode, page=page-1) }}"
                       aria-label="Previous">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>

                {% for p in range(1, total_pages + 1) %}
                    {% if p == page %}
                        <li class="page-item active">
                            <span class="page-link">{{ p }}</span>
                        </li>
                    {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                        <li class="page-item">
                            <a class="page-link" 
                               href="{{ url_for('admin_routes.browser', folder=folder|urlencode, page=p) }}">
                                {{ p }}
                            </a>
                        </li>
                    {% elif p == 4 and page > 4 or p == total_pages - 3 and page < total_pages - 3 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}

                <li class="page-item {{ 'disabled' if page == total_pages else '' }}">
                    <a class="page-link" 
                       href="{{ url_for('admin_routes.browser', folder=folder|urlencode, page=page+1) }}"
                       aria-label="Next">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
        <div class="text-center text-muted small mt-2">
            Showing page {{ page }} of {{ total_pages }} ({{ total_images }} total images)
        </div>
    </div>
    {% endif %}

    <!-- Image Modal -->
    <div class="modal fade" 
         id="imageModal" 
         tabindex="-1" 
         aria-labelledby="imageModalLabel" 
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel"></h5>
                    <button type="button" 
                            class="btn-close" 
                            data-bs-dismiss="modal" 
                            aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="image-viewer">
                        <button class="nav-btn prev-btn" id="prevImageBtn" type="button">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <div class="image-container">
                            <img id="modalImage" src="" alt="">
                        </div>
                        <button class="nav-btn next-btn" id="nextImageBtn" type="button">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/browser.js') }}"></script>
{% endblock %}
