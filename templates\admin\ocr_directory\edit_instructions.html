{% extends "admin/admin_base.html" %}

{% block title %}Edit Dataset Instructions{% endblock %}

{% block content %}
<div class="container py-4">
  <h2 class="mb-4"><i class="bi bi-pencil me-2"></i>Edit Dataset Instructions</h2>

  <div class="card mb-4">
    <div class="card-body">
      <form id="instructionForm" action="{{ url_for('admin_routes.edit_instructions') }}" method="post">
        <input type="hidden" name="type" value="dataset">
        
        <!-- Mode Selection Dropdown -->
        <div class="mb-4">
          <label for="modeSelect" class="form-label">Select Annotation Mode</label>
          <select class="form-select" id="modeSelect" name="mode">
            <option value="">-- Select Mode --</option>
            <option value="manual" {% if selected_mode == 'manual' %}selected{% endif %}>Manual Labeling</option>
            <option value="verification" {% if selected_mode == 'verification' %}selected{% endif %}>Verification Mode</option>
            <option value="supervision" {% if selected_mode == 'supervision' %}selected{% endif %}>Supervision Mode</option>
          </select>
        </div>
        
        <!-- Dataset Selection - only shown for Manual and Verification modes -->
        <div class="mb-4" id="datasetSelectionContainer" {% if not selected_mode or selected_mode == 'supervision' %}style="display: none;"{% endif %}>
          <label for="datasetSelect" class="form-label">Select Dataset</label>
          <select class="form-select" id="datasetSelect" name="dataset_id" {% if not datasets %}disabled{% endif %}>
            <option value="">-- Select Dataset --</option>
            {% if selected_mode == 'manual' %}
              {% for dataset in manual_datasets %}
                <option value="{{ dataset.id }}" {% if selected_dataset_id|string == dataset.id|string %}selected{% endif %}>
                  {{ dataset.dataset_name }}
                </option>
              {% endfor %}
            {% elif selected_mode == 'verification' %}
              {% for dataset in verification_datasets %}
                <option value="{{ dataset.id }}" {% if selected_dataset_id|string == dataset.id|string %}selected{% endif %}>
                  {{ dataset.dataset_name }}
                </option>
              {% endfor %}
            {% endif %}
          </select>
          {% if selected_mode and selected_mode != 'supervision' and not datasets %}
            <div class="form-text text-warning">No datasets available for this mode.</div>
          {% endif %}
        </div>
        
        <!-- Instructions for Supervision Mode -->
        <div class="mb-4" id="supervisionInstructionsContainer" {% if selected_mode != 'supervision' %}style="display: none;"{% endif %}>
          <label for="supervisionInstructionsText" class="form-label">Supervision Mode Instructions</label>
          <textarea
            class="form-control"
            id="supervisionInstructionsText"
            name="supervision_instructions"
            rows="10"
            placeholder="Enter instructions for supervision mode..."
          >{{ supervision_instructions }}</textarea>
          <div class="form-text">
            These instructions will be shown to annotators when working in supervision mode.
          </div>
        </div>
        
        <!-- Instructions Text Area for Dataset-specific instructions -->
        <div class="mb-4" id="datasetInstructionsContainer" {% if not selected_dataset_id %}style="display: none;"{% endif %}>
          <label for="datasetInstructionsText" class="form-label">Dataset Instructions</label>
          <textarea
            class="form-control"
            id="datasetInstructionsText"
            name="dataset_instructions"
            rows="10"
            placeholder="Enter dataset-specific instructions..."
          >{{ dataset_instructions }}</textarea>
          <div class="form-text">
            These instructions will be shown to annotators when working with this dataset.
          </div>
        </div>
        
        <!-- Submit Button -->
        <div class="text-end" id="submitContainer" {% if not selected_dataset_id and selected_mode != 'supervision' %}style="display: none;"{% endif %}>
          <button type="submit" class="btn btn-primary">Save Instructions</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
  <script src="{{ url_for('static', filename='js/admin/admin_layout.js') }}"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const modeSelect = document.getElementById('modeSelect');
      const datasetSelect = document.getElementById('datasetSelect');
      const datasetSelectionContainer = document.getElementById('datasetSelectionContainer');
      const datasetInstructionsContainer = document.getElementById('datasetInstructionsContainer');
      const supervisionInstructionsContainer = document.getElementById('supervisionInstructionsContainer');
      const submitContainer = document.getElementById('submitContainer');
      
      // For debugging
      console.log('Selected Mode:', '{{ selected_mode }}');
      console.log('Selected Dataset ID:', '{{ selected_dataset_id }}');
      
      // Mode selection change handler
      modeSelect.addEventListener('change', function() {
        if (this.value) {
          if (this.value === 'supervision') {
            // For supervision mode, hide dataset selection and show supervision instructions
            window.location.href = "{{ url_for('admin_routes.edit_instructions') }}?mode=supervision";
          } else {
            // For manual and verification modes, show dataset selection
            window.location.href = "{{ url_for('admin_routes.edit_instructions') }}?mode=" + this.value;
          }
        } else {
          // Hide everything if no mode selected
          datasetSelectionContainer.style.display = 'none';
          datasetInstructionsContainer.style.display = 'none';
          supervisionInstructionsContainer.style.display = 'none';
          submitContainer.style.display = 'none';
        }
      });
      
      // Dataset selection change handler
      datasetSelect.addEventListener('change', function() {
        console.log('Dataset selected:', this.value);
        
        if (this.value) {
          // Show the datasetInstructionsContainer and submitContainer right away
          // This prevents them from flashing before the page reloads
          datasetInstructionsContainer.style.display = 'block';
          submitContainer.style.display = 'block';
          
          // Navigate to the dataset edit page
          const selectedMode = modeSelect.value;
          const url = "{{ url_for('admin_routes.edit_instructions') }}?mode=" + selectedMode + "&dataset=" + this.value;
          console.log('Redirecting to:', url);
          
          // Use a slight delay before redirecting to ensure the UI updates first
          setTimeout(function() {
            window.location.href = url;
          }, 50);
        } else {
          // Hide instructions when no dataset is selected
          datasetInstructionsContainer.style.display = 'none';
          submitContainer.style.display = 'none';
        }
      });
      
      // If dataset is selected, ensure instructions are displayed
      if (datasetSelect.value) {
        datasetInstructionsContainer.style.display = 'block';
        submitContainer.style.display = 'block';
      }
    });
  </script>
{% endblock %}
