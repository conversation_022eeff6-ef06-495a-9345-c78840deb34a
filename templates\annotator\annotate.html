{% extends "base.html" %}

{% block title %}Annotate Images - DADP Data Annotation Platform{% endblock %}

{% block extra_css %}
<!-- Cropper.js CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/annotator/annotate.css') }}">
<!-- jQuery for DOM manipulation -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
{% endblock %}

{% block content %}
    <div class="container py-4">
        <div class="d-flex justify-content-center align-items-center mb-2">
            <h1 class="h3 mb-0">Image Annotation</h1>
            <div class="d-flex gap-2">
                {% if user.role == 'admin' %}
                <a href="{{ url_for('admin_routes.browser', folder=folder) }}" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-arrow-left"></i> Back to Browser
                </a>
                {% endif %}
            </div>
        </div>

        <!-- Annotation interface with task guidelines and annotation area -->
        <div class="annotation-layout">
            <!-- Left side: Task Guidelines -->
            <div class="guidelines-sidebar">
                <div class="card guidelines-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-list-task me-2"></i>Task Guidelines</h5>
                    </div>
                    <div class="card-body p-3">
                        {% if admin_instructions %}
                            <div class="instructions-content">{{ admin_instructions|nl2br }}</div>
                        {% else %}
                            <p class="text-muted fst-italic">No specific instructions have been provided for this dataset.</p>
                        {% endif %}
                        
                        <div class="best-practices mt-4">
                            <h6 class="fw-bold text-primary mb-2">Best Practices</h6>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle-fill text-success me-2"></i>Be consistent with your labeling approach</li>
                                <li><i class="bi bi-check-circle-fill text-success me-2"></i>Take breaks to avoid fatigue</li>
                                <li><i class="bi bi-check-circle-fill text-success me-2"></i>Use keyboard shortcuts for efficiency</li>
                                <li><i class="bi bi-check-circle-fill text-success me-2"></i>Refer to guidelines if unsure</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main annotation area -->
            <div class="annotation-container">
                <div class="annotation-main">
                    <div id="imageDisplay" class="image-display">
                        <span id="cropMode" class="crop-mode">
                            <i class="bi bi-crop me-1"></i> Crop Mode (Press Enter to confirm, Esc to cancel)
                        </span>
                        <span id="zoomIndicator" class="zoom-indicator">
                            <i class="bi bi-zoom-in me-1"></i> <span id="zoomPercentage">100%</span>
                        </span>
                        <span id="panHint" class="pan-hint">
                            <i class="bi bi-arrows-move me-1"></i> Drag to move image
                        </span>
                        <div class="image-container">
                            <img id="currentImage" src="" alt="Loading image...">
                        </div>
                        <span id="completedBadge" class="completed-badge" style="display: none;">
                            <i class="bi bi-check-circle me-1"></i> Labeled
                        </span>
                        <div class="zoom-controls">
                            <div class="zoom-btn" id="zoomOut" title="Zoom Out (-)">
                                <i class="bi bi-dash"></i>
                            </div>
                            <div class="zoom-level" id="zoomLevel">100%</div>
                            <div class="zoom-btn" id="zoomIn" title="Zoom In (+)">
                                <i class="bi bi-plus"></i>
                            </div>
                            <div class="zoom-btn" id="zoomReset" title="Reset Zoom">
                                <i class="bi bi-arrows-fullscreen"></i>
                            </div>
                        </div>
                        <!-- Add refresh button -->
                        <div class="position-absolute top-0 end-0 m-3" style="z-index: 5;">
                            <button
                              id="refreshImageBtn"
                              class="btn btn-sm btn-light"
                              title="Refresh Image (F5)"
                            >
                              <i class="bi bi-arrow-clockwise fs-4"></i>
                            </button>
                        </div>

                        <!-- Progress bar at the bottom of the display area -->
                        <div class="progress position-absolute bottom-0 start-0 end-0 m-0 rounded-0" style="height: 6px; z-index: 5;">
                            <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <div class="image-counter mb-2">
                        <span id="completedCounter">0</span> of <span id="totalCounter">{{ images|length }}</span> images labeled
                        (<span id="percentageCounter">0%</span>)
                    </div>

                    <div class="form-container">
                        {% if verification_mode %}
                        <div class="verification-mode-indicator">
                            <i class="bi bi-check-circle-fill me-1"></i> Label Verification Mode
                        </div>

                        <div class="verification-label" id="verificationLabelContainer">
                            <div class="label-text" id="currentLabel"></div>
                            <div class="label-actions">
                                <button class="btn btn-sm btn-verify" id="verifyLabelBtn" title="Verify Label (Enter)">
                                    <i class="bi bi-check-lg me-1"></i> Verify
                                </button>
                                <button class="btn btn-sm btn-edit" id="editLabelBtn" title="Edit Label">
                                    <i class="bi bi-pencil me-1"></i> Edit
                                </button>
                            </div>
                        </div>

                        <div class="mb-3" id="editLabelContainer" style="display: none;">
                            <label for="editLabelInput" class="form-label">Edit Label</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="editLabelInput" placeholder="Enter corrected label">
                                <button class="btn btn-primary" id="saveLabelBtn">
                                    <i class="bi bi-check-lg me-1"></i> Save
                                </button>
                                <button class="btn btn-secondary" id="cancelEditBtn">
                                    <i class="bi bi-x-lg me-1"></i> Cancel
                                </button>
                            </div>
                        </div>
                        {% else %}
                        <!-- Label input -->
                        <div class="mb-3">
                            <label for="labelInput" class="form-label">Label <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="labelInput" placeholder="Enter label for this image" required>
                                <button class="btn btn-primary-1" id="saveBtn" type="button" title="Save Label (Enter)">
                                    <i class="bi bi-check-lg me-1"></i> Save
                                </button>
                            </div>
                            <div class="form-text text-muted">Label is required before proceeding to the next image</div>
                        </div>
                        {% endif %}

                        <div class="image-navigation">
                            <button id="prevBtn" class="btn btn-outline-primary nav-btn">
                                <i class="bi bi-arrow-left"></i> Previous
                            </button>

                            <span id="imageCounter">Image 1 of {{ images|length }}</span>

                            <button id="nextBtn" class="btn btn-outline-primary nav-btn">
                                Next <i class="bi bi-arrow-right"></i>
                            </button>
                        </div>

                        <!-- Save All Labels section -->
                        <div class="save-all-container">
                            <div id="completionMessage" class="alert alert-success mb-2 py-1 px-2" style="display: none;">
                                <i class="bi bi-check-circle-fill me-1"></i> All images labeled! Ready to save.
                            </div>
                            <div class="d-flex justify-content-center align-items-center mb-4">
                                <button id="saveAllBtn" class="btn btn-success save-all-btn" disabled data-bs-toggle="tooltip"
                                        title="Complete labeling all images to enable this button">
                                    <i class="bi bi-save"></i> Save All Labels
                                </button>
                                <button id="getNextSetBtn" class="btn btn-primary save-all-btn" disabled data-bs-toggle="tooltip"
                                        title="Save all labels first to enable this button">
                                    <i class="bi bi-arrow-right-circle"></i> Get Next Set of Images
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right side: Keyboard Shortcuts -->
            <div class="shortcuts-sidebar">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-keyboard me-2"></i>Keyboard Shortcuts</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="shortcut-list">
                            <div class="shortcut-item">
                                <span class="shortcut-text">Save label</span>
                                <span class="key">↵ Enter</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Previous image</span>
                                <span class="key">← Left</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Next image</span>
                                <span class="key">→ Right</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Save all labels</span>
                                <span class="key">Ctrl+S</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Crop image</span>
                                <span class="key">Ctrl+C</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Refresh image</span>
                                <span class="key">R / F5</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Zoom in</span>
                                <span class="key">+</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Zoom out</span>
                                <span class="key">-</span>
                            </div>
                            <div class="shortcut-item">
                                <span class="shortcut-text">Pan image</span>
                                <span class="key">Drag</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add a toast notification container to the page -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i id="toastIcon" class="bi bi-info-circle me-2"></i>
                <strong class="me-auto" id="toastTitle">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                Notification message
            </div>
        </div>
    </div>
<!-- Data Attributes -->
<div id="images-data" data-images='{{ images | tojson | safe }}'></div>
<div id="verifyMode" data-verify='{{ verification_mode | tojson | safe }}'></div>
<div id="sessionData" data-session='{{ session_id | tojson | safe }}'></div>
<div id="batchData" data-batch='{{ batch_name | tojson | safe }}'></div>

{% endblock %}

{% block extra_js %}
<!-- Cropper.js JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>

<!-- Pass data from Flask to JavaScript -->
<script>
    // Global variables that will be used by annotate.js
    const ANNOTATION_DATA = {
    images: JSON.parse(document.getElementById('images-data').dataset.images),
    verification_mode: JSON.parse(document.getElementById('verifyMode').dataset.verify),
    session_id: JSON.parse(document.getElementById('sessionData').dataset.session),
    batch_name: JSON.parse(document.getElementById('batchData').dataset.batch)
};
</script>

<!-- Load the clean JavaScript file -->
<script src="{{ url_for('static', filename='js/annotator/annotate.js') }}"></script>
{% endblock %}
