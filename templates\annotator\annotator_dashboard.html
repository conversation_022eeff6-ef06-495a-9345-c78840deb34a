{% extends "base.html" %}

{% block title %}Annotator Dashboard - DADP Data Annotation Platform{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/annotator/annotator_dashboard.css') }}">
<link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header Title -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h1 class="dashboard-title">Annotator Dashboard</h1>
        </div>
    </div>

    <!-- Welcome Card -->
    <div class="welcome-card mb-4">
        <div class="row">
            <div class="col-md-auto">
                <div class="avatar-circle">
                    <span class="initials">{{ session.full_name[0] if session.full_name else session.username[0] }}</span>
                </div>
            </div>
            <div class="col">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h2>Welcome back, {{ session.full_name or session.username }} <span class="emoji">😊</span></h2>
                        <div class="typewriter-container">
                            <span id="text-part1" class="typewriter-text"></span>
                        </div>
                    </div>
                    <span class="annotator-tag">{{ annotation_mode|title }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Guidelines and Actions Section -->
    <div class="row">
        <!-- Task Guidelines with Tabs -->
        <div class="col-md-5 mb-4">
            <div class="card guidelines-card">
                <div class="card-body p-0">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs" id="guidelinesTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="task-tab" data-bs-toggle="tab" data-bs-target="#task" type="button" role="tab" aria-controls="task" aria-selected="true">Task Guidelines</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="keyboard-tab" data-bs-toggle="tab" data-bs-target="#keyboard" type="button" role="tab" aria-controls="keyboard" aria-selected="false">Keyboard Shortcuts</button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content" id="guidelinesTabContent">
                        <!-- Task Guidelines Tab -->
                        <div class="tab-pane fade show active p-3" id="task" role="tabpanel" aria-labelledby="task-tab">
                            {% if admin_instructions %}
                                <div class="instructions-content mt-3">{{ admin_instructions|nl2br }}</div>
                            {% endif %}
                        </div>

                        <!-- Keyboard Shortcuts Tab -->
                        <div class="tab-pane fade p-3" id="keyboard" role="tabpanel" aria-labelledby="keyboard-tab">
                            <table class="table table-sm shortcuts-table">
                                <tbody>
                                    <tr>
                                        <td>Next Image</td>
                                        <td><span class="badge bg-secondary">→ or N</span></td>
                                    </tr>
                                    <tr>
                                        <td>Previous Image</td>
                                        <td><span class="badge bg-secondary">← or P</span></td>
                                    </tr>
                                    <tr>
                                        <td>Save Label</td>
                                        <td><span class="badge bg-secondary">Enter</span></td>
                                    </tr>
                                    <tr>
                                        <td>Save All Labels</td>
                                        <td><span class="badge bg-secondary">Ctrl+S</span></td>
                                    </tr>
                                    <tr>
                                        <td>Zoom Controls</td>
                                        <td><span class="badge bg-secondary">Mouse Wheel</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="col-md-7">
            <div class="row">
                <!-- Manual Labelling Card -->
                <div class="col-md-4 mb-4">
                    <div class="card action-card {% if annotation_mode != 'manual' %}disabled-option{% endif %}">
                        <div class="card-body text-center">
                            <div class="card-icon mb-3">
                                <i class="bi bi-pencil-square"></i>
                            </div>
                            <h3 class="card-title">Manual Labelling</h3>
                            <p class="card-text">Draw and edit new labels on images.</p>
                            <button class="btn btn-primary action-button" id="manualLabellingBtn" data-href="{{ url_for('annotator_routes.annotate_route', start_labeling='true') }}">
                                Let's Label!
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Label Verification Card -->
                <div class="col-md-4 mb-4">
                    <div class="card action-card {% if annotation_mode != 'verification' %}disabled-option{% endif %}">
                        <div class="card-body text-center">
                            <div class="card-icon mb-3">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <h3 class="card-title">Label Verification</h3>
                            <p class="card-text">Review and verify existing images</p>
                            <button class="btn btn-primary action-button" id="verificationBtn" data-href="{{ url_for('annotator_routes.annotate_route', start_verification='true') }}">
                                Let's Verify!
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Supervision Mode Card -->
                <div class="col-md-4 mb-4">
                    <div class="card action-card {% if annotation_mode != 'supervision' %}disabled-option{% endif %}">
                        <div class="card-body text-center">
                            <div class="card-icon mb-3">
                                <i class="bi bi-eye"></i>
                            </div>
                            <h3 class="card-title">Supervision Mode</h3>
                            <p class="card-text">Live verify the AI response and fill the necessary responses</p>
                            <button class="btn btn-primary action-button" id="supervisionBtn" data-href="{{ url_for('supervision_routes.supervision_route') }}">
                                Let's Supervise!
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/annotator/annotator_dashboard.js') }}"></script>
{% endblock %}