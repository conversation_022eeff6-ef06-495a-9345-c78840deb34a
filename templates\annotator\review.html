{% extends "base.html" %}

{% block title %}Review Documents - Documind-O{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/annotator/review.css') }}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
    .document-item:hover {
        background-color: #f0f0f0;
        cursor: pointer;
    }
    .document-item.active {
        background-color: #e6f0ff;
        border-left: 4px solid #0d6efd;
    }
    .zoom-controls button {
        margin: 0 2px;
    }
    .sidebar-scroll {
        max-height: calc(100vh - 150px);
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="noise-texture"></div>
<div class="background-grid"></div>

<div class="container-fluid pt-4">
    <div class="row">
        <!-- Document Queue -->
        <div class="col-md-2">
            <div class="card h-100 shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">Document Queue</h5>
                </div>
                <div class="card-body p-0 sidebar-scroll">
                    <div class="list-group list-group-flush" id="documentsListGroup">
                        {% for file in files %}
                        <div class="list-group-item document-item d-flex justify-content-between align-items-center"
                             data-id="{{ file.id }}"
                             data-name="{{ file.name }}"
                             data-model-type="{{ file.model_type|default('standard') }}">
                            <div class="text-truncate" style="max-width: 65%;">{{ file.name }}</div>
                            <div class="d-flex flex-column align-items-end">
                                <span class="badge bg-secondary processing-status">Processing</span>
                                <small class="model-type-badge">{{ file.model_type|default('standard')|capitalize }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Review -->
        <div class="col-md-10">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Document Review</h5>
                        <small id="current-model-type" class="text-muted"></small>
                    </div>
                    <span id="progress-counter" class="badge bg-light text-dark"></span>
                </div>

                <div class="card-body">
                    <!-- Loading message -->
                    <div id="initialMessage">
                        <div class="text-center my-5">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>Processing documents...</h5>
                            <p>The first document will appear here when ready</p>
                        </div>
                    </div>

                    <!-- Document Data -->
                    <div id="documentDataContainer" class="d-none">
                        <div class="row">
                            <!-- Document Image -->
                            <div class="col-md-7 mb-3">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h6 class="mb-0">Document Image</h6>
                                    </div>
                                    <div class="card-body text-center p-0" style="height: calc(100vh - 280px);">
                                        <div class="zoom-controls my-2">
                                            <button id="zoomInBtn" class="btn btn-sm btn-outline-primary"><i class="fas fa-search-plus"></i></button>
                                            <span class="zoom-level">100%</span>
                                            <button id="zoomOutBtn" class="btn btn-sm btn-outline-primary"><i class="fas fa-search-minus"></i></button>
                                            <button id="resetZoomBtn" class="btn btn-sm btn-outline-secondary"><i class="fas fa-undo"></i></button>
                                            <button id="fitToScreenBtn" class="btn btn-sm btn-outline-secondary"><i class="fas fa-expand"></i></button>
                                        </div>
                                        <div class="image-container">
                                            <img id="documentImage" src="" alt="Document Image" class="img-fluid">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Extracted Data -->
                            <div class="col-md-5 mb-3">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">Extracted Data</h6>
                                        <div class="d-flex gap-2">
                                            <button type="button" id="saveButton" class="btn btn-sm btn-success">Save & Next</button>
                                            <button type="button" id="saveAllButton" class="btn btn-sm btn-warning">Save All</button>
                                            <button type="button" id="nextButton" class="btn btn-sm btn-primary">Next</button>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">Download</button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" id="downloadCsvBtn" href="#">Download CSV</a></li>
                                                    <li><a class="dropdown-item" id="downloadTxtBtn" href="#">Download TXT</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body" style="height: calc(100vh - 280px); overflow-y: auto;">
                                        <form id="extractedDataForm">
                                            <input type="hidden" id="currentDocumentId">
                                            <div id="extractedFields" class="row">
                                                <!-- Dynamic fields -->
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Completion Message -->
                    <div id="completionMessage" class="d-none text-center my-5">
                        <svg xmlns="http://www.w3.org/2000/svg" width="84" height="84" fill="currentColor" class="bi bi-check-circle-fill text-success mb-3" viewBox="0 0 16 16">
                            <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
                        </svg>
                        <h3 class="mb-3">All documents processed!</h3>
                        <p class="mb-4">You have successfully processed and reviewed all documents.</p>
                        <div class="d-flex justify-content-center gap-3">
                            <a href="{{ url_for('supervision_routes.supervision_route') }}" class="btn btn-primary btn-lg" style="min-width: 250px;">Process More Documents</a>
                            <a href="{{ url_for('annotator_routes.annotator_dashboard') }}" class="btn btn-primary btn-lg" style="min-width: 250px;">Annotator Dashboard</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> 
</div>
{% endblock %}

{% block extra_js %}
<script>
    window.DOCUMENT_TYPE = "{{ document_type }}";
    window.MODEL_TYPE = "{{ model_type | default('standard') }}";
    window.driveLinks = {};
    window.modelTypes = {};
</script>
{% for file in files %}
<script>
    window.driveLinks["{{ file.id }}"] = "{{ file.drive_link or '' }}";
    window.modelTypes["{{ file.id }}"] = "{{ file.model_type or 'standard' }}";
</script>
{% endfor %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="{{ url_for('static', filename='js/annotator/review.js') }}"></script>
{% endblock %}
