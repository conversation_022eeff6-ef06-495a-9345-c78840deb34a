{% extends "base.html" %}

{% block title %}Process Documents - Documind-O{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/annotator/supervision.css') }}" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h2>Process Documents</h2>
        <p class="lead">Extract valuable information from your documents with our advanced AI processing</p>
    </div>
    
    <!-- Source Selection (Simplified) -->
    <div class="row mb-4 source-options">
        <!-- Local File Upload -->
        <div class="col-md-6">
            <div class="card source-card shadow-corporate">
                <div class="card-body">
                    <div class="icon-title-container">
                        <div class="card-icon"><i class="fas fa-file-upload"></i></div>
                        <div class="icon-content">
                            <h5 class="card-title">Process Local Files</h5>
                            <p class="card-text">Upload documents from your computer for processing</p>
                        </div>
                    </div>
                    <form id="localUploadForm" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="documentType" class="form-label">Document Type</label>
                            <select class="form-select custom-select" id="documentType" name="document_type" required>
                                <option value="">Select document type</option>
                                <option value="check">Cheque</option>
                                <option value="passport">Passport</option>
                                <option value="invoice">Invoice</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="modelType" class="form-label">Processing Power</label>
                            <select class="form-select custom-select" id="modelType" name="model_type" required>
                                <option value="">Select processing power</option>
                                <option value="standard">Standard (Recommended)</option>
                                <option value="enhanced">Enhanced (Complex Documents)</option>
                                <option value="premium">Premium (Difficult Documents)</option>
                            </select>
                        </div>
                        <div class="form-group mb-3">
                            <label for="files" class="form-label">Select Files</label>
                            <div class="file-upload-wrapper">
                                <input type="file" class="form-control custom-file-input" id="files" name="files[]" multiple required>
                                <div class="file-upload-message">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Drag and drop files or click to browse</span>
                                    <div class="file-count" id="selectedFileCount">
                                        <span class="badge">0 Files Selected</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="button-container">
                            <button type="submit" class="btn btn-corporate">
                                <i class="fas fa-cogs me-2"></i>Process Local Files
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Google Drive -->
        <div class="col-md-6">
            <div class="card source-card shadow-corporate">
                <div class="card-body">
                    <div class="icon-title-container">
                        <div class="card-icon"><i class="fab fa-google-drive"></i></div>
                        <div class="icon-content">
                            <h5 class="card-title">Process from Google Drive</h5>
                            <p class="card-text">Connect to your Google Drive to select documents for processing</p>
                        </div>
                    </div>
                    <form id="driveForm" class="needs-validation" novalidate>
                        <div class="form-group mb-3">
                            <label for="driveDocumentType" class="form-label">Document Type</label>
                            <select class="form-select custom-select" id="driveDocumentType" name="document_type" required>
                                <option value="">Select document type...</option>
                                <option value="check">Cheque</option>
                                <option value="passport">Passport</option>
                                <option value="invoice">Invoice</option>
                            </select>
                            <div class="invalid-feedback">Please select a document type</div>
                        </div>
                        <div class="form-group mb-3">
                            <label for="driveModelType" class="form-label">Processing Power</label>
                            <select class="form-select custom-select" id="driveModelType" name="model_type" required>
                                <option value="">Select processing power</option>
                                <option value="standard">Standard (Recommended)</option>
                                <option value="enhanced">Enhanced (Complex Documents)</option>
                                <option value="premium">Premium (Difficult Documents)</option>
                            </select>
                            <div class="invalid-feedback">Please select processing power</div>
                        </div>
                        <div class="button-container">
                            <button type="button" id="loadDriveFiles" class="btn btn-corporate">
                                <i class="fas fa-sync-alt me-2"></i>Load Drive Files
                            </button>
                        </div>
                        
                        <!-- Processing status div -->
                        <div id="processingStatus" class="mb-3 processing-status" style="display: none;">
                            <div class="progress progress-corporate">
                                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="statusMessage" class="mt-2 status-message"></div>
                        </div>
                        
                        <!-- Drive Files List -->
                        <div id="driveFilesList" class="mb-3 files-list" style="display: none;">
                            <h6 class="files-header">Available Files:</h6>
                            <div class="list-group custom-list-group">
                                <!-- Files will be loaded here -->
                            </div>
                            <div class="button-container selection-buttons">
                                <button type="button" id="selectAllFiles" class="btn btn-corporate btn-sm">
                                    <i class="fas fa-check-square me-1"></i>Select All
                                </button>
                                <button type="button" id="deselectAllFiles" class="btn btn-corporate btn-sm">
                                    <i class="fas fa-square me-1"></i>Deselect All
                                </button>
                            </div>
                        </div>

                        <div class="button-container">
                            <button type="submit" id="processDriveFiles" class="btn btn-corporate" disabled>
                                <i class="fas fa-cloud-download-alt me-2"></i>Process Selected Files
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Model Selection Guide -->
    <div class="card main-card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <div class="header-icon"><i class="fas fa-tachometer-alt"></i></div>
                <h4>Processing Power Guide</h4>
            </div>
            <button class="btn btn-corporate btn-sm" id="toggleGuideBtn" data-bs-toggle="collapse" data-bs-target="#powerGuideContent" aria-expanded="false" aria-controls="powerGuideContent">
                <i class="fas fa-info-circle me-1"></i> Show Guide
            </button>
        </div>
        <div class="collapse" id="powerGuideContent">
            <div class="card-body power-guide">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card power-card standard-card">
                            <div class="card-body">
                                <div class="power-icon"><i class="fas fa-bolt"></i></div>
                                <h5 class="card-title">Standard</h5>
                                <div class="power-meter">
                                    <div class="power-bar active"></div>
                                    <div class="power-bar"></div>
                                    <div class="power-bar"></div>
                                </div>
                                <p class="card-text">Perfect for everyday document processing with clear text and standard layouts. Cost-effective and fast.</p>
                                <p class="small text-muted mb-0">Best for: Clean, high-quality documents</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card power-card enhanced-card">
                            <div class="card-body">
                                <div class="power-icon"><i class="fas fa-bolt"></i></div>
                                <h5 class="card-title">Enhanced</h5>
                                <div class="power-meter">
                                    <div class="power-bar active"></div>
                                    <div class="power-bar active"></div>
                                    <div class="power-bar"></div>
                                </div>
                                <p class="card-text">Improved accuracy for complex layouts, handwriting, and moderate image quality issues.</p>
                                <p class="small text-muted mb-0">Best for: Mixed quality documents, complex forms</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card power-card premium-card">
                            <div class="card-body">
                                <div class="power-icon"><i class="fas fa-bolt"></i></div>
                                <h5 class="card-title">Premium</h5>
                                <div class="power-meter">
                                    <div class="power-bar active"></div>
                                    <div class="power-bar active"></div>
                                    <div class="power-bar active"></div>
                                </div>
                                <p class="card-text">Advanced computation for challenging documents with poor image quality, extensive handwriting or complex layouts.</p>
                                <p class="small text-muted mb-0">Best for: Difficult documents, poor scans</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/annotator/supervision.js') }}"></script>
{% endblock %} 