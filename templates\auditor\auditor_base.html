<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="username" content="{{ session.username|default('') }}">
    <title>{% block title %}Auditor Dashboard{% endblock %} - DADP</title>
    {# Link Bootstrap and Icons (assuming they are available globally or in base.html's context) #}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
    {# Link the shared admin layout CSS #}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin/admin_layout.css') }}">
    {# Add Poppins font if not already global #}
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/auditor/dashboard.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="header-flex">
                <button class="sidebar-toggle" id="sidebarToggle" title="Toggle Sidebar">
                    <i class="bi bi-list"></i>
                </button>
                <a class="sidebar-brand" href="{{ url_for('auditor_routes.auditor_dashboard') }}">
                    <span>DADP</span>
                </a>
            </div>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'auditor_routes.auditor_dashboard' else '' }}"
                   href="{{ url_for('auditor_routes.auditor_dashboard') }}"
                   data-tooltip="Dashboard">
                    <i class="bi bi-grid-1x2-fill"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'auditor_routes.auditor_available_tasks' else '' }}"
                   href="{{ url_for('auditor_routes.auditor_available_tasks') }}"
                   data-tooltip="Available Tasks">
                    <i class="bi bi-list-task"></i>
                    <span>Available Tasks</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link {{ 'active' if request.endpoint == 'auditor_routes.auditor_history' else '' }}"
                   href="{{ url_for('auditor_routes.auditor_history') }}"
                   data-tooltip="History">
                    <i class="bi bi-clock-history"></i>
                    <span>History</span>
                </a>
            </li>
        </ul>

        <div class="sidebar-footer mt-auto">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('auth_routes.logout') }}" data-tooltip="Logout">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>Logout</span>
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                       data-bs-toggle="dropdown" aria-expanded="false" data-tooltip="Profile">
                        <i class="bi bi-person-circle"></i>
                        <span>{{ session.username|default('User') }}</span>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="userDropdown">
                        <li><span class="dropdown-item-text">{{ session.role|capitalize|default('Auditor') }}</span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('user_routes.change_password') }}">
                                <i class="bi bi-key-fill"></i>
                                <span>Change Password</span>
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <div class="main-content">
        <!-- Flash messages container -->
        <div class="flash-container container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show shadow-sm" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
        </div>

        {% block content %}
        <!-- Main page content goes here -->
        {% endblock %}
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/auditor/sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='js/auditor/utils.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>