{% extends "auditor/auditor_base.html" %}

{% block title %}Review Available Tasks{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auditor/available_tasks.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h4 class="mb-4">Select and Review Tasks</h4>

    <!-- Task Selection Form -->
    <div class="card selection-card shadow-sm mb-3" id="selectionForm">
        <div class="card-header">
            <h5 class="mb-0 fs-6"><i class="bi bi-ui-checks-grid me-2"></i>Select Task Criteria</h5>
        </div>
        <div class="card-body">
            <form id="selection-form">
                <!-- Hidden field to store the verification mode if needed by JS -->
                <input type="hidden" id="current-verification-mode" name="current_verification_mode" value="{{ request.args.get('mode', '') }}">

                <div class="row g-3 align-items-end">
                    <!-- Verification Mode Selection -->
                    <div class="col-md-4">
                        <label for="verification-mode" class="form-label form-label-sm">Verification Mode</label>
                        <select class="form-select form-select-sm" id="verification-mode" name="verification_mode" required>
                            <option value="">Select mode...</option>
                            <!-- Populate options dynamically via JS or backend -->
                            <option value="manual" {% if request.args.get('mode') == 'manual' %}selected{% endif %}>Manual Label Verification</option>
                            <option value="agentic" {% if request.args.get('mode') == 'agentic' %}selected{% endif %}>Agentic Verification</option>
                        </select>
                    </div>

                    <!-- Dataset Selection -->
                    <div class="col-md-4">
                        <label for="dataset" class="form-label form-label-sm">Dataset</label>
                        <select class="form-select form-select-sm" id="dataset" name="dataset" required {% if not request.args.get('mode') %}disabled{% endif %}>
                            <option value="">Select dataset...</option>
                            {% for dataset in datasets %}
                            <option value="{{ dataset.id }}">{{ dataset.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Image Folder Selection -->
                    <div class="col-md-4">
                        <label for="image-folder" class="form-label form-label-sm">Image Folder</label>
                        <select class="form-select form-select-sm" id="image-folder" name="image_folder" disabled>
                            <option value="">Select image folder...</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>

                    <!-- Verifier Selection -->
                    <div class="col-md-4">
                        <label for="verifier" class="form-label form-label-sm">Verifier</label>
                        <select class="form-select form-select-sm" id="verifier" name="verifier" disabled>
                            <option value="">Select verifier...</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>

                    <!-- JSON File Selection (if applicable) -->
                    <div class="col-md-4">
                        <label for="json-file-select" class="form-label form-label-sm">Verification File</label>
                        <select class="form-select form-select-sm" id="json-file-select" name="json_file" disabled>
                            <option value="">Select file...</option>
                            <!-- Options populated by JS -->
                        </select>
                    </div>

                    <!-- Load Button -->
                    <div class="col-md-4">
                        <button type="submit" id="load-tasks" class="btn btn-primary w-100" disabled>
                            <i class="bi bi-cloud-download me-1"></i> Load Tasks
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Toggle Form Button -->
    <button type="button" id="toggleFormBtn" class="btn btn-sm btn-outline-secondary mb-3">
        <i class="bi bi-chevron-up"></i> Hide Selection Form
    </button>

    <!-- Tasks Container -->
    <div id="tasks-container" class="card tasks-container shadow-sm" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0 fs-6"><i class="bi bi-images me-2"></i>Review Area</h5>
             <span id="task-count" class="badge bg-light text-dark rounded-pill float-end"></span>
        </div>
        <div class="card-body tasks-body">
            <!-- Loading Spinner -->
            <div id="tasks-loading" class="loading-state text-center p-5" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-muted">Loading tasks...</p>
            </div>

            <!-- No Tasks Message -->
            <div id="no-tasks-message" class="empty-state text-center p-5" style="display: none;">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <p class="mt-2">No tasks available for the selected criteria.</p>
            </div>

            <!-- Tasks List Table -->
            <div id="tasks-list" style="display: none;">
                <div class="table-responsive">
                    <table id="image-review-table" class="table task-table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th width="50%">Image</th>
                                <th width="50%">Labels (Editable)</th>
                            </tr>
                        </thead>
                        <tbody>{# Populated by available_tasks.js #}</tbody>
                    </table>
                </div>
                <div class="action-row mt-3 mb-2 d-flex justify-content-end gap-2">
                     <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#commentsModal">
                        <i class="bi bi-chat-square-text me-1"></i>Add Comments
                    </button>
                    <button id="save-all-btn" class="btn btn-sm btn-success">
                        <i class="bi bi-save me-1"></i>Save All Labels
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Comments Modal -->
<div class="modal fade" id="commentsModal" tabindex="-1" aria-labelledby="commentsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentsModalLabel">Add Batch Comments</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="batchComments" class="form-label">Comments (will be stored in audit history)</label>
                    <textarea class="form-control" id="batchComments" rows="4" placeholder="Enter any notes or comments about this batch..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="saveCommentsBtn" class="btn btn-sm btn-primary">Save Comments</button>
            </div>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imagePreviewModalLabel">Image Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div id="previewImageContainer">
                    <img id="previewImage" src="" class="img-fluid" alt="Preview" style="max-height: 80vh;">
                    <div id="imageLoadError" class="alert alert-warning mt-3" style="display: none;">
                        Failed to load image. The image might not exist or you may not have access permissions.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="image-controls">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="zoomOut">
                        <i class="bi bi-zoom-out"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="zoomIn">
                        <i class="bi bi-zoom-in"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" id="resetZoom">
                        <i class="bi bi-arrows-fullscreen"></i> Reset
                    </button>
                </div>
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    <!-- jQuery is required for the AJAX calls in the JS file -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load the original JS which handles the form logic, task loading, table population, saving etc. -->
    <script src="{{ url_for('static', filename='js/auditor/available_tasks.js') }}"></script>
{% endblock %}