{% extends "auditor/auditor_base.html" %}

{% block title %}Review History{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/auditor/history.css') }}">
{% endblock %}

{% block content %}
<div class="history-title text-center mb-4">
    <h4>Review History</h4>
    <div class="title-underline"></div>
</div>

<div class="row justify-content-center mb-4">
    <div class="col-md-10">
        <div class="search-box">
            <i class="bi bi-search search-icon"></i>
            <input type="text" id="searchInput" class="form-control search-input" placeholder="Search by dataset, file name, verifier...">
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card history-card">
            <!-- <div class="card-header d-flex justify-content-between align-items-center"> -->
                <!-- <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Reviewed Batches</h5> -->
            </div>
            <div class="card-body p-0">
                {% if history_entries %}
                <div class="table-responsive">
                    <table class="table table-hover custom-table mb-0 align-middle">
                        <thead>
                            <tr>
                                <th>Review Date</th>
                                <th>Dataset</th>
                                <th>File</th>
                                <th>Batch</th>
                                <th>Verifier</th>
                                <th>Status</th>
                                <th>Processed By</th>
                                <th>Comments</th>
                            </tr>
                        </thead>
                        <tbody id="auditTable">
                            {% for entry in history_entries %}
                            <tr class="searchable-row">
                                <td>{{ entry.processed_at }}</td>
                                <td>{{ entry.dataset }}</td>
                                <td>{{ entry.file_name or '-' }}</td>
                                <td>{{ entry.verifier or '-' }}</td>
                                <td>
                                    <span class="status-badge {% if entry.status == 'approved' %}status-approved{% elif entry.status == 'rejected' %}status-rejected{% else %}status-pending{% endif %}">
                                        {{ entry.status }}
                                    </span>
                                </td>
                                <td>{{ entry.processed_by }}</td>
                                <td class="small">{{ entry.comments or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}
            </div>
            {% if pagination and (pagination.has_prev or pagination.has_next) %}
            <div class="card-footer bg-transparent border-0 py-3">
                <nav aria-label="History pagination">
                    <ul class="pagination pagination-sm justify-content-center mb-0">
                        <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('auditor_routes.auditor_history', page=pagination.prev_num) }}">&laquo;</a>
                        </li>
                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                <li class="page-item {% if page_num == pagination.page %}active{% endif %}">
                                    <a class="page-link" href="{{ url_for('auditor_routes.auditor_history', page=page_num) }}">{{ page_num }}</a>
                                </li>
                            {% else %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                            {% endif %}
                        {% endfor %}
                        <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
                            <a class="page-link" href="{{ url_for('auditor_routes.auditor_history', page=pagination.next_num) }}">&raquo;</a>
                        </li>
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if not history_entries %}
<div class="empty-state text-center">
    <i class="bi bi-emoji-frown fs-1"></i>
    <p class="mt-3 mb-0">No review history found.</p>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
    <script src="{{ url_for('static', filename='js/auditor/history.js') }}"></script>
{% endblock %}