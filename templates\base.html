<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}DADP Data Annotation & Delivery Platform{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Auditor CSS for auditor role -->
    {% if current_user.is_authenticated and hasattr(current_user, 'role') and current_user.role == 'auditor' %}
    <link href="{{ url_for('static', filename='css/auditor/styles.css') }}" rel="stylesheet">
    {% endif %}
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% block navbar %}
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-database-fill me-2"></i>DADP
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if session.username %}
                        {% if session.role == 'admin' %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_routes.dashboard' %}active{% endif %}" href="{{ url_for('admin_routes.dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_routes.manage_users' %}active{% endif %}" href="{{ url_for('admin_routes.manage_users') }}">
                                <i class="bi bi-people-fill"></i> Users
                            </a>
                        </li>
                        {% endif %}

                        {% if session.role == 'auditor' %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'auditor_routes.auditor_dashboard' %}active{% endif %}" href="{{ url_for('auditor_routes.auditor_dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Auditor Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'auditor_routes.auditor_history' %}active{% endif %}" href="{{ url_for('auditor_routes.auditor_history') }}">
                                <i class="bi bi-clock-history"></i> Audit History
                            </a>
                        </li>
                        {% endif %}

                        {% if session.role == 'annotator' %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'annotator_routes.annotator_dashboard' %}active{% endif %}" href="{{ url_for('annotator_routes.annotator_dashboard') }}">
                                <i class="bi bi-grid-1x2-fill"></i> Dashboard
                            </a>
                        </li>
                        {% endif %}
                    {% endif %}
                </ul>

                <ul class="navbar-nav">
                    {% if session.username %}
                        <!-- Role badge -->
                        <li class="nav-item me-3 d-flex align-items-center">
                            <span class="badge rounded-pill" style="background-color: rgba(255,255,255,0.2); color: white;">
                                {% if session.role == 'admin' %}
                                    <i class="bi bi-shield-fill me-1"></i>
                                {% elif session.role == 'auditor' %}
                                    <i class="bi bi-eye-fill me-1"></i>
                                {% else %}
                                    <i class="bi bi-person-fill me-1"></i>
                                {% endif %}
                                {{ session.role|capitalize }}
                            </span>
                        </li>
                        <!-- User dropdown menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i> {{ session.full_name or session.username }}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                {% if session.role == 'admin' %}
                                <li><a class="dropdown-item" href="{{ url_for('admin_routes.manage_users') }}"><i class="bi bi-people me-2"></i> Manage Users</a></li>
                                <li><hr class="dropdown-divider"></li>
                                {% endif %}
                                <li><a class="dropdown-item" href="{{ url_for('user_routes.change_password') }}"><i class="bi bi-key me-2"></i> Change Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth_routes.logout') }}"><i class="bi bi-box-arrow-right me-2"></i> Logout</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth_routes.login') }}">
                                <i class="bi bi-box-arrow-in-right me-1"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    {% endblock %}

    <!-- Flash Messages -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show shadow-sm" role="alert" style="border-radius: 10px; border: none;">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% if category == 'success' %}
                                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                                {% elif category == 'danger' or category == 'error' %}
                                    <i class="bi bi-exclamation-circle-fill text-danger fs-4"></i>
                                {% elif category == 'warning' %}
                                    <i class="bi bi-exclamation-triangle-fill text-warning fs-4"></i>
                                {% elif category == 'info' %}
                                    <i class="bi bi-info-circle-fill text-info fs-4"></i>
                                {% endif %}
                            </div>
                            <div>
                                {{ message }}
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer py-2">
        <div class="container text-center">
            <div class="row">
                <div class="col-md-12">
                    <span class="text-muted">
                        <i class="bi bi-database me-2"></i>
                        Data Annotation & Delivery Platform &copy; Process Venue {{ now.year }}
                    </span>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <!-- Auditor JS for auditor role -->
    {% if current_user.is_authenticated and hasattr(current_user, 'role') and current_user.role == 'auditor' %}
    <script src="{{ url_for('static', filename='js/auditor/main.js') }}"></script>
    {% endif %}
    {% block extra_js %}{% endblock %}
</body>
</html>