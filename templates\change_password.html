{% extends "base.html" %}

{% block title %}Change Password{% endblock %}

{% block content %}
<div class="container mt-4">
<div class="row justify-content-center">
    <div class="col-lg-6 col-md-8">
        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0"><i class="bi bi-key-fill me-2"></i>Change Your Password</h4>
            </div>
            <div class="card-body p-4">
                <form method="post" action="{{ url_for('user_routes.change_password') }}" id="changePasswordForm" novalidate>
                    <div class="form-floating mb-3">
                        <input type="password" class="form-control" id="current_password" name="current_password" placeholder="Current Password" required>
                        <label for="current_password">Current Password</label>
                         <div class="invalid-feedback">Please enter your current password.</div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="password" class="form-control" id="new_password" name="new_password" placeholder="New Password" required minlength="6">
                        <label for="new_password">New Password</label>
                        <div class="form-text">Must be at least 6 characters long.</div>
                        <div class="invalid-feedback">Password must be at least 6 characters long.</div>
                    </div>
                    <div class="form-floating mb-3">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm New Password" required>
                        <label for="confirm_password">Confirm New Password</label>
                        <div class="invalid-feedback">Passwords do not match.</div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                         {# Link back to appropriate dashboard based on user role #}
                        {% if user.role == 'admin' %}
                            <a href="{{ url_for('admin_routes.dashboard') }}" class="btn btn-outline-secondary">Cancel</a>
                        {% elif user.role == 'auditor' %}
                            <a href="{{ url_for('auditor_routes.auditor_dashboard') }}" class="btn btn-outline-secondary">Cancel</a>
                        {% elif user.role == 'annotator' %}
                            <a href="{{ url_for('annotator_routes.annotator_dashboard') }}" class="btn btn-outline-secondary">Cancel</a>
                        {% else %}
                            <a href="{{ url_for('annotator_routes.annotate_route') }}" class="btn btn-outline-secondary">Cancel</a>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-1"></i> Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Basic client-side validation for password match
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('changePasswordForm');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    function validatePasswords() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity("Passwords do not match.");
        } else {
            confirmPassword.setCustomValidity("");
        }
    }

    newPassword.addEventListener('change', validatePasswords);
    confirmPassword.addEventListener('keyup', validatePasswords);

    // Bootstrap validation styling
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        validatePasswords(); // Re-validate on submit
        form.classList.add('was-validated');
    }, false);
});
</script>
{% endblock %}