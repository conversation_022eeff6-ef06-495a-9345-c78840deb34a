<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documind-o - HuAI Agent</title>
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/img/PVlogo-1024x780.webp') }}" type="image/webp">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/documind/home.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
</head>
<body>
    <div class="noise-texture"></div>
    <!-- Header Section -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <a href="/" class="logo">
                        <img src="{{ url_for('static', filename='img/PVlogo-1024x780.png') }}" alt="Documind-o Logo">
                    </a>
                    <div class="logo-text">
                        <span class="logo-line-1">End to End Data Solutions</span>
                        <span class="logo-line-2">Human-AI Collaboration</span>
                        <span class="beta-tag">Beta Version</span>
                    </div>
                </div>

                <button class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                <nav class="main-nav">
                    <div class="nav-container">
                        <ul class="nav-links">
                            <li><a href="#features">Features</a></li>
                            <li><a href="#how-it-works">How It Works</a></li>
                        </ul>
                    </div>
                    <a href="/" class="nav-cta">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Back to Home
                      </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Documind-o<br><span class="huai-agent">HAI</span> A<span class="smaller">GENT</span></span></h1>
                <div class="huai-tagline">
                    <span class="tagline-typing"></span><span class="tagline-cursor">|</span>
                </div>
                <h2 class="hero-title-2">From Source to Solution → Smart Data Validation and Delivery</h2>
                <div class="hero-main-content">
                    <div class="hero-description">
                        <div class="process-steps">
                            <div class="process-step">
                                <h3>Data Fetching</h3>
                                <p>Choose Data Source</p>
                            </div>
                            <div class="process-step">
                                <h3>Data Processing</h3>
                                <p>AI → Time Saving<br>Human → Precision</p>
                            </div>
                            <div class="process-step">
                                <h3>Data Delivery</h3>
                                <p>Save to Desired Database</p>
                            </div>
                        </div>
                        <div class="typing-container">
                            <div class="typing-animation">
                                <span class="typing-message">Seamlessly fetch data from anywhere, store everywhere.</span>
                            </div>
                        </div>
                        <!--<p class="hero-main-content-p"> From source to solution → smart data validation and delivery. </p>-->
                        <div class="cta-buttons">
                            <a href="/login?source=data_fetching&role=admin" class="btn-primary">Data Fetching</a>
                            <a href="/login?source=data_processing&role=annotator" class="btn-primary">Data Processing</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-3"></div>
        </div>
        <div class="container">
            <h2 class="section-title">Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cloud-download-alt"></i>
                    </div>
                    <h3 class="feature-title">Multiple Data Sources</h3>
                    <p class="feature-desc">Seamless integration of platforms like Telegram, Google Drive, and more.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Detailed Analytics</h3>
                    <p class="feature-desc">Gain insights from your documents with comprehensive analytics and reporting tools.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="feature-title">Multi-format Support</h3>
                    <p class="feature-desc">Process diverse documents with intelligent format detection.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="info-section" id="how-it-works">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-2"></div>
            <div class="shape shape-4"></div>
        </div>
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <div class="steps-container">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>Fetch Documents</h3>
                        <p>Connect and extract data from various platforms including Telegram, Google Drive, and more with seamless integration.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Process in Secure Environment</h3>
                        <p>Select processing power (Standard, Enhanced, or Premium) based on document quality and complexity.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Review & Export</h3>
                        <p>Inspect processed documents, review extracted data, make corrections, and export it to your desired database.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Data Storage Section -->
    <!--<section class="features-section" id="data-storage">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-3"></div>
        </div>
        <div class="container">
            <h2 class="section-title">Data Storage</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3 class="feature-title">Your Database</h3>
                    <p class="feature-desc">Securely fetch and store your data in your desired database.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fab fa-google-drive"></i>
                    </div>
                    <h3 class="feature-title">Google Drive</h3>
                    <p class="feature-desc">Organizes original documents, processed images, and downloaded Telegram files with secure access via OAuth authentication.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <h3 class="feature-title">Google Sheets</h3>
                    <p class="feature-desc">Maintains field mappings by document type, user login activity logs, and structured extracted document data.</p>
                </div>
            </div>
        </div>
    </section>-->


    <!-- Footer Section -->
    <footer class="main-footer">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
        <div class="container">
            <p>Documind-o • HAI Agent • All Rights Reserved • &copy; 2025 </p>
        </div>
    </footer>

    <!-- Include external JavaScript file -->
    <script src="{{ url_for('static', filename='js/documind/home.js') }}"></script>
</body>
</html>