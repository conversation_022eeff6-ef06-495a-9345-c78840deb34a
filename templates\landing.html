<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>DADP - Data Analytics & Delivery Platform</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

  <!-- Optional Font Awesome (if needed elsewhere) -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
  <!-- Custom Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap" rel="stylesheet">

  <!-- Your custom CSS (will be updated based on documind/home.css) -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/common/landing_page.css') }}">

  <!-- Removed link to contact-styled.css as it wasn't provided -->

</head>
<body>

<!-- Background elements similar to documind/home.css will be styled via CSS -->
<div class="background-gradient"></div>
<div class="noise-texture"></div>
<div class="background-grid"></div>

<!-- Updated Header Structure -->
<header class="main-header">
    <div class="container">
        <div class="header-content">
            <div class="logo-container">
                <a class="logo" href="/">
                    <img alt="ProcessVenue Logo"
                         src="{{ url_for('static', filename='img/PVlogo-1024x780.png') }}" />
                </a>
                <!-- Added logo text container -->
                <div class="logo-text">
                    <!--<span class="logo-line-1">End to End Data Solutions</span>-->
                    <span class="logo-line-2">Human Augmented AI Agents</span>
                    <span class="beta-tag">Beta Version</span>
                </div>
            </div>
            <button class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <nav class="main-nav">
                <div class="nav-container">
                    <ul class="nav-links">
                        <li>
                            <a href="/">Home</a>
                        </li>
                        <li class="dropdown">
                            <!-- NOTE: Dropdown functionality might need JS adjustment if not already handled by landing.js -->
                            <a href="#" class="dropdown-toggle">
                                Services <!-- <i class="bi bi-chevron-down"></i> --> <!-- Removed icon for now -->
                            </a>
                            <div class="dropdown-content">
                                <a href="/synthetic">SynGround</a>
                                <a href="/documind">Documind-o</a>
                                <a href="/note-ocr">NoteOCR</a>
                            </div>
                        </li>
                        <li>
                            <a href="#about">About</a>
                        </li>
                        <li>
                            <a href="#contact">Contact</a>
                        </li>
                    </ul>
                </div>
                <a class="nav-cta login-btn" href="/login">
                    Login
                </a>
            </nav>
        </div>
    </div>
</header>

<!-- Updated Hero Section Structure -->
<section class="hero-section">
  <!-- Floating shapes structure added -->
  <div class="floating-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
    <div class="shape shape-4"></div>
    <div class="shape shape-5"></div>
  </div>
  <div class="container">
      <div class="hero-content" style="display: flex; justify-content: space-between; align-items: center;"> <!-- Use Flexbox for layout -->
          <!-- Wrapper for left-aligned content -->
          <div class="hero-left-content">
              <!-- Using existing H1 content -->
              <h1 class="hero-title"><span class="huai-agent">D<span class="smaller">ATA</span><span class="huai-agent"> A<span class="smaller">NALYTICS</span><span class="huai-agent"> &amp; <span class="huai-agent-1">D</span><span class="smaller">ELIVERY </span><span class="huai-agent-1">P</span><span class="smaller">LATFORM</span></span></span></h1>
              <h3 class="hero-subtitle">HAI-Agent platform with HITL for precise AI training Datasets & Enterprise Data Management.</h3>
              <!-- New container for animated taglines -->
              <div class="tagline-animation-container">
                  <div class="tagline-column pillar-btn">
                      <i class="fas fa-shield-alt" style="margin-right: 15px;"></i>
                      <span id="tagline-left">Your Data</span> <!-- Initial text -->
                  </div>
                  <div class="tagline-column pillar-btn">
                      <i class="fas fa-check-circle" style="margin-right: 15px;"></i>
                      <span id="tagline-right">Our AI Processing</span> <!-- Initial text -->
                  </div>
              </div>
              <!-- Keeping existing CTA buttons -->
              <div class="action-buttons"> <!-- Replaced cta-buttons with action-buttons -->
                  <a class="action-btn btn-primary-action" href="/login">
                      <i class="fas fa-rocket" style="margin-right: 8px;"></i>
                      Get Started
                  </a>
                  <a class="action-btn btn-secondary-action" href="#features">
                      <i class="fas fa-book" style="margin-right: 8px;"></i>
                      Learn More
                  </a>
                  <a class="action-btn btn-tertiary-action" href="https://huggingface.co/Process-Venue" target="_blank">
                      <i class="fas fa-database" style="margin-right: 8px;"></i>
                      See Sample Dataset
                  </a>
                  <a class="action-btn btn-tertiary-action" href="/synthetic">
                      <i class="fas fa-layer-group" style="margin-right: 8px;"></i>
                      SynGround
                  </a>
                  <a class="action-btn btn-tertiary-action" href="/documind">
                      <i class="fas fa-file-image" style="margin-right: 8px;"></i>
                      Documind-o
                  </a>
                  <a class="action-btn btn-tertiary-action" href="/note-ocr">
                      <i class="fas fa-file-alt" style="margin-right: 8px;"></i>
                      NoteOCR
                  </a>
              </div>
          </div>
          <!-- Moved Typewriter Animation to the right -->

      </div>
  </div>
</section>

<!-- Updated Features Section Structure -->
<section id="features" class="features-section">
  <div class="container">
      <h2 class="section-title">Platform Features</h2> <!-- Added a title like in documind -->
      <div class="features-grid">
          <!-- Feature 1 -->
          <div class="feature-card">
              <div class="feature-icon">
                  <i class="bi bi-lightbulb"></i> <!-- Adjusted icon size/style via CSS potentially -->
              </div>
              <h3 class="feature-title">Efficient Annotation</h3>
              <p class="feature-desc">Accelerate your workflow with fast, accurate, and intuitive data annotation.</p>
          </div>
          <!-- Feature 2 -->
          <div class="feature-card">
              <div class="feature-icon">
                   <i class="bi bi-shield-check"></i>
              </div>
              <h3 class="feature-title">Auditing Mode</h3>
              <p class="feature-desc">Ensure quality and compliance with seamless, real-time auditing capabilities.</p>
          </div>
          <!-- Feature 3 -->
          <div class="feature-card">
               <div class="feature-icon">
                   <i class="bi bi-people"></i>
               </div>
              <h3 class="feature-title">Team collaboration</h3>
              <p class="feature-desc">Collaborate with your team in real-time, share annotations, and track progress.</p>
          </div>
          <!-- Feature 4 -->
          <div class="feature-card">
              <div class="feature-icon">
                  <i class="bi bi-folder2-open"></i>
              </div>
              <h3 class="feature-title">File management</h3>
              <p class="feature-desc">Organize and manage your data with ease.</p>
          </div>
      </div>
  </div>
</section>

<!-- About Section - Keeping existing structure but applying new styles via CSS -->
<section class="about-section" id="about">
    <div class="container"> <!-- Added container for consistency -->
        <div class="about-text"> <!-- Using about-text class for styling -->
            <h2 class="section-title">About ProcessVenue</h2> <!-- Use section-title styling -->
            <h3 class="about-subtitle">Transforming Data into Actionable Intelligence</h3> <!-- Added about-subtitle -->
            <p>
                Founded in 2014, ProcessVenue delivers secure, compliant, &amp; AI-powered data solutions to businesses worldwide.
            </p>

            <div class="expertise-section">
                <h4>Our Expertise</h4>
                <div class="expertise-cards">
                    <div class="expertise-card">
                        <h5>Data Analytics &amp; Delivery Platform (DADP)</h5>
                        <p>
                            AI-powered platform with human-in-the-loop expertise for precise AI training Datasets &amp; Agentic Process Automations.
                        </p>
                    </div>
                    <div class="expertise-card">
                        <h5>Business &amp; Knowledge Process Outsourcing</h5>
                        <p>
                            AI &amp; ML driven back-office support for finance, customer service, content moderation, and data management.
                        </p>
                    </div>
                </div>
            </div>

            <div class="compliance-section">
                <h4>Compliance &amp; Security</h4>
                <p>We ensure global data protection with industry-leading certifications:</p>
                <div class="compliance-badges">
                    <span class="badge">SOC2</span>
                    <span class="badge">HIPAA</span>
                    <span class="badge">GDPR</span>
                    <span class="badge">ISO 27001</span>
                </div>
            </div>
        </div>
    </div>
    <!-- Removed old animated blob -->
</section>

<!-- Updated Contact Section Structure -->
<section class="contact-section" id="contact">
  <div class="container">
    <h2 class="section-title">Get in Touch</h2>

    <!-- Added social buttons structure from documind example (if applicable) or keep existing -->
     <div class="contact-social">
      <a class="social-btn website-btn" href="https://www.processvenue.com/" target="_blank">
        <i class="fas fa-globe"></i> Visit Website
      </a>
      <a class="social-btn linkedin-btn" href="https://www.linkedin.com/showcase/processvenue/about/" target="_blank">
        <i class="fab fa-linkedin"></i> LinkedIn
      </a>
      <a class="social-btn twitter-btn" href="https://twitter.com/processvenue" target="_blank">
        <i class="fab fa-twitter"></i> Twitter <!-- Assuming documind css styles these -->
      </a>
    </div>

    <!-- Using contact-info grid structure -->
    <div class="contact-info">
        <!-- Email -->
        <div class="contact-item">
            <div class="contact-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="contact-details">
                <h3>Email Us</h3>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
        </div>

        <!-- Phone -->
        <div class="contact-item">
            <div class="contact-icon">
                 <i class="fas fa-phone"></i>
            </div>
            <div class="contact-details">
                <h3>Call Us</h3>
                <!-- Grouping phone numbers -->
                <div class="phone-numbers">
                    <div class="phone-group">
                        <span class="country">India:</span>
                        <p><a href="tel:+************">+91 ************</a></p>
                    </div>
                     <div class="phone-group">
                        <span class="country">USA:</span>
                        <p><a href="tel:+14156854332">****** 685 4332</a></p>
                    </div>
                     <div class="phone-group">
                        <span class="country">UK:</span>
                        <p><a href="tel:+************">+44 20 3289 4232</a></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address -->
        <div class="contact-item">
            <div class="contact-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <div class="contact-details">
                 <h3>Visit Us</h3>
                 <p>130, New Sanganer Rd, opp. Metro Station,<br />Shiva Colony, Sodala,<br />Jaipur, Rajasthan 302019</p>
            </div>
        </div>
    </div>
  </div>
</section>

<!-- Updated Footer Structure -->
<footer class="main-footer">
    <div class="container">
        <p>
            Data Analytics & Delivery Platform • All Rights Reserved • © {{ now.year if now else '2024' }} <!-- Updated year -->
        </p>
    </div>
</footer>

  <!-- Bootstrap JS Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Your custom JS -->
  <script src="{{ url_for('static', filename='js/common/landing.js') }}"></script>

</body>
</html>
