{% extends "base.html" %}

{% block title %}Login - DADP Data Annotation Platform{% endblock %}

{% block navbar %}
<!-- Override navbar with empty content -->
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/common/login.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/common/background.css') }}">
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap" rel="stylesheet">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
{% endblock %}


{% block content %}
<!-- Background elements -->
<div class="blob-1"></div>
<div class="blob-2"></div>
<div class="background-gradient"></div>
<div class="noise-texture"></div>
<div class="background-grid"></div>
<div class="animated-light-background"></div>
<div class="light-particles"></div>

<!-- Theme switch -->
<div class="theme-switch">
    <label class="switch">
        <input type="checkbox" id="themeToggle">
        <span class="slider">
            <div class="slider-icons">
                <i class="bi bi-sun-fill"></i>
                <i class="bi bi-moon-fill"></i>
            </div>
        </span>
    </label>
</div>

<div class="login-card shadow position-relative">
    <!-- Back arrow -->
    <a href="{{ url_for('index') }}" class="back-arrow">
        <i class="bi bi-arrow-left"></i>
    </a>
    <!-- Gradient indicator line at top -->
    <div class="card-indicator"></div>
    
    <div class="card-body p-4">
        <div class="text-center mb-4">
            <!-- 2-column layout with logo and title+quote -->
            <div class="header-container">
                <div class="logo-column">
                    <img src="{{ url_for('static', filename='img/PVlogo-1024x780.png') }}" alt="Logo" class="login-logo">
                </div>
                <div class="title-column">
                    <h2 class="login-title mb-0"><a href="{{ url_for('index') }}" class="title-link">DADP</a></h2>
                    <p class="mb-0"><em>"Place to Teach AI"</em></p>
                </div>
            </div>
            
            <!-- Sign In heading with underline -->
            <h3 class="signin-text">Sign In</h3>
            <div class="signin-underline"></div>
        </div>
        <form id="loginForm" method="post" action="{{ url_for('auth_routes.login') }}" novalidate>
            <!-- Email/Username input -->
            <div class="form-group">
                <label for="username" class="input-label">Username</label>
                <div class="input-field">
                    <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                </div>
                <div class="invalid-feedback">Please enter your username</div>
            </div>
            
            <!-- Password input -->
            <div class="form-group">
                <label for="password" class="input-label">Password</label>
                <div class="password-field">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    <span class="password-toggle" id="passwordToggle">
                        <i class="bi bi-eye"></i>
                    </span>
                </div>
                <div class="invalid-feedback">Please enter your password</div>
            </div>
            
            <!-- Login button with gradient -->
            <button type="submit" class="btn-login">
                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span class="btn-text">Login</span>
            </button>
            
            <!-- Flash message container -->
            <div id="loginFlashMessage" class="alert mt-3 text-center" style="display: none;">
                <span id="flashMessageText">Please hold on while we assign the task to you.</span>
            </div>
            
            <!-- Divider with OR text -->
            <div class="divider mt-4">
                <div class="divider-line"></div>
                <span class="divider-text">OR</span>
                <div class="divider-line"></div>
            </div>
            
            <!-- Register link -->
            <div class="text-center mt-4">
                <p class="mb-3">Don't have an account?</p>
                <a href="{{ url_for('auth_routes.user_register') }}" class="btn-login create-account-btn">
                    <span class="btn-text">Create New Account</span>
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    <script src="{{ url_for('static', filename='js/common/login.js') }}"></script>
{% endblock %} 