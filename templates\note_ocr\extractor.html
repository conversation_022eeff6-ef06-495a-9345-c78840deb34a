{% extends "base.html" %}

{% block title %}Extractor - Image OCR{% endblock %}

{% block navbar %}
<!-- Navbar is intentionally empty to hide it on this page -->
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/noteocr/extractor.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="extractor-container">
    <div class="back-button-container">
        <a href="/note-ocr" class="back-button">
            <i class="bi bi-house-fill" aria-hidden="true"></i> Back to Dashboard
        </a>
    </div>

    <div class="extractor-header">
        <h1>Image Extractor</h1>
        <p>Extract text from any image with powerful OCR technology</p>
    </div>

    <div class="extractor-content">
        <div class="upload-section">
            <div class="upload-card">
                <div class="card-header">
                    <h3>Upload Image</h3>
                </div>
                <div class="card-content">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="upload-area" id="dropZone">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-arrow-up"></i>
                            </div>
                            <p>Drag and drop your image here</p>
                            <p class="or-text">or</p>
                            <button type="button" class="browse-button" id="browseBtn" tabindex="0">
                                <i class="bi bi-folder"></i> Browse Files
                            </button>
                            <div class="upload-preview d-none">
                                <img id="previewImage" src="" alt="Preview">
                            </div>
                            <input type="file" id="imageUpload" name="image" accept="image/*" required class="hidden-input">
                        </div>
                        
                        <div class="preview-section">
                            <p>Image preview will appear here</p>
                        </div>
                        
                        <!-- Standard Extract Button (shown when no custom prompt) -->
                        <button type="button" class="extract-data-button d-none" id="extractBtn" disabled>
                            <i class="bi bi-magic"></i> Extract Data
                        </button>
                        
                        <!-- Processing button state (initially hidden) -->
                        <button type="button" class="processing-button d-none" id="processingBtn" disabled>
                            <div class="spinner-container">
                                <i class="bi bi-arrow-repeat spinning"></i>
                            </div>
                            Processing...
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="result-section">
            <div class="result-card">
                <div class="card-header">
                    <h3><i class="bi bi-file-text"></i> <span id="resultHeaderText">Results</span></h3>
                    <div class="action-buttons">
                        <button class="action-btn edit-btn" id="toggleEditBtn" disabled>
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                        <button class="action-btn download-btn" id="downloadBtn" disabled>
                            <i class="bi bi-download"></i> Download
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <!-- Mode Selection (moved here from upload card) -->
                    <div class="mode-selection-container">
                        <h4 class="mode-header">Choose Mode:</h4>
                        <div class="mode-buttons">
                            <!-- Custom Prompt Mode -->
                            <button type="button" class="mode-button" id="customPromptBtn" disabled>
                                <i class="bi bi-type-cursor-text"></i>
                                <span class="mode-button-text">Extract with System Prompt</span>
                            </button>
                            
                            <!-- Chat with Image Mode -->
                            <button type="button" class="mode-button" id="chatWithImageBtn" disabled>
                                <i class="bi bi-chat-dots"></i>
                                <span class="mode-button-text">Chat with Image</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Custom Prompt Input (initially hidden) -->
                    <div class="custom-prompt-input d-none" id="customPromptInput">
                        <label for="promptText">Enter custom extraction prompt:</label>
                        <textarea id="promptText" class="prompt-textarea" placeholder="Extract all text from this image carefully..."></textarea>
                        <button type="button" class="extract-data-button" id="extractWithPromptBtn">
                            <i class="bi bi-magic"></i> Extract with Custom Prompt
                        </button>
                    </div>
                    
                    <div class="processing-indicator d-none" id="processingIndicator">
                        <div class="spinner"></div>
                        <p>Processing your image...</p>
                    </div>
                    
                    <div id="resultContainer">
                        <div class="empty-result" id="emptyState">
                            <i class="bi bi-text-paragraph"></i>
                            <p>Choose a mode above to process your image</p>
                        </div>
                        
                        <!-- OCR Result Section -->
                        <div class="ocr-result d-none" id="ocrResult">
                            <div class="result-header">
                                <h4>Extracted Text</h4>
                                <button class="copy-btn" id="copyBtn">
                                    <i class="bi bi-clipboard"></i> Copy
                                </button>
                            </div>
                            <div class="result-text-area" id="resultText" contenteditable="false"></div>
                            
                            <div class="edit-controls d-none">
                                <button class="cancel-btn" id="cancelEditBtn">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </button>
                                <button class="save-btn" id="saveEditBtn">
                                    <i class="bi bi-check-circle"></i> Save
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Image Chat Section -->
                    <div class="chat-section d-none" id="chatSection">
                        <div class="chat-header">
                            <h4><i class="bi bi-chat-dots"></i> Chat with your image</h4>
                            <button class="toggle-chat-btn" id="toggleChatBtn">
                                <i class="bi bi-chevron-up"></i>
                            </button>
                        </div>
                        
                        <!-- <div class="chat-container">
                            <div class="chat-info-banner">
                                <i class="bi bi-info-circle"></i>
                                <span>The AI will analyze your image again for each question you ask.</span>
                            </div> -->
                            
                            <div class="chat-messages" id="chatMessages">
                                <div class="chat-message system-message">
                                    <div class="message-content">
                                        <p>You can ask questions about what you see in this image.<br>For example: "What text appears in this image?" or "Describe what you see in this image."</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="chat-input-container">
                                <input type="text" id="chatInput" class="chat-input" placeholder="Ask about this image...">
                                <button type="button" id="sendChatBtn" class="send-chat-btn">
                                    <i class="bi bi-send"></i>
                                </button>
                            </div>
                            
                            <div class="chat-processing d-none" id="chatProcessing">
                                <div class="chat-spinner"></div>
                                <span>Analyzing image and processing your question...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/noteocr/extractor.js') }}"></script>
{% endblock %} 