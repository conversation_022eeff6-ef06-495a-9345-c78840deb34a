<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NOTE-OCR - Document Extraction Service</title>
    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='images/img/PVlogo-1024x780.webp') }}" type="image/webp">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/noteocr/home.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/noteocr/features.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
</head>
<body>
    <div class="noise-texture"></div>
    <!-- Header Section -->
    <header class="main-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <a href="/" class="logo">
                        <img src="{{ url_for('static', filename='img/PVlogo-1024x780.png') }}" alt="NOTE-OCR Logo">
                    </a>
                    <div class="logo-text">
                        <span class="logo-line-1">End to End Data Solutions</span>
                        <span class="logo-line-2">Human-AI Collaboration</span>
                        <span class="beta-tag">Coming Soon</span>
                    </div>
                </div>

                <button class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>

                <nav class="main-nav">
                    <div class="nav-container">
                        <ul class="nav-links">
                            <li><a href="#features">Features</a></li>
                            <li><a href="/coming_soon">PDF Processing</a></li>
                            <li><a href="/extractor-mode">Image Extraction</a></li>
                            <li><a href="#features">How It Works</a></li>
                        </ul>
                    </div>
                    <a href="/" class="nav-cta">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Back to Home
                      </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">N<span class="smaller">OTE</span>OCR<br><span class="huai-agent">PDF</span> E<span class="smaller1">XTRACTION</span></span></h1>
                <div class="huai-tagline">
                    <span class="tagline-typing"></span><span class="tagline-cursor">|</span>
                </div>

                <div class="hero-main-content">
                    <div class="hero-description">
                        <div class="workflow-container">
                            <div class="process-cards">
                                <div class="process-card">
                                    <div class="card-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <div class="card-content">
                                        <h3>Element Extraction</h3>
                                        <p>Text, Tables, Images & Formulas</p>
                                    </div>
                                </div>
                                <div class="process-connector">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="process-card">
                                    <div class="card-icon">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="card-content">
                                        <h3>Human Validation</h3>
                                        <p>Intelligent Layout Detection</p>
                                    </div>
                                </div>
                                <div class="process-connector">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="process-card">
                                    <div class="card-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="card-content">
                                        <h3>Structured Output</h3>
                                        <p>Organized Data Files</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="typing-container">
                            <div class="typing-animation">
                                <span class="typing-message">Extract structured content from complex PDF documents.</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section" id="features">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-3"></div>
        </div>
        <div class="container">
            <h2 class="section-title">Features</h2>
            <div class="features-grid four-column">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="feature-title">Text Extraction</h3>
                    <p class="feature-desc">Accurately extract and organize text content from PDF documents with precise layout preservation.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-table"></i>
                    </div>
                    <h3 class="feature-title">Table Extraction</h3>
                    <p class="feature-desc">Identify and extract tables from PDFs with structure preservation, converting them to usable CSV format.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <h3 class="feature-title">Image Extraction</h3>
                    <p class="feature-desc">Extract embedded images from PDF documents while maintaining quality</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-superscript"></i>
                    </div>
                    <h3 class="feature-title">Formula Extraction</h3>
                    <p class="feature-desc">Identify and extract mathematical formulas from technical documents with high precision.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="info-section" id="how-it-works">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-2"></div>
            <div class="shape shape-4"></div>
        </div>
        <div class="container">
            <h2 class="section-title">How It Works</h2>
            <div class="steps-container">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>PDF Document Input</h3>
                        <p>Place PDF documents in the input directory for batch processing or submit individual files</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>Automated Processing</h3>
                        <p>The system analyzes document structure, detects elements, and applies specialized extraction for each element type</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>Structured Output</h3>
                        <p>Extracted content organized into dedicated folders for text, tables (CSV), images, and formulas with detailed timing reports</p>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Footer Section -->
    <footer class="main-footer">
        <div class="background-grid"></div>
        <div class="floating-shapes">
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
        </div>
        <div class="container">
            <p>NOTE-OCR • Document Extraction Service • All Rights Reserved • &copy; 2025 </p>
        </div>
    </footer>

    <script src="{{ url_for('static', filename='js/noteocr/home.js') }}"></script>
</body>
</html>