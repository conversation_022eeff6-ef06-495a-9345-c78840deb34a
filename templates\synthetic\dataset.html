<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Generate Dataset</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Bootstrap Icons -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />

  <!-- Custom Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap" rel="stylesheet">

  <!-- Custom CSS -->
  <link rel="stylesheet" href="{{ url_for('static', filename='css/synthetic/styles.css') }}">

</head>
<body>

<!-- Background effects -->
<div class="blob-1"></div>
<div class="blob-2"></div>
<div id="particles-js"></div>
<div class="background-gradient"></div>
<div class="noise-texture"></div>
<div class="background-grid"></div>

<div class="dataset-container">
    <div class="dataset-header">
        <h1>Generate AI Training Dataset</h1>
        <p>Create various types of training data from your documents for model training or evaluation</p>
            <!-- Back to Upload Button -->
    <div class="back-to-upload-container">
        <a href="/synthetic-upload" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i>
            Back to Upload
        </a>
    </div>
    </div>
    
    <div class="panels-container">
        <!-- Dataset Type Panel -->
        <div class="dataset-panel">
            <h2><i class="fas fa-database"></i> Dataset Type</h2>
            <p>Select the type of dataset you want to generate</p>
            
            <!-- Selected Dataset Type Display - Hiding this as requested -->
            <div id="selected-dataset-display" class="selected-dataset-container" style="display: none;">
                <div class="selected-dataset-icon" id="selected-dataset-icon"></div>
                <div class="selected-dataset-info">
                    <div class="selected-dataset-name" id="selected-dataset-name">Question-Answer Pairs</div>
                    <a href="#" id="change-dataset-link" class="change-dataset-link">Change dataset type</a>
                </div>
            </div>
            
            <!-- Dataset Type Options (Making always visible) -->
            <div class="dataset-type-options" id="dataset-type-options">
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-qa" value="qa" checked>
                    <label class="form-check-label" for="dataset-type-qa">
                        Question-Answer Pairs
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-entity-extraction" value="entity-extraction">
                    <label class="form-check-label" for="dataset-type-entity-extraction">
                        Entity Extraction
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-concept-definitions" value="concept-definitions">
                    <label class="form-check-label" for="dataset-type-concept-definitions">
                        Concept Definitions
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-summarization" value="summarization">
                    <label class="form-check-label" for="dataset-type-summarization">
                        Summarization
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-procedures" value="procedures">
                    <label class="form-check-label" for="dataset-type-procedures">
                        Procedures
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-comparisons" value="comparisons">
                    <label class="form-check-label" for="dataset-type-comparisons">
                        Comparisons
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-role-relationships" value="role-relationships">
                    <label class="form-check-label" for="dataset-type-role-relationships">
                        Role Relationships
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-code-explanations" value="code-explanations">
                    <label class="form-check-label" for="dataset-type-code-explanations">
                        Code Examples
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-fact-opinion" value="fact-opinion">
                    <label class="form-check-label" for="dataset-type-fact-opinion">
                        Fact vs Opinion
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-cause-effect" value="cause-effect">
                    <label class="form-check-label" for="dataset-type-cause-effect">
                        Cause-Effect Relationships
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-paraphrases" value="paraphrases">
                    <label class="form-check-label" for="dataset-type-paraphrases">
                        Paraphrases
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-intent-detection" value="intent-detection">
                    <label class="form-check-label" for="dataset-type-intent-detection">
                        Intent Detection
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="dataset-type" id="dataset-type-topic-classification" value="topic-classification">
                    <label class="form-check-label" for="dataset-type-topic-classification">
                        Topic Classification
                    </label>
                </div>
            </div>
        </div>
        
        <!-- Sources Panel -->
        <div id="sources-panel" class="dataset-panel">
            <h2><i class="fas fa-file-alt"></i> Sources</h2>
            <p>Documents and web pages that will be used to generate the dataset</p>
            
            <div class="sources-list-container" id="source-list">
                <div class="sources-list-header">
                    <div>Sources</div>
                    <div class="sources-actions">
                        <!-- Add source button -->
                        <button id="add-source-btn" class="add-source-btn">
                            <i class="fas fa-plus"></i> Add Source
                        </button>
                        <button id="select-all-sources" class="btn-sm btn-outline-primary">Select All</button>
                        <button id="deselect-all-sources" class="btn-sm btn-outline-secondary">Deselect All</button>
                        <div class="sources-count"><span id="source-count">0</span> sources (<span id="selected-source-count">0</span> selected)</div>
                    </div>
                </div>
                <div class="sources-list-body" id="selected-sources">
                    <!-- Sources will be added here dynamically -->
                    <div class="loading-sources">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading your sources...</span>
                    </div>
                </div>
            </div>
            
            <div id="no-sources-message" style="display: none;" class="no-sources-message">
                <i class="fas fa-file-upload"></i>
                <p>No sources found. Add sources to generate your dataset.</p>
                <button id="add-first-source-btn" class="add-source-btn">
                    <i class="fas fa-plus"></i> Add Source
                </button>
            </div>
        </div>
        
        <!-- Keywords Suggestions Panel -->
        <div class="dataset-panel">
            <h2><i class="fas fa-tags"></i> Suggested Topics</h2>
            <p>Click on keywords to add them as custom queries or topics</p>
            
            <div class="keywords-container">
                <div class="keywords-list" id="extracted-keywords">
                    <!-- Keywords will be loaded here -->
                    <div class="loading-keywords">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Extracting keywords from sources...</span>
                    </div>
                </div>
                <div id="no-keywords-message" style="display: none;" class="info-message">
                    <i class="fas fa-info-circle"></i> No keywords found. Select sources to extract keywords.
                </div>
            </div>
        </div>
        
        <!-- Model Selection Panel -->
        <div class="dataset-panel">
            <h2><i class="fas fa-robot"></i> Select AI Model</h2>
            <p>Choose which AI model will generate your question-answer pairs</p>
            
            <div class="model-options">
                <div class="model-option" data-model="ollama" data-needs-key="false">
                    <div class="model-header">
                        <div class="model-icon">
                            <img src="{{ url_for('static', filename='img/ollama.png') }}" alt="Ollama" class="model-logo">
                        </div>
                        <div class="model-name">Ollama</div>
                    </div>
                    <div class="model-description">
                        Local open-source model
                    </div>
                </div>
                <div class="model-option" data-model="gemini" data-needs-key="true">
                    <div class="model-header">
                        <div class="model-icon">
                            <img src="{{ url_for('static', filename='img/gemini.png') }}" alt="Gemini" class="model-logo">
                        </div>
                        <div class="model-name">Gemini</div>
                    </div>
                    <div class="model-description">
                        Google's multimodal AI
                    </div>
                </div>
                
                <div class="model-option disabled" data-model="gpt" data-needs-key="true">
                    <div class="model-header">
                        <div class="model-icon">
                            <img src="{{ url_for('static', filename='img/openai.png') }}" alt="OpenAI" class="model-logo">
                        </div>
                        <div class="model-name">GPT</div>
                    </div>
                    <div class="model-description">
                        OpenAI's advanced model
                    </div>
                    <span class="coming-soon-badge">Coming Soon</span>
                </div>
                
                <div class="model-option disabled" data-model="claude" data-needs-key="true">
                    <div class="model-header">
                        <div class="model-icon">
                            <img src="{{ url_for('static', filename='img/claude.png') }}" alt="Claude" class="model-logo">
                        </div>
                        <div class="model-name">Claude</div>
                    </div>
                    <div class="model-description">
                        Anthropic's assitant AI     
                    </div>
                    <span class="coming-soon-badge">Coming Soon</span>
                </div>
                

            </div>
            
            <div id="api-key-container" class="api-key-container hidden">
                <div class="form-group">
                    <div class="api-key-options">
                        <div class="form-check">
                            <input type="radio" id="use-default-key" name="api-key-option" class="form-check-input" value="default" checked>
                            <label for="use-default-key" class="form-check-label">Use default API key</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="use-custom-key" name="api-key-option" class="form-check-input" value="custom">
                            <label for="use-custom-key" class="form-check-label">Use custom API key</label>
                        </div>
                    </div>
                    <div id="api-key-notification" class="api-key-notification my-2 hidden"></div>
                    <div id="custom-key-input" class="mt-2 hidden">
                        <label for="api-key" class="form-label">API Key</label>
                        <input type="password" id="api-key" class="form-control" placeholder="Enter your API key">
                    </div>
                    <button id="submit-api-key" class="btn btn-primary mt-2">Submit</button>
                </div>
            </div>
        </div>
        
        <!-- Configuration Panel -->
        <div class="dataset-panel">
            <h2><i class="fas fa-cog"></i> Configure Generation Settings</h2>
            <p>Customize how your QA dataset will be generated</p>
            
            <div class="dataset-options">
                <div class="option-group">
                    <label class="form-label">Number of QA Pairs</label>
                    <div class="input-group">
                        <input type="number" id="qa-count" class="form-control" value="10" min="1" max="100">
                    </div>
                    <p class="small-text">How many question-answer pairs to generate per source</p>
                </div>
                
                <div class="option-group">
                    <label class="form-label">Difficulty Level</label>
                    <div class="select-wrapper">
                        <select id="difficulty" class="form-select">
                            <option value="basic">Basic (Factual)</option>
                            <option value="intermediate" selected>Intermediate (Analytical)</option>
                            <option value="advanced">Advanced (Inferential)</option>
                        </select>
                    </div>
                    <p class="small-text">Determines complexity of generated questions</p>
                </div>
            </div>
        </div>
        
        <!-- Custom Query Panel -->
        <div class="dataset-panel">
            <h2><i class="fas fa-question-circle"></i> Custom Queries</h2>
            <p>Add your own specific questions to generate answers for</p>
            
            <div class="custom-query-container">
                <div class="custom-query-list" id="custom-queries">
                    <!-- Custom queries will be added here -->
                </div>
                
                <div class="form-group">
                    <label class="form-label">Add Custom Query</label>
                    <div class="query-input-group">
                        <textarea id="query-input" class="form-control" placeholder="Enter your question here..."></textarea>
                        <button id="add-query" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Add Query
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="custom-query-actions">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="include-auto-queries" checked>
                    <label class="form-check-label" for="include-auto-queries">
                        Include auto-generated queries
                    </label>
                </div>
            </div>
        </div>
    </div>

    <div class="generate-btn-container">
        <button id="generate-dataset" class="btn btn-primary btn-lg" disabled>
            <i class="fas fa-database"></i>
            Generate Dataset
        </button>
    </div>
    
    <!-- Preview Panel -->
    <div class="dataset-panel" id="preview-panel" style="display: none;">
        <h2><i class="fas fa-eye"></i> Dataset Preview</h2>
        <p>Review generated dataset before exporting</p>
        
        <div class="dataset-preview">
            <div class="dataset-preview-header">
                <span><strong id="qa-count-display">0</strong> pairs generated</span>
                <div class="preview-controls">
                    <button id="refresh-preview" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-sync-alt"></i>
                        Regenerate
                    </button>
                </div>
            </div>
            <div class="dataset-preview-body" id="qa-preview">
                <div class="loading-indicator" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Generating Dataset...</span>
                </div>
                <!-- QA pairs will be added here dynamically -->
            </div>
        </div>
        
        <div class="export-options">
            <button id="export-json" class="btn-export">
                <i class="fas fa-file-code"></i>
                Export JSON
            </button>
            <button id="export-csv" class="btn-export">
                <i class="fas fa-file-csv"></i>
                Export CSV
            </button>
            <button id="export-txt" class="btn-export">
                <i class="fas fa-file-alt"></i>
                Export TXT
            </button>
        </div>
    </div>
    

    
    <!-- Add Source Modal -->
    <div id="source-modal-backdrop" class="source-modal-backdrop">
        <div class="source-modal">
            <div class="source-modal-header">
                <h3><i class="fas fa-file-upload"></i> Add Source</h3>
                <button class="source-modal-close" id="close-source-modal">&times;</button>
            </div>
            <div class="source-modal-body">
                <div class="source-modal-tabs">
                    <div class="source-modal-tab active" data-tab="upload">File Upload</div>
                    <div class="source-modal-tab" data-tab="url">Web URL</div>
                    <div class="source-modal-tab" data-tab="text">Paste Text</div>
                </div>
                
                <div class="upload-area" id="upload-area">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Drag and drop your files here or <span class="browse">browse</span></p>
                    <span class="small-text">Supported formats: PDF, DOCX, TXT, MD</span>
                    <input type="file" id="file-input" accept=".pdf,.docx,.txt,.md" multiple>
                </div>
                
                <div class="url-area" id="url-area" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">Add webpage URL</label>
                        <div class="url-input-group">
                            <input type="text" id="url-input" class="form-control" placeholder="https://example.com/article">
                        </div>
                        <p class="small-text">Enter a URL to extract content from a webpage</p>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Source Name (Optional)</label>
                        <input type="text" id="url-source-name" class="form-control" placeholder="Enter a name for this URL source">
                        <p class="small-text">If left empty, the URL itself will be used as the source name</p>
                    </div>
                </div>
                
                <div class="text-area" id="text-area" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">Paste your text content</label>
                        <textarea id="text-content" class="form-control" rows="10" placeholder="Paste or type your text content here..."></textarea>
                        <div class="text-options mt-3">
                            <div class="form-group">
                                <label class="form-label">Source Name</label>
                                <input type="text" id="text-source-name" class="form-control" placeholder="Enter a name for this text source">
                                <p class="small-text">This will be used to identify your source in the list</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="source-modal-footer">
                <button id="cancel-source" class="btn btn-outline-secondary">Cancel</button>
                <button id="upload-source" class="btn btn-primary">Upload</button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="main-footer">
    <div class="background-grid"></div>
    <div class="floating-shapes">
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
    </div>
    <div class="container">
        <p>Data Annotation & delivery Platform • All Rights Reserved • © 2025</p>
    </div>
</footer>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JS -->
<script src="{{ url_for('static', filename='js/synthetic/main.js') }}"></script>
<script src="{{ url_for('static', filename='js/synthetic/dataset.js') }}"></script>

</body>
</html> 