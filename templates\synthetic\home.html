<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Synthetic Dataset Generation & Model Distillation</title>
  
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
  <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='img/PVlogo-favicon.png') }}">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="static/css/synthetic/home.css">
</head>
<body>
  <!-- Main Background -->
  <div class="main-background"></div>
  
  <!-- Navigation -->
  <header>
    <div class="header-content">
      <div class="logo">
        <a class="logo-link" href="/">
        <img src="/static/img/PVlogo-1024x780.png" alt="logo">
        </a>
        <div class="logo-text">
          <span class="logo-title">End to End Synthetic Dataset Solutions</span>
          <span class="logo-subtitle">Human-AI Collaboration</span>
          <span class="beta-label">BETA VERSION</span>
        </div>
      </div>
      <div class="nav-links">
        <ul>
          <li><a href="#top">Home</a></li>
          <li><a href="#features">Features</a></li>
          <li><a href="#how-it-works">How It Works</a></li>
          <li><a href="#dataset-types">Datasets</a></li>
          <li><a href="#model-distillation">Distillation</a></li>
          <li><a href="#use-cases">Use Cases</a></li>
        </ul>
      </div>
      <div class="button-area">
        <a href="/" class="back-button"><i class="fas fa-home"></i> Back to DADP</a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <div id="top" class="hero-container">
    <div class="container">
      <div class="hero-content-wrapper">
        <div class="hero-content animate-fade-in">
          <h1 class="main-title">SynGround</h1>
          <p class="hero-subtitle">Accelerate model training with high-quality synthetic data and efficient knowledge transfer</p>
          
          <!-- Adding animated text content -->
          <div class="typing-content">
            <div class="typing-text">Generate synthetic datasets from any source</div>
            <div class="typing-text">Train and optimize AI models efficiently</div>
            <div class="typing-text">Transfer knowledge between models seamlessly</div>
          </div>
          
          <div class="hero-buttons">
            <a href="/login" class="action-btn get-started-btn">
              <i class="fas fa-rocket"></i>
              Get Started
            </a>
          </div>

          <div class="scroll-indicator">
            <a href="#features">
              <i class="fas fa-chevron-down"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
   
  <!-- Features Section -->
  <div id="features" class="section">
    <div class="container">
      <h2 class="section-title">Dataset Generation Features</h2>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <h3>Source Analysis</h3>
          <p>Upload PDFs, text files, and other documents like web page urls to generate comprehensive datasets</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="card-icon">
            <i class="fas fa-cogs"></i>
          </div>
          <h3>Customizable Settings</h3>
          <p>Configure number of QA pairs, difficulty levels, and query (optional)</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.4s;">
          <div class="card-icon">
            <i class="fas fa-download"></i>
          </div>
          <h3>Export Options</h3>
          <p>Download your generated datasets in multiple formats for training or evaluation</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.5s;">
          <div class="card-icon">
            <i class="fas fa-compress-arrows-alt"></i>
          </div>
          <h3>Model Distillation</h3>
          <p>Create compact datasets for transferring knowledge from large to smaller models</p>
        </div>
      </div>
    </div>
  </div>

  <!-- How It Works Section -->
  <div id="how-it-works" class="section">
    <div class="container">
      <h2 class="section-title">How Dataset Generation Works</h2>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="step-number">1</div>
          <h3>Select Dataset Types</h3>
          <p>Choose from multiple dataset types based on your specific needs</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.2s;">
          <div class="step-number">2</div>
          <h3>Upload Sources</h3>
          <p>Add documents or web pages to generate datasets from</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="step-number">3</div>
          <h3>Add Keywords (Optional)</h3>
          <p>Provide specific keywords as queries to focus the dataset generation</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.4s;">
          <div class="step-number">4</div>
          <h3>Select AI Model</h3>
          <p>Choose from OpenAI, Claude, Gemini, or Ollama based on your requirements</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.5s;">
          <div class="step-number">5</div>
          <h3>Configure Settings</h3>
          <p>Set the number of items to generate and difficulty level</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.6s;">
          <div class="step-number">6</div>
          <h3>Generate Dataset</h3>
          <p>Create your custom dataset using the selected parameters</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.7s;">
          <div class="step-number">7</div>
          <h3>Download Results</h3>
          <p>Export your dataset in various formats including JSON, CSV, and TXT</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Models Available Section -->
  <div id="models" class="section">
    <div class="container">
      <h2 class="section-title">Models Available</h2>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon">
            <i class="fas fa-robot"></i>
          </div>
          <h3>OpenAI</h3>
          <p>Advanced GPT models for generating high-quality, contextual question-answer pairs</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.2s;">
          <div class="card-icon">
            <i class="fas fa-microchip"></i>
          </div>
          <h3>Claude</h3>
          <p>Specialized in detailed and nuanced responses for complex domain knowledge</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="card-icon">
            <i class="fas fa-brain"></i>
          </div>
          <h3>Gemini</h3>
          <p>Google's multimodal model with strong reasoning and content generation capabilities</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.4s;">
          <div class="card-icon">
            <i class="fas fa-server"></i>
          </div>
          <h3>Deepseek r1</h3>
          <p>Run various open-source models locally using Ollama with customizable parameters</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Types of Datasets Section -->
  <div id="dataset-types" class="section">
    <div class="container">
      <h2 class="section-title">Types of Datasets</h2>
      <div class="card-grid dataset-grid">
        <div class="card small-card animate-fade-in" style="animation-delay: 0.05s;">
          <div class="card-icon small-icon">
            <i class="fas fa-question-circle"></i>
          </div>
          <h3>Question-Answer Pairs</h3>
          <p>Standard format with questions and corresponding correct answers</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon small-icon">
            <i class="fas fa-tag"></i>
          </div>
          <h3>Entity Extraction</h3>
          <p>Identify and extract named entities, terms, and key information points</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.15s;">
          <div class="card-icon small-icon">
            <i class="fas fa-book"></i>
          </div>
          <h3>Concept Definitions</h3>
          <p>Explanations and definitions of key terms and concepts</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.2s;">
          <div class="card-icon small-icon">
            <i class="fas fa-compress-alt"></i>
          </div>
          <h3>Summarization</h3>
          <p>Condensed versions of longer texts capturing key information</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.25s;">
          <div class="card-icon small-icon">
            <i class="fas fa-list-ol"></i>
          </div>
          <h3>Procedures</h3>
          <p>Step-by-step instructions for completing specific tasks</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="card-icon small-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h3>Comparisons</h3>
          <p>Contrasting different items, concepts, or approaches</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.35s;">
          <div class="card-icon small-icon">
            <i class="fas fa-sitemap"></i>
          </div>
          <h3>Role Relationships</h3>
          <p>Connections and interactions between different entities</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.4s;">
          <div class="card-icon small-icon">
            <i class="fas fa-code"></i>
          </div>
          <h3>Code Examples</h3>
          <p>Examples of code snippets based on the topic from the source</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.45s;">
          <div class="card-icon small-icon">
            <i class="fas fa-gavel"></i>
          </div>
          <h3>Fact vs Opinion</h3>
          <p>Classification of statements as factual or subjective</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.5s;">
          <div class="card-icon small-icon">
            <i class="fas fa-arrow-right"></i>
          </div>
          <h3>Cause-Effect Relationships</h3>
          <p>Identifying connections between causes and their results</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.55s;">
          <div class="card-icon small-icon">
            <i class="fas fa-retweet"></i>
          </div>
          <h3>Paraphrases</h3>
          <p>Alternative ways to express the same information</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.6s;">
          <div class="card-icon small-icon">
            <i class="fas fa-bullseye"></i>
          </div>
          <h3>Intent Detection</h3>
          <p>Identifying the purpose or goal behind a piece of text</p>
        </div>
        
        <div class="card small-card animate-fade-in" style="animation-delay: 0.65s;">
          <div class="card-icon small-icon">
            <i class="fas fa-folder"></i>
          </div>
          <h3>Topic Classification</h3>
          <p>Categorizing content according to subject matter</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Model Distillation Section -->
  <div id="model-distillation" class="section">
    <div class="container">
      <h2 class="section-title">Model Distillation</h2>
      
      <div class="section-intro">
        <p>Model Distillation is a compression technique where a large Teacher Model transfers its knowledge to a smaller Student Model while keeping performance high. The goal is to make AI models smaller, faster, and more efficient for real-world applications.</p>
      </div>
      
      <h3 class="subsection-title">Types of Model Distillation</h3>
      
      <h4 class="distillation-category">When no real data available:</h4>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon">
            <i class="fas fa-magic"></i>
          </div>
          <h3>Data-Free Knowledge Distillation</h3>
          <p>Transfer knowledge from a teacher model to a student model without relying on original training data by generating synthetic data to guide the student's learning</p>
        </div>
      </div>
      
      <h4 class="distillation-category">When real data is available:</h4>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon">
            <i class="fas fa-share-alt"></i>
          </div>
          <h3>Response Based Knowledge Distillation</h3>
          <p>Transfers information from the final output layer of the teacher model by training the student model to output logits that match the teacher model's predictions</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.2s;">
          <div class="card-icon">
            <i class="fas fa-layer-group"></i>
          </div>
          <h3>Feature Based Knowledge Distillation</h3>
          <p>Focuses on information conveyed in intermediate layers where neural networks perform feature extraction, training the student to learn the same features as the teacher</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="card-icon">
            <i class="fas fa-project-diagram"></i>
          </div>
          <h3>Relation Based Knowledge Distillation</h3>
          <p>Focuses on the relationships between different layers or between feature maps representing the activations at different layers or locations</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Use Cases Section -->
  <div id="use-cases" class="section">
    <div class="container">
      <h2 class="section-title">Dataset Use Cases</h2>
      <div class="card-grid">
        <div class="card animate-fade-in" style="animation-delay: 0.1s;">
          <div class="card-icon">
            <i class="fas fa-brain"></i>
          </div>
          <h3>AI Model Training</h3>
          <p>Create training datasets to improve AI model performance on domain-specific knowledge</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.2s;">
          <div class="card-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Performance Evaluation</h3>
          <p>Generate test sets to evaluate how well AI models understand your content</p>
        </div>
        
        <div class="card animate-fade-in" style="animation-delay: 0.3s;">
          <div class="card-icon">
            <i class="fas fa-book"></i>
          </div>
          <h3>Educational Resources</h3>
          <p>Create Q&A materials for educational purposes or knowledge assessment</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="main-footer">
    <div class="container">
      <p>&copy; 2025 Process Venue. All rights reserved.</p>
    </div>
  </footer>

  <!-- Back to Top Button -->
  <a href="#top" id="back-to-top" class="back-to-top">
    <i class="fas fa-arrow-up"></i>
  </a>

  <!-- Custom JavaScript -->
  <script src="static/js/synthetic/home.js"></script>
</body>
</html>