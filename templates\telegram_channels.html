{% extends "admin/admin_base.html" %}

{% block title %}Telegram Channels - Documind-O{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin/telegram_channels.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="noise-texture"></div>
<div class="background-grid"></div>
<div class="floating-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
</div>

<div class="content-wrapper">
<div class="container-fluid p-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Connect to Telegram</h1>
        <div>
            <a href="{{ url_for('admin_routes.fetch_data_route') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Back to Data Sources
            </a>
        </div>
    </div>

    <!-- Connection Form -->
    <div id="connection-form" class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">Connect to Telegram</h5>
            <div id="session-status" class="alert alert-info mb-3" style="display: none;">
                Checking for existing session...
            </div>
            <form id="telegram-form">
                <div class="mb-3">
                    <label for="api_id" class="form-label">API ID</label>
                    <input type="text" class="form-control" id="api_id" required>
                </div>
                <div class="mb-3">
                    <label for="api_hash" class="form-label">API Hash</label>
                    <input type="text" class="form-control" id="api_hash" required>
                </div>
                <div class="mb-3">
                    <label for="phone" class="form-label">Phone Number</label>
                    <input type="text" class="form-control" id="phone" required>
                </div>
                <div class="d-flex justify-content-between">
                    <button type="submit" class="btn btn-primary">Connect</button>
                    <button type="button" id="reset-session-btn" class="btn btn-outline-danger">Reset Session</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Verification Code Form -->
    <div id="verification-form" class="card mb-4" style="display: none;">
        <div class="card-body">
            <h5 class="card-title">Enter Verification Code</h5>
            <form id="code-form">
                <div class="mb-3">
                    <label for="code" class="form-label">Code</label>
                    <input type="text" class="form-control" id="code" required>
                </div>
                <button type="submit" class="btn btn-primary">Verify Code</button>
            </form>
        </div>
    </div>

    <!-- 2FA Password Form -->
    <div id="password-form" class="card mb-4" style="display: none;">
        <div class="card-body">
            <h5 class="card-title">Enter 2FA Password</h5>
            <form id="password-form-inputs">
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-primary">Verify Password</button>
            </form>
        </div>
    </div>

    <!-- No back button needed since we're using the admin sidebar navigation -->
</div>
</div>

<!-- Status Message -->
<div id="status-message" class="alert status-message" style="display: none;"></div>

<!-- Loading Spinner -->
<div id="loading-spinner" class="position-fixed top-50 start-50 translate-middle" style="display: none; z-index: 9999;">
    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin/telegram_channels.js') }}"></script>
{% endblock %}