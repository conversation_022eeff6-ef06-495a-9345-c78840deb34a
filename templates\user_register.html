{% extends "base.html" %}

{% block title %}Create New Account{% endblock %}

{% block body_class %}auth-page{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common/user_register.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/common/background.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto:wght@400;500;700&family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
{% endblock %}

{% block content %}
<!-- Background elements -->
<div class="blob-1"></div>
<div class="blob-2"></div>
<div class="background-gradient"></div>
<div class="noise-texture"></div>
<div class="background-grid"></div>
<div class="animated-light-background"></div>
<div class="light-particles"></div>

<!-- Theme switch -->
<!--<div class="theme-switch">
    <label class="switch">
        <input type="checkbox" id="themeToggle">
        <span class="slider">
            <div class="slider-icons">
                <i class="bi bi-sun-fill"></i>
                <i class="bi bi-moon-fill"></i>
            </div>
        </span>
    </label>
</div>-->

<div class="auth-card shadow">

    <div class="auth-header">
        <h1>Create New Account</h1>
    </div>

    <div class="auth-body">
        <form method="post" action="{{ url_for('auth_routes.user_register') }}" id="registerForm" novalidate>
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="Username" required pattern="[a-zA-Z0-9_]+">
                <label for="username">Username</label>
                <div class="form-text">Letters, numbers, and underscores only</div>
                <div class="invalid-feedback">Username can only contain letters, numbers, and underscores</div>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="Password" required minlength="6">
                <label for="password">Password</label>
                <div class="form-text">Must be at least 6 characters long</div>
                <div class="invalid-feedback">Password must be at least 6 characters long</div>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                <label for="confirm_password">Confirm Password</label>
                <div class="invalid-feedback">Passwords do not match</div>
            </div>

            <div class="form-floating">
                <input type="text" class="form-control" id="full_name" name="full_name" placeholder="Full Name">
                <label for="full_name">Full Name</label>
            </div>

            <div class="form-floating">
                <input type="email" class="form-control" id="email" name="email" placeholder="Email">
                <label for="email">Email</label>
                <div class="invalid-feedback">Please enter a valid email address</div>
            </div>

            <div class="mb-3">
                <label for="role" class="form-label">Role</label>
                <select class="form-select" id="role" name="role">
                    <option value="annotator" selected>Annotator</option>
                    <option value="auditor">Auditor</option>

                </select>
            </div>

            <div class="row mt-4">
                <div class="col-6 d-flex align-items-center">
                    <a href="{{ url_for('auth_routes.login') }}" class="btn btn-outline-secondary w-100">Back to Login</a>
                </div>
                <div class="col-6 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary w-100">Register</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Gradient indicator line at bottom -->
    <div class="card-indicator bottom"></div>
</div>
{% endblock %}

{% block extra_js %}
    <script src="{{ url_for('static', filename='js/common/user_register.js') }}"></script>
{% endblock %}
